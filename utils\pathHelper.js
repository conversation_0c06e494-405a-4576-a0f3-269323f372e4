/**
 * Utility functions for handling file paths across different platforms
 */

/**
 * Sanitizes a Windows file path for safe storage and display
 * 
 * @param {string} filePath - The original Windows file path
 * @returns {string} The sanitized file path
 */
function sanitizeFilePath(filePath) {
  if (!filePath) return '';
  
  // Replace backslashes with forward slashes
  let sanitized = filePath.replace(/\\/g, '/');
  
  // Encode special characters
  sanitized = encodeURIComponent(sanitized);
  
  return sanitized;
}

/**
 * Converts a sanitized file path back to its original format
 * 
 * @param {string} sanitizedPath - The sanitized file path
 * @returns {string} The original file path
 */
function desanitizeFilePath(sanitizedPath) {
  if (!sanitizedPath) return '';
  
  // Decode special characters
  let original = decodeURIComponent(sanitizedPath);
  
  // Convert to appropriate path format based on platform
  if (process.platform === 'win32') {
    original = original.replace(/\//g, '\\');
  }
  
  return original;
}

module.exports = {
  sanitizeFilePath,
  desanitizeFilePath
};
