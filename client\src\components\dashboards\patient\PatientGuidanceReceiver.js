import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Modal,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ROLE_COLORS } from '../../../config/theme';
import { db, auth } from '../../../config/firebase';
import { collection, query, where, onSnapshot, orderBy, limit } from 'firebase/firestore';

const PatientGuidanceReceiver = () => {
  const navigation = useNavigation();
  const patientColors = ROLE_COLORS.patient;

  // State variables
  const [loading, setLoading] = useState(true);
  const [guidanceList, setGuidanceList] = useState([]);
  const [selectedGuidance, setSelectedGuidance] = useState(null);
  const [showGuidanceModal, setShowGuidanceModal] = useState(false);

  useEffect(() => {
    // Listen for guidance updates when component mounts
    const unsubscribe = listenForGuidanceUpdates();

    // Clean up listener when component unmounts
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  const listenForGuidanceUpdates = () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        setLoading(false);
        return null;
      }

      const guidanceCollection = collection(db, 'patientGuidance');
      const q = query(
        guidanceCollection,
        where('patientId', '==', currentUser.uid),
        where('status', 'in', ['sent', 'active', 'completed']),
        orderBy('createdAt', 'desc'),
        limit(10)
      );

      // Set up real-time listener
      return onSnapshot(q, (snapshot) => {
        const guidanceData = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          guidanceData.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate?.() || new Date()
          });
        });

        setGuidanceList(guidanceData);
        setLoading(false);
      }, (error) => {
        console.error('Error listening to guidance updates:', error);
        setLoading(false);
      });
    } catch (error) {
      console.error('Error setting up guidance listener:', error);
      setLoading(false);
      return null;
    }
  };

  const handleViewGuidance = (guidance) => {
    setSelectedGuidance(guidance);
    setShowGuidanceModal(true);
  };

  const handleStartNavigation = (guidance) => {
    // Navigate to the map screen with guidance data
    navigation.navigate('MapScreen', {
      guidanceData: guidance,
      mode: 'navigation'
    });

    // Close the modal if it's open
    setShowGuidanceModal(false);
  };

  const renderGuidanceItem = ({ item }) => {
    const formattedDate = new Date(item.createdAt).toLocaleDateString();
    const formattedTime = new Date(item.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    return (
      <TouchableOpacity
        style={styles.guidanceItem}
        onPress={() => handleViewGuidance(item)}
      >
        <View style={styles.guidanceIconContainer}>
          <Ionicons
            name={item.routeType === 'walking' ? 'walk' : item.routeType === 'driving' ? 'car' : 'bus'}
            size={24}
            color="#fff"
          />
        </View>

        <View style={styles.guidanceContent}>
          <Text style={styles.guidanceDestination} numberOfLines={1}>
            {item.destination}
          </Text>
          <Text style={styles.guidanceSupervisor}>
            From: {item.supervisorName}
          </Text>
          <Text style={styles.guidanceTime}>
            {formattedDate} at {formattedTime}
          </Text>
        </View>

        <Ionicons name="chevron-forward" size={20} color="#999" />
      </TouchableOpacity>
    );
  };

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="navigate-circle-outline" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No guidance received</Text>
      <Text style={styles.emptySubtext}>
        When your supervisor sends you guidance, it will appear here
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={patientColors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Navigation Guidance</Text>
      </View>

      <FlatList
        data={guidanceList}
        renderItem={renderGuidanceItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={guidanceList.length === 0 ? { flex: 1 } : { padding: 16 }}
        ListEmptyComponent={renderEmptyList}
      />

      {/* Guidance Detail Modal */}
      <Modal
        visible={showGuidanceModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowGuidanceModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Guidance Details</Text>
              <TouchableOpacity
                onPress={() => setShowGuidanceModal(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {selectedGuidance && (
              <View style={styles.guidanceDetails}>
                <View style={styles.guidanceDetailRow}>
                  <Text style={styles.guidanceDetailLabel}>From:</Text>
                  <Text style={styles.guidanceDetailValue}>{selectedGuidance.supervisorName}</Text>
                </View>

                <View style={styles.guidanceDetailRow}>
                  <Text style={styles.guidanceDetailLabel}>Destination:</Text>
                  <Text style={styles.guidanceDetailValue}>{selectedGuidance.destination}</Text>
                </View>

                <View style={styles.guidanceDetailRow}>
                  <Text style={styles.guidanceDetailLabel}>Mode:</Text>
                  <View style={styles.routeTypeContainer}>
                    <Ionicons
                      name={
                        selectedGuidance.routeType === 'walking' ? 'walk' :
                        selectedGuidance.routeType === 'driving' ? 'car' : 'bus'
                      }
                      size={20}
                      color={patientColors.primary}
                    />
                    <Text style={styles.routeTypeText}>
                      {selectedGuidance.routeType.charAt(0).toUpperCase() + selectedGuidance.routeType.slice(1)}
                    </Text>
                  </View>
                </View>

                {/* Route Information */}
                {selectedGuidance.routeDistance && selectedGuidance.routeDuration && (
                  <View style={styles.routeInfoContainer}>
                    <View style={styles.routeInfoItem}>
                      <Ionicons name="speedometer-outline" size={20} color={patientColors.primary} />
                      <Text style={styles.routeInfoText}>
                        Distance: {selectedGuidance.routeDistance.toFixed(2)} km
                      </Text>
                    </View>
                    <View style={styles.routeInfoItem}>
                      <Ionicons name="time-outline" size={20} color={patientColors.primary} />
                      <Text style={styles.routeInfoText}>
                        Duration: {selectedGuidance.routeDuration > 60
                          ? `${Math.floor(selectedGuidance.routeDuration / 60)}h ${selectedGuidance.routeDuration % 60}min`
                          : `${selectedGuidance.routeDuration} min`}
                      </Text>
                    </View>
                  </View>
                )}

                <View style={styles.guidanceDetailRow}>
                  <Text style={styles.guidanceDetailLabel}>Sent:</Text>
                  <Text style={styles.guidanceDetailValue}>
                    {new Date(selectedGuidance.createdAt).toLocaleString()}
                  </Text>
                </View>

                {selectedGuidance.instructions && (
                  <View style={styles.instructionsContainer}>
                    <Text style={styles.instructionsLabel}>Instructions:</Text>
                    <Text style={styles.instructionsText}>{selectedGuidance.instructions}</Text>
                  </View>
                )}

                <TouchableOpacity
                  style={styles.startNavigationButton}
                  onPress={() => handleStartNavigation(selectedGuidance)}
                >
                  <Ionicons name="navigate" size={20} color="#fff" />
                  <Text style={styles.startNavigationText}>Start Navigation</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  guidanceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  guidanceIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ROLE_COLORS.patient.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  guidanceContent: {
    flex: 1,
  },
  guidanceDestination: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  guidanceSupervisor: {
    fontSize: 14,
    color: '#666',
  },
  guidanceTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  guidanceDetails: {
    marginBottom: 16,
  },
  guidanceDetailRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  guidanceDetailLabel: {
    width: 100,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  guidanceDetailValue: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  routeTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeTypeText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
  },
  routeInfoContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  routeInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  routeInfoText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  instructionsContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  instructionsLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
  },
  startNavigationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ROLE_COLORS.patient.primary,
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  startNavigationText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default PatientGuidanceReceiver;
