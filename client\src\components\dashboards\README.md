# NeuroCare Dashboard Components

This directory contains the dashboard components for the NeuroCare healthcare application, providing role-specific interfaces for patients, doctors, nurses, administrators, and pharmacists.

## Base Components

### DashboardLayout

A reusable layout component that provides a consistent structure for all dashboards, including:
- Header with user profile access
- Slide-out navigation menu
- Content area

```jsx
<DashboardLayout 
  title="Dashboard Title" 
  roleName="Role Name"
  menuItems={menuItemsArray}
  headerBackgroundColor="#colorCode"
  accentColor="#colorCode"
>
  {/* Dashboard content */}
</DashboardLayout>
```

### DashboardCard

A card component used to display key statistics and metrics.

```jsx
<DashboardCard
  title="Card Title"
  value="42"
  icon="icon-name"
  iconColor="#colorCode"
  width="48%"
  unit="units"
  trend="up"
  trendValue="+5%"
  onPress={() => handlePress()}
/>
```

### DashboardChart

A component for data visualization using various chart types.

```jsx
<DashboardChart
  title="Chart Title"
  subtitle="Subtitle"
  data={chartData}
  type="line" // or "bar", "pie"
  height={200}
  width={width => width * 0.48}
/>
```

### UpcomingList

A component that displays lists of upcoming items like appointments, tasks, etc.

```jsx
<UpcomingList
  title="List Title"
  data={listData}
  onItemPress={(item) => handleItemPress(item)}
  onViewAll={() => handleViewAll()}
  emptyText="No items to display"
/>
```

## Role-Specific Dashboards

The application includes five role-specific dashboards, each tailored to the needs of different healthcare roles:

1. **PatientDashboard**: Provides patients with access to their health metrics, appointments, and medications.

2. **DoctorDashboard**: Helps doctors manage their patient appointments, review pending tasks, and monitor patient distribution.

3. **NurseDashboard**: Assists nurses in tracking patient care tasks, medication administration, and vital sign recording.

4. **AdminDashboard**: Enables administrators to oversee system operations, user management, and financial metrics.

5. **PharmacistDashboard**: Allows pharmacists to manage prescriptions, monitor inventory, and track medication dispensing.

## Usage

Import the required dashboard component based on the user's role:

```jsx
import { 
  PatientDashboard,
  DoctorDashboard,
  AdminDashboard,
  NurseDashboard,
  PharmacistDashboard
} from '../components/dashboards';

// Then use the appropriate dashboard based on user role
const Dashboard = () => {
  const { user } = useAuth();
  
  switch (user.role) {
    case 'patient':
      return <PatientDashboard />;
    case 'doctor':
      return <DoctorDashboard />;
    case 'admin':
      return <AdminDashboard />;
    case 'nurse':
      return <NurseDashboard />;
    case 'pharmacist':
      return <PharmacistDashboard />;
    default:
      return null;
  }
}
```

## Customization

Each dashboard can be customized with different color themes, menu items, and data sources as needed for your specific implementation. 