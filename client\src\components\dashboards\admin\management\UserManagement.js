import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
  Dimensions,
  Alert,
  ActivityIndicator,
  Modal,
  ScrollView,
  Switch
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import FilterUsers from './FilterUsers';
import SortControl from './SortControl';
import axios from 'axios';
import { API_URL, usersAPI } from '../../../../config/api';
import { useAuth } from '../../../../contexts/AuthContext';
import { auth } from '../../../../config/firebase';
import { ROLE_COLORS, COLORS } from '../../../../config/theme';

const { width } = Dimensions.get('window');
const cardWidth = width > 600 ? (width - 60) / 2 : width - 40; // Responsive card width

const UserManagement = () => {
  const { token } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('firstName');
  const [sortDirection, setSortDirection] = useState('asc'); // 'asc' or 'desc'

  // Modal state
  const [modalVisible, setModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [modalMode, setModalMode] = useState('edit'); // 'edit' or 'create'
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: '',
    speciality: '',
    city: '',
    country: ''
  });

  // Filter state
  const [filters, setFilters] = useState({
    role: [],
    country: [],
    city: [],
    speciality: [],
    status: []
  });

  // Track if filter panel is open
  const [showFilters, setShowFilters] = useState(false);

  // Get unique values for each filter type
  const [filterOptions, setFilterOptions] = useState({
    role: [],
    country: [],
    city: [],
    speciality: [],
    status: ['active', 'banned', 'inactive']
  });

  // Bulk selection state
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [bulkSelectMode, setBulkSelectMode] = useState(false);
  const [bulkActionModalVisible, setBulkActionModalVisible] = useState(false);

  // User details modal
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [userDetails, setUserDetails] = useState(null);

  // View mode is always card since we removed the toggle buttons
  const viewMode = 'card';

  // Get users
  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      // Ensure the user is authenticated
      const currentUser = auth.currentUser;
      if (!currentUser) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for auth to initialize
        if (!auth.currentUser) {
          setError('You need to be logged in to access this page.');
          setLoading(false);
          return;
        }
      }

      // Force token refresh to ensure we have the latest
      try {
        await auth.currentUser.getIdToken(true);
      } catch (tokenError) {
        console.error('Failed to refresh token:', tokenError);
      }

      const fetchedUsers = await usersAPI.admin.getAllUsers();
      console.log('Fetched users successfully:', fetchedUsers);

      if (!fetchedUsers) {
        console.error('No users data received');
        setError('No users data received from server.');
        setLoading(false);
        return;
      }

      if (!Array.isArray(fetchedUsers)) {
        console.error('Invalid users data received:', fetchedUsers);
        setError('Received invalid data from server. Check console for details.');
        setLoading(false);
        return;
      }

      if (fetchedUsers.length === 0) {
        console.log('No users found in database');
      }

      setUsers(fetchedUsers);

      // Extract unique values for each filter type
      const roles = [...new Set(fetchedUsers.map(user => user.role))].filter(Boolean);
      const countries = [...new Set(fetchedUsers.map(user => user.location?.country))].filter(Boolean);
      const cities = [...new Set(fetchedUsers.map(user => user.location?.city))].filter(Boolean);
      const specialities = [...new Set(fetchedUsers.map(user => user.speciality))].filter(Boolean);
      const statuses = [...new Set(fetchedUsers.map(user => user.status))].filter(Boolean);

      setFilterOptions({
        role: roles,
        country: countries,
        city: cities,
        speciality: specialities,
        status: ['active', 'banned', 'inactive'].concat(
          statuses.filter(status => !['active', 'banned', 'inactive'].includes(status))
        )
      });
    } catch (err) {
      console.error('Error fetching users:', err);
      const errorMessage = err.responseData?.error || err.message || 'Failed to fetch users. Please try again.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text) => {
    setSearchTerm(text);
  };

  const handleDelete = (uid) => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this user? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await usersAPI.admin.deleteUser(uid);

              // Remove user from state
              setUsers(users.filter((user) => user.uid !== uid));
              Alert.alert('Success', 'User deleted successfully');
            } catch (err) {
              console.error('Error deleting user:', err);
              Alert.alert('Error', 'Failed to delete user. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleBan = async (uid, isBanned) => {
    const action = isBanned ? 'unban' : 'ban';

    Alert.alert(
      `Confirm ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      `Are you sure you want to ${action} this user?`,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: action.charAt(0).toUpperCase() + action.slice(1),
          style: isBanned ? 'default' : 'destructive',
          onPress: async () => {
            try {
              await usersAPI.admin.banUser(uid, !isBanned);

              // Update user status in state
              setUsers(users.map(user =>
                user.uid === uid
                  ? {...user, status: isBanned ? 'active' : 'banned'}
                  : user
              ));

              Alert.alert('Success', `User ${isBanned ? 'unbanned' : 'banned'} successfully`);
            } catch (err) {
              console.error(`Error ${action}ning user:`, err);
              Alert.alert('Error', `Failed to ${action} user. Please try again.`);
            }
          }
        }
      ]
    );
  };

  const handleEdit = (user) => {
    console.log('Editing user:', user);

    if (!user || !user.uid) {
      Alert.alert('Error', 'Cannot edit user: Invalid user data');
      return;
    }

    // Use proper defaults for all fields to avoid undefined values
    setCurrentUser(user);
    setFormData({
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email || '',
      role: user.role || 'patient',
      speciality: user.speciality || '',
      city: user.location?.city || '',
      country: user.location?.country || ''
    });

    // Set the mode and show the modal
    setModalMode('edit');
    setModalVisible(true);
  };

  const handleCreate = () => {
    setCurrentUser(null);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      role: 'patient',
      speciality: '',
      city: '',
      country: ''
    });
    setModalMode('create');
    setModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      // Update user
      if (!formData.firstName || !formData.lastName) {
        return Alert.alert('Error', 'Please fill in all required fields');
      }

      if (!currentUser || !currentUser.uid) {
        return Alert.alert('Error', 'Cannot update user: Invalid user data');
      }

      console.log('Updating user with data:', formData);

      await usersAPI.admin.updateUser(currentUser.uid, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role,
        speciality: formData.speciality,
        location: {
          city: formData.city || '',
          country: formData.country || ''
        }
      });

      // Update user in state
      setUsers(users.map(user =>
        user.uid === currentUser.uid
          ? {
              ...user,
              firstName: formData.firstName,
              lastName: formData.lastName,
              displayName: `${formData.firstName} ${formData.lastName}`,
              role: formData.role,
              speciality: formData.speciality,
              location: {
                city: formData.city || '',
                country: formData.country || ''
              }
            }
          : user
      ));

      Alert.alert('Success', 'User updated successfully');

      // Close modal and reset form
      setModalVisible(false);
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        role: '',
        speciality: '',
        city: '',
        country: ''
      });
    } catch (err) {
      console.error('Error updating user:', err);
      Alert.alert('Error', `Failed to update user. ${err.response?.data?.message || 'Please try again.'}`);
    }
  };

  const sortUsers = (users) => {
    return [...users].sort((a, b) => {
      let comparison = 0;

      if (sortOption === 'firstName') {
        comparison = (a.firstName || '').localeCompare(b.firstName || '');
      } else if (sortOption === 'lastName') {
        comparison = (a.lastName || '').localeCompare(b.lastName || '');
      } else if (sortOption === 'createdAt') {
        // Convert to date objects for proper comparison
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        comparison = dateA - dateB;
      }

      // Reverse the comparison if sort direction is descending
      return sortDirection === 'desc' ? -comparison : comparison;
    });
  };

  // Toggle user selection for bulk actions
  const toggleUserSelection = (uid) => {
    setSelectedUsers(prevSelected => {
      if (prevSelected.includes(uid)) {
        return prevSelected.filter(id => id !== uid);
      } else {
        return [...prevSelected, uid];
      }
    });
  };

  // Toggle bulk select mode
  const toggleBulkSelectMode = () => {
    setBulkSelectMode(prev => !prev);
    if (bulkSelectMode) {
      // Clear selections when exiting bulk mode
      setSelectedUsers([]);
    }
  };

  // Select all visible users
  const selectAllUsers = () => {
    if (selectedUsers.length === sortedAndFilteredUsers.length) {
      // If all are selected, deselect all
      setSelectedUsers([]);
    } else {
      // Otherwise select all visible users
      setSelectedUsers(sortedAndFilteredUsers.map(user => user.uid));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedUsers.length === 0) return;

    Alert.alert(
      'Confirm Bulk Delete',
      `Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Create a copy of the selected users array
              const usersToDelete = [...selectedUsers];

              // Show loading indicator
              setLoading(true);

              // Delete each user
              for (const uid of usersToDelete) {
                await usersAPI.admin.deleteUser(uid);
              }

              // Update state to remove deleted users
              setUsers(users.filter(user => !usersToDelete.includes(user.uid)));

              // Clear selections
              setSelectedUsers([]);
              setBulkActionModalVisible(false);

              Alert.alert('Success', `${usersToDelete.length} users deleted successfully`);
            } catch (err) {
              console.error('Error deleting users:', err);
              Alert.alert('Error', 'Failed to delete some users. Please try again.');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // Handle bulk ban/unban
  const handleBulkBanUnban = (shouldBan) => {
    if (selectedUsers.length === 0) return;

    const action = shouldBan ? 'ban' : 'unban';

    Alert.alert(
      `Confirm Bulk ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      `Are you sure you want to ${action} ${selectedUsers.length} users?`,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: action.charAt(0).toUpperCase() + action.slice(1),
          style: shouldBan ? 'destructive' : 'default',
          onPress: async () => {
            try {
              // Create a copy of the selected users array
              const usersToUpdate = [...selectedUsers];

              // Show loading indicator
              setLoading(true);

              // Update each user
              for (const uid of usersToUpdate) {
                await usersAPI.admin.banUser(uid, shouldBan);
              }

              // Update user status in state
              setUsers(users.map(user =>
                usersToUpdate.includes(user.uid)
                  ? {...user, status: shouldBan ? 'banned' : 'active'}
                  : user
              ));

              // Clear selections
              setSelectedUsers([]);
              setBulkActionModalVisible(false);

              Alert.alert('Success', `${usersToUpdate.length} users ${shouldBan ? 'banned' : 'unbanned'} successfully`);
            } catch (err) {
              console.error(`Error ${action}ning users:`, err);
              Alert.alert('Error', `Failed to ${action} some users. Please try again.`);
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // Show user details
  const showUserDetails = (user) => {
    setUserDetails(user);
    setDetailsModalVisible(true);
  };

  const filteredUsers = users
    .filter((user) => {
      // Text search filter
      const displayName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      const matchesSearch = displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (user.email || '').toLowerCase().includes(searchTerm.toLowerCase());

      // Check if user matches all active filters
      const matchesRole = filters.role.length === 0 ||
        filters.role.includes(user.role);

      const matchesCountry = filters.country.length === 0 ||
        filters.country.includes(user.location?.country);

      const matchesCity = filters.city.length === 0 ||
        filters.city.includes(user.location?.city);

      const matchesSpeciality = filters.speciality.length === 0 ||
        filters.speciality.includes(user.speciality);

      const matchesStatus = filters.status.length === 0 ||
        filters.status.includes(user.status);

      return matchesSearch && matchesRole && matchesCountry && matchesCity && matchesSpeciality && matchesStatus;
    });

  const sortedAndFilteredUsers = sortUsers(filteredUsers);

  // Count active filters
  const activeFilterCount = Object.values(filters).flat().length;

  // Function to get role badge color
  const getRoleBadgeColor = (role) => {
    const roleLower = role?.toLowerCase();
    if (roleLower === 'doctor' && ROLE_COLORS.doctor) {
      return ROLE_COLORS.doctor.primary;
    } else if (roleLower === 'patient' && ROLE_COLORS.patient) {
      return ROLE_COLORS.patient.primary;
    } else if ((roleLower === 'caregiver' || roleLower === 'nurse') && ROLE_COLORS.caregiver) {
      return ROLE_COLORS.caregiver.primary;
    } else if (roleLower === 'supervisor' && ROLE_COLORS.supervisor) {
      return ROLE_COLORS.supervisor.primary;
    } else if (roleLower === 'admin' && ROLE_COLORS.admin) {
      return ROLE_COLORS.admin.primary;
    } else {
      return 'rgb(158, 158, 158)';
    }
  };

  // Function to get status indicator color
  const getStatusColor = (status) => {
    if (status === 'banned') return 'rgb(244, 67, 54)';
    return status === 'active' ? 'rgb(76, 175, 80)' : 'rgb(158, 158, 158)';
  };

  // Render user card
  const renderUserCard = ({ item }) => {
    const isBanned = item.status === 'banned';
    const displayName = `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown User';
    const isSelected = selectedUsers.includes(item.uid);

    return (
      <Animated.View style={[
        styles.card,
        { width: cardWidth },
        isSelected && styles.selectedCard
      ]}>
        {bulkSelectMode && (
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => toggleUserSelection(item.uid)}
          >
            <View style={[
              styles.checkbox,
              isSelected && styles.checkboxSelected
            ]}>
              {isSelected && (
                <Ionicons name="checkmark" size={16} color="#FFFFFF" />
              )}
            </View>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.cardHeader}
          onPress={() => showUserDetails(item)}
        >
          <View style={styles.profileSection}>
            <Image
              source={
                item.profilePicture
                  ? { uri: item.profilePicture }
                  : require('../../../../../assets/ImagesTest/avatar.png')
              }
              style={[
                styles.profilePicture,
                { borderColor: getRoleBadgeColor(item.role) }
              ]}
            />
            <View style={styles.nameSection}>
              <Text style={styles.userName}>{displayName}</Text>
              <Text style={styles.userEmail}>{item.email}</Text>

              <View style={styles.statusContainer}>
                <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
                <Text style={styles.statusText}>
                  {item.status === 'active' ? 'Active' :
                   item.status === 'banned' ? 'Banned' :
                   item.status === 'inactive' ? 'Inactive' : item.status || 'Unknown'}
                </Text>
              </View>
            </View>
          </View>

          <View style={[
            styles.roleBadge,
            { backgroundColor: getRoleBadgeColor(item.role) }
          ]}>
            <Text style={styles.roleBadgeText}>
              {item.role ? item.role.charAt(0).toUpperCase() + item.role.slice(1) : 'Unknown'}
            </Text>
          </View>
        </TouchableOpacity>

        <View style={styles.cardDivider} />

        <View style={styles.cardContent}>
          <View style={styles.infoRow}>
            <View style={styles.infoColumn}>
              <Text style={styles.infoLabel}>Speciality</Text>
              <Text style={styles.infoValue}>{item.speciality || 'N/A'}</Text>
            </View>

            <View style={styles.infoColumn}>
              <Text style={styles.infoLabel}>Location</Text>
              <Text style={styles.infoValue}>
                {item.location?.city && item.location?.country
                  ? `${item.location.city}, ${item.location.country}`
                  : 'N/A'
                }
              </Text>
            </View>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoColumn}>
              <Text style={styles.infoLabel}>Created</Text>
              <Text style={styles.infoValue}>
                {item.createdAt
                  ? new Date(item.createdAt).toLocaleDateString()
                  : 'N/A'
                }
              </Text>
            </View>

            <View style={styles.infoColumn}>
              <Text style={styles.infoLabel}>User Code</Text>
              <Text style={styles.infoValue}>
                {item.userCode || 'N/A'}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.cardDivider} />

        <View style={styles.cardActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.viewButton]}
            onPress={() => showUserDetails(item)}
          >
            <Ionicons name="eye-outline" size={20} color="#3498DB" style={styles.actionIcon} />
            <Text style={[styles.actionButtonText, { color: '#3498DB' }]}>View</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.editButton]}
            onPress={() => handleEdit(item)}
          >
            <Ionicons name="create-outline" size={20} color="#4285F4" style={styles.actionIcon} />
            <Text style={[styles.actionButtonText, { color: '#4285F4' }]}>Edit</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => {
              // Check if uid exists before attempting to delete
              if (item && item.uid) {
                handleDelete(item.uid);
              } else {
                Alert.alert('Error', 'Cannot delete user: Missing user ID');
              }
            }}
          >
            <Ionicons name="trash-outline" size={20} color="#F44336" style={styles.actionIcon} />
            <Text style={[styles.actionButtonText, { color: '#F44336' }]}>Delete</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.actionButton,
              isBanned ? styles.unbanButton : styles.banButton
            ]}
            onPress={() => handleBan(item.uid, isBanned)}
          >
            <Ionicons
              name={isBanned ? "shield-checkmark-outline" : "shield-outline"}
              size={20}
              color={isBanned ? "#4CAF50" : "#FF9800"}
              style={styles.actionIcon}
            />
            <Text style={[styles.actionButtonText, { color: isBanned ? "#4CAF50" : "#FF9800" }]}>
              {isBanned ? 'Unban' : 'Ban'}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  // Render form input field
  const renderFormField = (label, placeholder, name, secure = false) => (
    <View style={styles.formGroup}>
      <Text style={styles.formLabel}>{label}</Text>
      <TextInput
        style={styles.formInput}
        placeholder={placeholder}
        value={formData[name]}
        onChangeText={(text) => setFormData({...formData, [name]: text})}
        secureTextEntry={secure}
      />
    </View>
  );

  // Render role selection field
  const renderRoleSelect = () => (
    <View style={styles.formGroup}>
      <Text style={styles.formLabel}>Role</Text>
      <View style={styles.roleOptions}>
        {['patient', 'doctor', 'nurse', 'admin'].map((role) => (
          <TouchableOpacity
            key={role}
            style={[
              styles.roleOption,
              formData.role === role && styles.roleOptionSelected,
              { backgroundColor: getRoleBadgeColor(role) }
            ]}
            onPress={() => setFormData({...formData, role})}
          >
            <Text style={styles.roleOptionText}>
              {role.charAt(0).toUpperCase() + role.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // User form modal
  const renderUserModal = () => (
    <Modal
      visible={modalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => setModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {modalMode === 'edit' ? 'Edit User' : 'Create User'}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#34495E" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            <View style={styles.formRow}>
              <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.formLabel}>First Name <Text style={styles.requiredStar}>*</Text></Text>
                <View style={styles.inputContainer}>
                  <Ionicons name="person-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                  <TextInput
                    style={styles.formInput}
                    placeholder="Enter first name"
                    value={formData.firstName}
                    onChangeText={(text) => setFormData({...formData, firstName: text})}
                  />
                </View>
              </View>

              <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.formLabel}>Last Name <Text style={styles.requiredStar}>*</Text></Text>
                <View style={styles.inputContainer}>
                  <Ionicons name="person-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                  <TextInput
                    style={styles.formInput}
                    placeholder="Enter last name"
                    value={formData.lastName}
                    onChangeText={(text) => setFormData({...formData, lastName: text})}
                  />
                </View>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Email</Text>
              <View style={[styles.inputContainer, { backgroundColor: modalMode === 'edit' ? '#F5F7FA' : '#FFFFFF' }]}>
                <Ionicons name="mail-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                <TextInput
                  style={[styles.formInput, modalMode === 'edit' && { color: '#7F8C8D' }]}
                  value={formData.email}
                  onChangeText={(text) => setFormData({...formData, email: text})}
                  editable={modalMode !== 'edit'}
                  placeholder={modalMode === 'edit' ? '' : 'Enter email address'}
                />
              </View>
            </View>

            {modalMode === 'create' && (
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Password <Text style={styles.requiredStar}>*</Text></Text>
                <View style={styles.inputContainer}>
                  <Ionicons name="lock-closed-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                  <TextInput
                    style={styles.formInput}
                    placeholder="Enter password"
                    value={formData.password}
                    onChangeText={(text) => setFormData({...formData, password: text})}
                    secureTextEntry
                  />
                </View>
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Role <Text style={styles.requiredStar}>*</Text></Text>
              <View style={styles.roleOptions}>
                {['patient', 'doctor', 'caregiver', 'supervisor', 'admin'].map((role) => (
                  <TouchableOpacity
                    key={role}
                    style={[
                      styles.roleOption,
                      formData.role === role && styles.roleOptionSelected,
                      { backgroundColor: getRoleBadgeColor(role) }
                    ]}
                    onPress={() => setFormData({...formData, role})}
                  >
                    <Ionicons
                      name={
                        role === 'patient' ? 'person-outline' :
                        role === 'doctor' ? 'medkit-outline' :
                        role === 'caregiver' ? 'heart-outline' :
                        role === 'supervisor' ? 'people-outline' :
                        'shield-outline'
                      }
                      size={18}
                      color="#FFFFFF"
                      style={styles.roleIcon}
                    />
                    <Text style={styles.roleOptionText}>
                      {role.charAt(0).toUpperCase() + role.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Speciality</Text>
              <View style={styles.inputContainer}>
                <Ionicons name="fitness-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                <TextInput
                  style={styles.formInput}
                  placeholder="Enter speciality (if applicable)"
                  value={formData.speciality}
                  onChangeText={(text) => setFormData({...formData, speciality: text})}
                />
              </View>
            </View>

            <View style={styles.formRow}>
              <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.formLabel}>City</Text>
                <View style={styles.inputContainer}>
                  <Ionicons name="location-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                  <TextInput
                    style={styles.formInput}
                    placeholder="Enter city"
                    value={formData.city}
                    onChangeText={(text) => setFormData({...formData, city: text})}
                  />
                </View>
              </View>

              <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.formLabel}>Country</Text>
                <View style={styles.inputContainer}>
                  <Ionicons name="globe-outline" size={20} color="#95A5A6" style={styles.inputIcon} />
                  <TextInput
                    style={styles.formInput}
                    placeholder="Enter country"
                    value={formData.country}
                    onChangeText={(text) => setFormData({...formData, country: text})}
                  />
                </View>
              </View>
            </View>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
            >
              <Ionicons name="save-outline" size={24} color="#FFFFFF" style={{ marginRight: 8 }} />
              <Text style={styles.submitButtonText}>
                {modalMode === 'edit' ? 'Update User' : 'Create User'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  // User details modal
  const renderUserDetailsModal = () => (
    <Modal
      visible={detailsModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => setDetailsModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>User Details</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setDetailsModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#34495E" />
            </TouchableOpacity>
          </View>

          {userDetails && (
            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              <View style={styles.userDetailsHeader}>
                <Image
                  source={
                    userDetails.profilePicture
                      ? { uri: userDetails.profilePicture }
                      : require('../../../../../assets/ImagesTest/avatar.png')
                  }
                  style={styles.detailsProfilePicture}
                />
                <View style={styles.userDetailsInfo}>
                  <Text style={styles.detailsName}>
                    {`${userDetails.firstName || ''} ${userDetails.lastName || ''}`.trim() || 'Unknown User'}
                  </Text>
                  <Text style={styles.detailsEmail}>{userDetails.email}</Text>

                  <View style={styles.detailsBadgeContainer}>
                    <View style={[
                      styles.detailsBadge,
                      { backgroundColor: getRoleBadgeColor(userDetails.role) }
                    ]}>
                      <Text style={styles.detailsBadgeText}>
                        {userDetails.role ? userDetails.role.charAt(0).toUpperCase() + userDetails.role.slice(1) : 'Unknown'}
                      </Text>
                    </View>

                    <View style={[
                      styles.detailsBadge,
                      {
                        backgroundColor:
                          userDetails.status === 'active' ? '#4CAF50' :
                          userDetails.status === 'banned' ? '#F44336' :
                          '#9E9E9E'
                      }
                    ]}>
                      <Text style={styles.detailsBadgeText}>
                        {userDetails.status === 'active' ? 'Active' :
                         userDetails.status === 'banned' ? 'Banned' :
                         userDetails.status === 'inactive' ? 'Inactive' :
                         userDetails.status || 'Unknown'}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.detailsSectionTitle}>Personal Information</Text>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>User ID</Text>
                    <Text style={styles.detailsValue}>{userDetails.uid || 'N/A'}</Text>
                  </View>
                </View>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>User Code</Text>
                    <Text style={styles.detailsValue}>{userDetails.userCode || 'N/A'}</Text>
                  </View>
                </View>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>Speciality</Text>
                    <Text style={styles.detailsValue}>{userDetails.speciality || 'N/A'}</Text>
                  </View>
                </View>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>Location</Text>
                    <Text style={styles.detailsValue}>
                      {userDetails.location?.city && userDetails.location?.country
                        ? `${userDetails.location.city}, ${userDetails.location.country}`
                        : 'N/A'
                      }
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.detailsSectionTitle}>Account Information</Text>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>Created</Text>
                    <Text style={styles.detailsValue}>
                      {userDetails.createdAt
                        ? new Date(userDetails.createdAt).toLocaleString()
                        : 'N/A'
                      }
                    </Text>
                  </View>
                </View>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>Last Updated</Text>
                    <Text style={styles.detailsValue}>
                      {userDetails.updatedAt
                        ? new Date(userDetails.updatedAt).toLocaleString()
                        : 'N/A'
                      }
                    </Text>
                  </View>
                </View>

                <View style={styles.detailsRow}>
                  <View style={styles.detailsItem}>
                    <Text style={styles.detailsLabel}>Email Verified</Text>
                    <Text style={styles.detailsValue}>
                      {userDetails.emailVerified ? 'Yes' : 'No'}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.detailsActions}>
                <TouchableOpacity
                  style={[styles.detailsActionButton, styles.editActionButton]}
                  onPress={() => {
                    setDetailsModalVisible(false);
                    handleEdit(userDetails);
                  }}
                >
                  <Ionicons name="create-outline" size={20} color="#FFFFFF" />
                  <Text style={styles.detailsActionButtonText}>Edit User</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.detailsActionButton,
                    userDetails.status === 'banned' ? styles.unbanActionButton : styles.banActionButton
                  ]}
                  onPress={() => {
                    setDetailsModalVisible(false);
                    handleBan(userDetails.uid, userDetails.status === 'banned');
                  }}
                >
                  <Ionicons
                    name={userDetails.status === 'banned' ? "shield-checkmark-outline" : "shield-outline"}
                    size={20}
                    color="#FFFFFF"
                  />
                  <Text style={styles.detailsActionButtonText}>
                    {userDetails.status === 'banned' ? 'Unban User' : 'Ban User'}
                  </Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          )}
        </View>
      </View>
    </Modal>
  );

  // Bulk action modal
  const renderBulkActionModal = () => (
    <Modal
      visible={bulkActionModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => setBulkActionModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { maxWidth: 400 }]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Bulk Actions</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setBulkActionModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#34495E" />
            </TouchableOpacity>
          </View>

          <View style={styles.bulkActionContent}>
            <Text style={styles.bulkActionText}>
              {selectedUsers.length} users selected
            </Text>

            <View style={styles.bulkActionButtons}>
              <TouchableOpacity
                style={[styles.bulkActionButton, styles.bulkDeleteButton]}
                onPress={handleBulkDelete}
                disabled={selectedUsers.length === 0}
              >
                <Ionicons name="trash-outline" size={24} color="#FFFFFF" />
                <Text style={styles.bulkActionButtonText}>Delete</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.bulkActionButton, styles.bulkBanButton]}
                onPress={() => handleBulkBanUnban(true)}
                disabled={selectedUsers.length === 0}
              >
                <Ionicons name="shield-outline" size={24} color="#FFFFFF" />
                <Text style={styles.bulkActionButtonText}>Ban</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.bulkActionButton, styles.bulkUnbanButton]}
                onPress={() => handleBulkBanUnban(false)}
                disabled={selectedUsers.length === 0}
              >
                <Ionicons name="shield-checkmark-outline" size={24} color="#FFFFFF" />
                <Text style={styles.bulkActionButtonText}>Unban</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (loading && users.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading users...</Text>
      </View>
    );
  }

  if (error && users.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={48} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchUsers}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>All users</Text>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by name or email..."
            value={searchTerm}
            onChangeText={handleSearch}
          />
        </View>

        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Ionicons name="options-outline" size={20} color="#FFFFFF" style={{ marginRight: 8 }} />
          <Text style={styles.filterButtonText}>Filters</Text>
          {activeFilterCount > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>{activeFilterCount}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {showFilters && (
        <FilterUsers
          options={filterOptions}
          filters={filters}
          setFilters={setFilters}
        />
      )}

      <View style={styles.sortContainer}>
        <View style={styles.sortControlWrapper}>
          <Text style={styles.sortLabel}>Sort by:</Text>
          <SortControl
            options={[
              { value: 'firstName', label: 'First Name' },
              { value: 'lastName', label: 'Last Name' },
              { value: 'createdAt', label: 'Created Date' },
              { value: 'role', label: 'Role' }
            ]}
            value={sortOption}
            onSelect={setSortOption}
            direction={sortDirection}
            onDirectionChange={setSortDirection}
          />
        </View>

        {loading && (
          <ActivityIndicator style={styles.loadingIndicator} size="small" color="#4CAF50" />
        )}
      </View>

      <View style={styles.resultContainer}>
        <Text style={styles.resultCount}>
          {sortedAndFilteredUsers.length} users found
        </Text>

        {selectedUsers.length > 0 && (
          <Text style={styles.selectedCount}>
            {selectedUsers.length} selected
          </Text>
        )}
      </View>

      <FlatList
        data={sortedAndFilteredUsers}
        renderItem={renderUserCard}
        keyExtractor={(item) => item.uid || item.id || Math.random().toString()}
        contentContainerStyle={styles.cardList}
        numColumns={width > 800 ? 2 : 1}
        columnWrapperStyle={width > 800 ? styles.columnWrapper : null}
        refreshing={loading}
        onRefresh={fetchUsers}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color="#CCCCCC" />
            <Text style={styles.emptyText}>No users found</Text>
            <Text style={styles.emptySubtext}>Try adjusting your filters or search terms</Text>
          </View>
        }
      />

      {renderUserModal()}
      {renderUserDetailsModal()}
      {renderBulkActionModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F5F7FA',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2C3E50',
    fontFamily: 'System',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E6ED',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewModeButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 8,
    backgroundColor: '#F5F7FA',
    borderWidth: 1,
    borderColor: '#E0E6ED',
  },
  headerActionIcon: {
    marginRight: 6,
  },
  headerActionText: {
    fontWeight: '600',
    color: '#2C3E50',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E6ED',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#FFFFFF',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C3E50',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#3498DB',
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  filterButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  filterBadge: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  filterBadgeText: {
    color: '#3498DB',
    fontSize: 12,
    fontWeight: 'bold',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  sortControlWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
    color: '#2C3E50',
  },
  loadingIndicator: {
    marginLeft: 12,
  },
  resultContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultCount: {
    color: '#7F8C8D',
    fontSize: 14,
  },
  selectedCount: {
    color: '#3498DB',
    fontSize: 14,
    fontWeight: 'bold',
  },
  cardList: {
    paddingBottom: 20,
  },
  columnWrapper: {
    justifyContent: 'space-between',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: '#3498DB',
  },
  checkboxContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 5,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#3498DB',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  checkboxSelected: {
    backgroundColor: '#3498DB',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FAFBFC',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: '#E0E6ED',
    backgroundColor: '#F5F7FA',
  },
  nameSection: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  userEmail: {
    fontSize: 14,
    color: '#7F8C8D',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#7F8C8D',
  },
  roleBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginLeft: 8,
  },
  roleBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardDivider: {
    height: 1,
    backgroundColor: '#E0E6ED',
  },
  cardContent: {
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  infoColumn: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: '#95A5A6',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '500',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardActions: {
    flexDirection: 'row',
    backgroundColor: '#FAFBFC',
    flexWrap: 'wrap',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E6ED',
    minWidth: 80,
  },
  actionIcon: {
    marginRight: 6,
  },
  actionButtonText: {
    fontWeight: '600',
  },
  viewButton: {
    borderRightWidth: 1,
    borderRightColor: '#E0E6ED',
  },
  editButton: {
    borderRightWidth: 1,
    borderRightColor: '#E0E6ED',
  },
  deleteButton: {
    borderRightWidth: 1,
    borderRightColor: '#E0E6ED',
  },
  banButton: {
    // No special styling needed
  },
  unbanButton: {
    // No special styling needed
  },

  // Empty state
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#7F8C8D',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#95A5A6',
    marginTop: 8,
    textAlign: 'center',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    width: '95%',
    maxWidth: 500,
    maxHeight: '90%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E6ED',
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  closeButton: {
    padding: 8,
  },
  modalBody: {
    maxHeight: '80%',
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 8,
  },
  requiredStar: {
    color: '#E74C3C',
    fontWeight: 'bold',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E6ED',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#FFFFFF',
  },
  inputIcon: {
    marginRight: 12,
  },
  formInput: {
    flex: 1,
    fontSize: 16,
    color: '#2C3E50',
  },
  roleOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 10,
    marginBottom: 10,
  },
  roleOptionSelected: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  roleOptionText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  roleIcon: {
    marginRight: 8,
  },
  submitButton: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // User details modal styles
  userDetailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E6ED',
  },
  detailsProfilePicture: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#E0E6ED',
  },
  userDetailsInfo: {
    marginLeft: 16,
    flex: 1,
  },
  detailsName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  detailsEmail: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 12,
  },
  detailsBadgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  detailsBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  detailsSection: {
    marginBottom: 24,
  },
  detailsSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E6ED',
  },
  detailsRow: {
    marginBottom: 12,
  },
  detailsItem: {
    marginBottom: 8,
  },
  detailsLabel: {
    fontSize: 14,
    color: '#95A5A6',
    marginBottom: 4,
  },
  detailsValue: {
    fontSize: 16,
    color: '#2C3E50',
    fontWeight: '500',
  },
  detailsActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  detailsActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 8,
  },
  editActionButton: {
    backgroundColor: '#4285F4',
  },
  banActionButton: {
    backgroundColor: '#FF9800',
  },
  unbanActionButton: {
    backgroundColor: '#4CAF50',
  },
  detailsActionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 8,
  },

  // Bulk action modal styles
  bulkActionContent: {
    padding: 16,
  },
  bulkActionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 24,
    textAlign: 'center',
  },
  bulkActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bulkActionButton: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 8,
  },
  bulkDeleteButton: {
    backgroundColor: '#F44336',
  },
  bulkBanButton: {
    backgroundColor: '#FF9800',
  },
  bulkUnbanButton: {
    backgroundColor: '#4CAF50',
  },
  bulkActionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginTop: 8,
  },

  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#E74C3C',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#3498DB',
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default UserManagement;