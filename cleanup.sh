#!/bin/bash

echo "Removing sensitive files from Git history..."

# Make sure we have all changes committed first
git add .
git commit -m "Add changes before history cleanup"

# Create a backup branch just in case
git branch backup-before-cleanup

# Remove sensitive files from Git history
git filter-branch --force --index-filter \
  "git rm --cached --ignore-unmatch server/config/serviceAccountKey.json server/config/neurocare-f672b-firebase-adminsdk-fbsvc-1e3f55a60f.json" \
  --prune-empty --tag-name-filter cat -- --all

# Remove the original refs
git for-each-ref --format="delete %(refname)" refs/original | git update-ref --stdin
git reflog expire --expire=now --all
git gc --prune=now

echo "History cleaned. Now remove the sensitive files from your working directory:"
rm -f server/config/serviceAccountKey.json server/config/neurocare-f672b-firebase-adminsdk-fbsvc-1e3f55a60f.json
echo "Then push with: git push -f origin main"
