import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Portal,
  Modal,
  List,
  Divider,
  Chip,
  Avatar,
  TextInput,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { format, parse } from 'date-fns';
import DateTimePicker from '@react-native-community/datetimepicker';
import { firebaseAppointmentsService } from '../../../../services/firebaseAppointmentsService';

const ITEMS_PER_PAGE = 10;

const DoctorAppointments = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // États pour la reprogrammation
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [newDate, setNewDate] = useState(new Date());
  const [newTime, setNewTime] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [notes, setNotes] = useState('');

  useEffect(() => {
    fetchAppointments();
  }, [page]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);

      // Use Firebase service to get doctor appointments
      const options = {
        includePatientDetails: true,
        dateOrder: 'desc',  // Most recent appointments first
        timeOrder: 'asc'    // Earlier times first for same day
      };

      // Get appointments from Firebase
      const fetchedAppointments = await firebaseAppointmentsService.getDoctorAppointments(options);
      console.log(`Fetched ${fetchedAppointments.length} total appointments`);

      // Apply pagination manually
      const startIndex = page * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const paginatedAppointments = fetchedAppointments.slice(startIndex, endIndex);

      setAppointments(paginatedAppointments);

      // Calculate total pages
      const totalItems = fetchedAppointments.length;
      setTotalPages(Math.ceil(totalItems / ITEMS_PER_PAGE));

      console.log(`Showing appointments ${startIndex+1} to ${Math.min(endIndex, totalItems)} of ${totalItems}`);
    } catch (error) {
      console.error('Error fetching appointments from Firebase:', error);
      Alert.alert('Error', 'Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchAppointments();
    setRefreshing(false);
  };

  const handleUpdateStatus = async (appointmentId, newStatus) => {
    try {
      setLoading(true);

      // Update appointment status in Firebase
      await firebaseAppointmentsService.updateAppointment(appointmentId, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });

      // Refresh the appointments list
      await fetchAppointments();

      Alert.alert('Success', 'Appointment status updated successfully');
    } catch (error) {
      console.error('Error updating appointment status in Firebase:', error);
      Alert.alert('Error', 'Failed to update appointment status');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour ouvrir le modal de reprogrammation
  const openRescheduleModal = (appointment) => {
    // Initialiser la date et l'heure avec les valeurs actuelles du rendez-vous
    if (appointment.date) {
      // Convertir la date du format YYYY-MM-DD en objet Date
      const [year, month, day] = appointment.date.split('-').map(num => parseInt(num));
      const appointmentDate = new Date(year, month - 1, day);
      setNewDate(appointmentDate);

      // Convertir l'heure en objet Date si disponible
      if (appointment.time) {
        try {
          const timeObj = parse(appointment.time, 'HH:mm', new Date());
          setNewTime(timeObj);
        } catch (error) {
          console.error('Error parsing time:', error);
          setNewTime(new Date());
        }
      }
    }

    // Initialiser les notes
    setNotes(`Rescheduled from ${appointment.date} at ${appointment.time || 'N/A'}`);

    // Ouvrir le modal
    setShowRescheduleModal(true);
  };

  // Fonction pour gérer le changement de date
  const onDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setNewDate(selectedDate);
    }
  };

  // Fonction pour gérer le changement d'heure
  const onTimeChange = (event, selectedTime) => {
    setShowTimePicker(false);
    if (selectedTime) {
      setNewTime(selectedTime);
    }
  };

  // Fonction pour reprogrammer un rendez-vous
  const handleReschedule = async () => {
    if (!selectedAppointment) return;

    try {
      setLoading(true);

      // Formater la date au format YYYY-MM-DD
      const formattedDate = format(newDate, 'yyyy-MM-dd');

      // Formater l'heure au format HH:mm
      const formattedTime = format(newTime, 'HH:mm');

      // Mettre à jour le rendez-vous dans Firebase
      await firebaseAppointmentsService.updateAppointment(selectedAppointment.id, {
        date: formattedDate,
        time: formattedTime,
        status: 'pending', // Remettre le statut à "pending" après reprogrammation
        previousDate: selectedAppointment.date,
        previousTime: selectedAppointment.time,
        notes: notes,
        rescheduledAt: new Date().toISOString(),
        rescheduledBy: 'doctor', // Indiquer que c'est le médecin qui a reprogrammé
        updatedAt: new Date().toISOString()
      });

      // Fermer le modal
      setShowRescheduleModal(false);
      setShowDetailsModal(false);

      // Rafraîchir la liste des rendez-vous
      await fetchAppointments();

      // Afficher un message de succès
      Alert.alert('Success', 'Appointment rescheduled successfully');
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      Alert.alert('Error', 'Failed to reschedule appointment');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA500';
      case 'confirmed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const renderAppointmentCard = (appointment) => (
    <Card
      key={appointment.id}
      style={styles.card}
      onPress={() => {
        setSelectedAppointment(appointment);
        setShowDetailsModal(true);
      }}
    >
      <LinearGradient
        colors={['#f5f7fa', '#ffffff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.cardGradient}
      >
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.dateContainer}>
              <Title style={styles.dateTitle}>{format(new Date(appointment.date), 'MMMM dd, yyyy')}</Title>
              <View style={styles.statusContainer}>
                <View style={[styles.statusDot, { backgroundColor: getStatusColor(appointment.status) }]} />
                <Text style={[styles.statusText, { color: getStatusColor(appointment.status) }]}>
                  {appointment.status.toUpperCase()}
                </Text>
              </View>
            </View>
            <Avatar.Icon
              size={40}
              icon="calendar"
              style={[styles.avatarIcon, { backgroundColor: getStatusColor(appointment.status) + '20' }]}
              color={getStatusColor(appointment.status)}
            />
          </View>

          <Divider style={styles.cardDivider} />

          <View style={styles.cardDetails}>
            <View style={styles.detailRow}>
              <Ionicons name="time-outline" size={18} color="#666" style={styles.detailIcon} />
              <Paragraph style={styles.detailText}>
                {appointment.time || 'Time not specified'}
              </Paragraph>
            </View>

            <View style={styles.detailRow}>
              <Ionicons name="medical-outline" size={18} color="#666" style={styles.detailIcon} />
              <Paragraph style={styles.detailText}>
                {appointment.type || 'In-person'}
              </Paragraph>
            </View>

            <View style={styles.detailRow}>
              <Ionicons name="person-outline" size={18} color="#666" style={styles.detailIcon} />
              <Paragraph style={styles.detailText}>
                {appointment.patient?.firstName && appointment.patient?.lastName
                  ? `${appointment.patient.firstName} ${appointment.patient.lastName}`
                  : appointment.patient?.displayName
                    ? appointment.patient.displayName
                    : appointment.patient?.name
                      ? appointment.patient.name
                      : 'Unknown Patient'}
              </Paragraph>
            </View>
          </View>

          <View style={styles.cardFooter}>
            <TouchableOpacity
              style={styles.viewDetailsButton}
              onPress={() => {
                setSelectedAppointment(appointment);
                setShowDetailsModal(true);
              }}
            >
              <Text style={styles.viewDetailsText}>View Details</Text>
              <Ionicons name="chevron-forward" size={16} color="#4CAF50" />
            </TouchableOpacity>
          </View>
        </Card.Content>
      </LinearGradient>
    </Card>
  );

  // Rendu du modal de détails
  const renderDetailsModal = () => (
    <Portal>
      <Modal
        visible={showDetailsModal}
        onDismiss={() => setShowDetailsModal(false)}
        contentContainerStyle={styles.modal}
      >
        {selectedAppointment && (
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.modalHeader}>
              <View style={styles.modalTitleContainer}>
                <Title style={styles.modalTitle}>Appointment Details</Title>
                <View style={[styles.modalStatusBadge, { backgroundColor: getStatusColor(selectedAppointment.status) + '20' }]}>
                  <Text style={[styles.modalStatusText, { color: getStatusColor(selectedAppointment.status) }]}>
                    {selectedAppointment.status.toUpperCase()}
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowDetailsModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.modalDateTimeContainer}>
              <View style={styles.modalDateContainer}>
                <Ionicons name="calendar-outline" size={24} color="#4CAF50" style={styles.modalIcon} />
                <View>
                  <Text style={styles.modalLabel}>Date</Text>
                  <Text style={styles.modalDateValue}>
                    {format(new Date(selectedAppointment.date), 'MMMM dd, yyyy')}
                  </Text>
                </View>
              </View>

              <View style={styles.modalTimeContainer}>
                <Ionicons name="time-outline" size={24} color="#2196F3" style={styles.modalIcon} />
                <View>
                  <Text style={styles.modalLabel}>Time</Text>
                  <Text style={styles.modalTimeValue}>
                    {selectedAppointment.time || 'Not specified'}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.modalDetailsSection}>
              <View style={styles.modalDetailRow}>
                <Ionicons name="medical-outline" size={20} color="#FF9800" style={styles.modalDetailIcon} />
                <View style={styles.modalDetailTextContainer}>
                  <Text style={styles.modalDetailLabel}>Type</Text>
                  <Text style={styles.modalDetailValue}>
                    {selectedAppointment.type || 'In-person'}
                  </Text>
                </View>
              </View>

              <View style={styles.modalDetailRow}>
                <Ionicons name="person-outline" size={20} color="#9C27B0" style={styles.modalDetailIcon} />
                <View style={styles.modalDetailTextContainer}>
                  <Text style={styles.modalDetailLabel}>Patient</Text>
                  <Text style={styles.modalDetailValue}>
                    {selectedAppointment.patient?.firstName && selectedAppointment.patient?.lastName
                      ? `${selectedAppointment.patient.firstName} ${selectedAppointment.patient.lastName}`
                      : selectedAppointment.patient?.displayName
                        ? selectedAppointment.patient.displayName
                        : selectedAppointment.patient?.name
                          ? selectedAppointment.patient.name
                          : 'Unknown Patient'}
                  </Text>
                </View>
              </View>

              <View style={styles.modalDetailRow}>
                <Ionicons name="document-text-outline" size={20} color="#607D8B" style={styles.modalDetailIcon} />
                <View style={styles.modalDetailTextContainer}>
                  <Text style={styles.modalDetailLabel}>Reason</Text>
                  <Text style={styles.modalDetailValue}>
                    {selectedAppointment.reason || 'Not specified'}
                  </Text>
                </View>
              </View>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.actionButtons}>
              {selectedAppointment.status === 'pending' && (
                <>
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateStatus(selectedAppointment.id, 'confirmed')}
                    style={[styles.actionButton, styles.confirmButton]}
                    icon="check"
                  >
                    Confirm
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateStatus(selectedAppointment.id, 'cancelled')}
                    style={[styles.actionButton, styles.cancelButton]}
                    icon="close"
                  >
                    Cancel
                  </Button>
                </>
              )}
              {selectedAppointment.status === 'confirmed' && (
                <>
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateStatus(selectedAppointment.id, 'completed')}
                    style={[styles.actionButton, styles.completeButton]}
                    icon="check-all"
                  >
                    Mark as Completed
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => openRescheduleModal(selectedAppointment)}
                    style={[styles.actionButton, styles.rescheduleButton]}
                    icon="calendar-sync"
                  >
                    Reschedule
                  </Button>
                </>
              )}
              {(selectedAppointment.status === 'pending' || selectedAppointment.status === 'cancelled') && (
                <Button
                  mode="contained"
                  onPress={() => openRescheduleModal(selectedAppointment)}
                  style={[styles.actionButton, styles.rescheduleButton]}
                  icon="calendar-sync"
                >
                  Reschedule
                </Button>
              )}
            </View>
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  // Rendu du modal de reprogrammation
  const renderRescheduleModal = () => (
    <Portal>
      <Modal
        visible={showRescheduleModal}
        onDismiss={() => setShowRescheduleModal(false)}
        contentContainerStyle={styles.modal}
      >
        {selectedAppointment && (
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.modalHeader}>
              <View style={styles.modalTitleContainer}>
                <Title style={styles.modalTitle}>Reschedule Appointment</Title>
                <Text style={styles.modalSubtitle}>
                  Current date: {selectedAppointment.date} at {selectedAppointment.time || 'N/A'}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowRescheduleModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>New Date:</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Ionicons name="calendar-outline" size={20} color="#4CAF50" style={styles.datePickerIcon} />
                <Text style={styles.datePickerText}>{format(newDate, 'MMMM dd, yyyy')}</Text>
              </TouchableOpacity>

              {showDatePicker && (
                <DateTimePicker
                  value={newDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={onDateChange}
                  minimumDate={new Date()}
                />
              )}
            </View>

            <View style={styles.datePickerContainer}>
              <Text style={styles.datePickerLabel}>New Time:</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowTimePicker(true)}
              >
                <Ionicons name="time-outline" size={20} color="#2196F3" style={styles.datePickerIcon} />
                <Text style={styles.datePickerText}>{format(newTime, 'HH:mm')}</Text>
              </TouchableOpacity>

              {showTimePicker && (
                <DateTimePicker
                  value={newTime}
                  mode="time"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={onTimeChange}
                />
              )}
            </View>

            <View style={styles.notesContainer}>
              <Text style={styles.datePickerLabel}>Notes:</Text>
              <TextInput
                style={styles.notesInput}
                value={notes}
                onChangeText={setNotes}
                multiline
                numberOfLines={3}
                placeholder="Add notes about rescheduling"
              />
            </View>

            <Divider style={styles.divider} />

            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => setShowRescheduleModal(false)}
                style={styles.actionButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleReschedule}
                style={[styles.actionButton, styles.rescheduleButton]}
                icon="calendar-sync"
                loading={loading}
                disabled={loading}
              >
                Confirm Reschedule
              </Button>
            </View>
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {appointments.length === 0 ? (
          <Text style={styles.noAppointments}>No appointments found</Text>
        ) : (
          <>
            {appointments.map(renderAppointmentCard)}
            <View style={styles.pagination}>
              <View style={styles.paginationControls}>
                <TouchableOpacity
                  style={[styles.paginationButton, page === 0 && styles.paginationButtonDisabled]}
                  onPress={() => page > 0 && setPage(page - 1)}
                  disabled={page === 0}
                >
                  <Text style={styles.paginationButtonText}>Previous</Text>
                </TouchableOpacity>
                <Text style={styles.paginationText}>
                  Page {page + 1} of {Math.max(1, totalPages)}
                </Text>
                <TouchableOpacity
                  style={[styles.paginationButton, page >= totalPages - 1 && styles.paginationButtonDisabled]}
                  onPress={() => page < totalPages - 1 && setPage(page + 1)}
                  disabled={page >= totalPages - 1}
                >
                  <Text style={styles.paginationButtonText}>Next</Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}
      </ScrollView>
      {renderDetailsModal()}
      {renderRescheduleModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 12,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: 'transparent', // Important pour que le gradient soit visible
  },
  cardGradient: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  dateContainer: {
    flex: 1,
  },
  dateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  avatarIcon: {
    marginLeft: 8,
  },
  cardDivider: {
    height: 1,
    marginVertical: 12,
    backgroundColor: '#e0e0e0',
  },
  cardDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  cardFooter: {
    marginTop: 12,
    alignItems: 'flex-end',
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 2,
  },
  viewDetailsText: {
    color: '#4CAF50',
    fontWeight: 'bold',
    marginRight: 4,
    fontSize: 14,
  },
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 16,
    maxHeight: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  modalTitleContainer: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  modalStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  modalStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  modalDateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  modalDateContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 8,
  },
  modalTimeContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
  },
  modalIcon: {
    marginRight: 12,
  },
  modalLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 2,
  },
  modalDateValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  modalTimeValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  modalDetailsSection: {
    marginVertical: 16,
  },
  modalDetailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  modalDetailIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  modalDetailTextContainer: {
    flex: 1,
  },
  modalDetailLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 2,
  },
  modalDetailValue: {
    fontSize: 16,
    color: '#333',
  },
  divider: {
    marginVertical: 16,
    height: 1,
    backgroundColor: '#e0e0e0',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 8,
    paddingVertical: 8,
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  completeButton: {
    backgroundColor: '#2196F3',
  },
  rescheduleButton: {
    backgroundColor: '#9C27B0',
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#757575',
    marginTop: 4,
  },
  datePickerContainer: {
    marginVertical: 12,
  },
  datePickerLabel: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 8,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  datePickerIcon: {
    marginRight: 10,
  },
  datePickerText: {
    fontSize: 16,
    color: '#333',
  },
  notesContainer: {
    marginVertical: 12,
  },
  notesInput: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    padding: 10,
    fontSize: 16,
  },
  noAppointments: {
    textAlign: 'center',
    marginTop: 40,
    marginBottom: 40,
    fontSize: 16,
    color: '#757575',
  },
  pagination: {
    marginVertical: 16,
  },
  paginationControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  paginationButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  paginationButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  paginationButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  paginationText: {
    marginHorizontal: 10,
    fontSize: 14,
    color: '#555',
  },
});

export default DoctorAppointments;