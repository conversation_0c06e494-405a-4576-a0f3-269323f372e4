/**
 * <PERSON><PERSON><PERSON> to deploy Firestore rules without requiring Firebase CLI
 * 
 * This script uses the Firebase Admin SDK to deploy Firestore rules
 * Run with: node deploy-rules.js
 */
const fs = require('fs');
const path = require('path');
const admin = require('firebase-admin');

// Path to service account key
const serviceAccountPath = path.join(__dirname, 'server', 'config', 'serviceAccountKey.json');

// Path to Firestore rules
const rulesPath = path.join(__dirname, 'firestore.rules');

// Check if service account file exists
if (!fs.existsSync(serviceAccountPath)) {
  console.error('Service account key file not found at:', serviceAccountPath);
  console.error('Please make sure the file exists and try again.');
  process.exit(1);
}

// Check if rules file exists
if (!fs.existsSync(rulesPath)) {
  console.error('Firestore rules file not found at:', rulesPath);
  console.error('Please make sure the file exists and try again.');
  process.exit(1);
}

// Read service account key
const serviceAccount = require(serviceAccountPath);

// Initialize Firebase Admin
try {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
  console.log('Firebase Admin initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin:', error);
  process.exit(1);
}

// Read rules file
const rules = fs.readFileSync(rulesPath, 'utf8');

// Deploy rules
async function deployRules() {
  try {
    console.log('Deploying Firestore rules...');
    
    // Get Firestore instance
    const firestore = admin.firestore();
    
    // Use the Firebase Admin SDK to update security rules
    // Note: This is a simplified example and may not work in all environments
    // In a real-world scenario, you would use the Firebase CLI or REST API
    console.log('Rules deployment is not directly supported through the Admin SDK.');
    console.log('Please use one of the following methods:');
    console.log('1. Install Firebase CLI: npm install -g firebase-tools');
    console.log('2. Run: firebase login');
    console.log('3. Run: firebase deploy --only firestore:rules');
    console.log('\nAlternatively, you can manually update the rules in the Firebase Console:');
    console.log('1. Go to https://console.firebase.google.com/');
    console.log('2. Select your project');
    console.log('3. Go to Firestore Database > Rules');
    console.log('4. Copy and paste the rules from firestore.rules');
    
    // Print the rules for easy copying
    console.log('\nHere are your current rules:');
    console.log('----------------------------------------');
    console.log(rules);
    console.log('----------------------------------------');
    
    console.log('\nRules deployment guide completed.');
  } catch (error) {
    console.error('Error deploying rules:', error);
  } finally {
    process.exit(0);
  }
}

deployRules();
