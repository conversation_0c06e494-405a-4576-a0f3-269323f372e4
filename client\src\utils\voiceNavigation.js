import { Audio } from 'expo-av';
import { speak } from 'expo-speech';

// Map of voice commands to navigation destinations
const PATIENT_NAVIGATION_COMMANDS = {
  // Basic navigation
  'dashboard': 'Dashboard',
  'home': 'Dashboard',
  'go home': 'Dashboard',
  'main screen': 'Dashboard',

  // Health records
  'health records': 'HealthRecords',
  'my records': 'HealthRecords',
  'medical records': 'HealthRecords',
  'show records': 'HealthRecords',
  'open records': 'HealthRecords',

  // Appointments
  'appointments': 'Appointments',
  'my appointments': 'Appointments',
  'show appointments': 'Appointments',
  'view appointments': 'Appointments',
  'schedule': 'Appointments',

  // Maps and Locations
  'map': 'MapScreen',
  'maps': 'MapScreen',
  'my locations': 'MapScreen',
  'locations': 'MapScreen',
  'saved locations': 'MapScreen',
  'show map': 'MapScreen',
  'open map': 'MapScreen',

  // Medications
  'medications': 'Medications',
  'my medications': 'Medications',
  'show medications': 'Medications',
  'view medications': 'Medications',
  'medicine': 'Medications',
  'pills': 'Medications',

  // Record vitals
  'record vitals': 'RecordVitals',
  'vitals': 'RecordVitals',
  'check vitals': 'RecordVitals',
  'log vitals': 'RecordVitals',

  // Profile
  'profile': 'Profile',
  'my profile': 'Profile',
  'account': 'Profile',
  'settings': 'Profile',

  // QR Code
  'qr code': 'UserQRCode',
  'my qr code': 'UserQRCode',
  'show qr code': 'UserQRCode',
};

// Request microphone permissions
const requestMicrophonePermission = async () => {
  try {
    const { status } = await Audio.requestPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error requesting microphone permission:', error);
    return false;
  }
};

// Start voice recording
const startVoiceRecording = async () => {
  try {
    // Configure audio mode for recording
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
      playsInSilentModeIOS: true,
      staysActiveInBackground: false,
    });

    // Create and prepare the recording
    const { recording } = await Audio.Recording.createAsync(
      Audio.RecordingOptionsPresets.HIGH_QUALITY
    );

    // Provide audio feedback that we're listening
    await speak("Listening for navigation command", {
      rate: 1.0,
      onDone: () => console.log("Voice prompt completed"),
      onError: (error) => console.error("Voice prompt error:", error)
    });

    return recording;
  } catch (error) {
    console.error('Failed to start recording:', error);
    return null;
  }
};

// Stop voice recording and process the command
const stopVoiceRecording = async (recording, onResult) => {
  if (!recording) return;

  try {
    // Stop recording
    await recording.stopAndUnloadAsync();
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: false,
    });

    // In a real app, you would send the audio to a speech-to-text service
    // For this demo, we'll simulate processing with a timeout

    // Process the recording (simulated)
    setTimeout(() => {
      // This is where you would normally process the actual speech recognition result
      // For now, we'll simulate a random command from our list
      const commands = Object.keys(PATIENT_NAVIGATION_COMMANDS);
      const randomCommand = commands[Math.floor(Math.random() * commands.length)];
      const destination = PATIENT_NAVIGATION_COMMANDS[randomCommand];

      onResult({
        success: true,
        command: randomCommand,
        destination: destination
      });
    }, 1500);
  } catch (error) {
    console.error('Failed to stop recording:', error);
    onResult({
      success: false,
      error: 'Failed to process voice command'
    });
  }
};

// Process a voice command string and return the navigation destination
const processVoiceCommand = (commandText) => {
  if (!commandText) return null;

  // Convert to lowercase for case-insensitive matching
  const normalizedCommand = commandText.toLowerCase().trim();

  // Check if the command matches any of our navigation commands
  for (const [command, destination] of Object.entries(PATIENT_NAVIGATION_COMMANDS)) {
    if (normalizedCommand.includes(command.toLowerCase())) {
      return destination;
    }
  }

  // No matching command found
  return null;
};

export {
  PATIENT_NAVIGATION_COMMANDS,
  requestMicrophonePermission,
  startVoiceRecording,
  stopVoiceRecording,
  processVoiceCommand
};
