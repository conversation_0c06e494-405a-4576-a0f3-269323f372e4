import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Platform,
  Modal,
  Image,
  Alert,
  Animated,
  Easing
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { showMessage } from 'react-native-flash-message';
import CountryFlag from "react-native-country-flag";
import axios from 'axios';
import { COLORS, getThemeForRole } from '../../config/theme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons, MaterialIcons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';

// Storage key for profile image
const PROFILE_IMAGE_STORAGE_KEY = '@neurocare:profile_image';



// Country codes data
const countries = [
  { code: 'AF', name: 'Afghanistan', dialCode: '+93' },
  { code: 'AL', name: 'Albania', dialCode: '+355' },
  { code: 'DZ', name: 'Algeria', dialCode: '+213' },
  { code: 'AD', name: 'Andorra', dialCode: '+376' },
  { code: 'AO', name: 'Angola', dialCode: '+244' },
  { code: 'AR', name: 'Argentina', dialCode: '+54' },
  { code: 'AM', name: 'Armenia', dialCode: '+374' },
  { code: 'AU', name: 'Australia', dialCode: '+61' },
  { code: 'AT', name: 'Austria', dialCode: '+43' },
  { code: 'AZ', name: 'Azerbaijan', dialCode: '+994' },
  { code: 'BS', name: 'Bahamas', dialCode: '+1242' },
  { code: 'BH', name: 'Bahrain', dialCode: '+973' },
  { code: 'BD', name: 'Bangladesh', dialCode: '+880' },
  { code: 'BY', name: 'Belarus', dialCode: '+375' },
  { code: 'BE', name: 'Belgium', dialCode: '+32' },
  { code: 'BZ', name: 'Belize', dialCode: '+501' },
  { code: 'BJ', name: 'Benin', dialCode: '+229' },
  { code: 'BT', name: 'Bhutan', dialCode: '+975' },
  { code: 'BO', name: 'Bolivia', dialCode: '+591' },
  { code: 'BA', name: 'Bosnia and Herzegovina', dialCode: '+387' },
  { code: 'BW', name: 'Botswana', dialCode: '+267' },
  { code: 'BR', name: 'Brazil', dialCode: '+55' },
  { code: 'BN', name: 'Brunei', dialCode: '+673' },
  { code: 'BG', name: 'Bulgaria', dialCode: '+359' },
  { code: 'BF', name: 'Burkina Faso', dialCode: '+226' },
  { code: 'BI', name: 'Burundi', dialCode: '+257' },
  { code: 'KH', name: 'Cambodia', dialCode: '+855' },
  { code: 'CM', name: 'Cameroon', dialCode: '+237' },
  { code: 'CA', name: 'Canada', dialCode: '+1' },
  { code: 'CV', name: 'Cape Verde', dialCode: '+238' },
  { code: 'CF', name: 'Central African Republic', dialCode: '+236' },
  { code: 'TD', name: 'Chad', dialCode: '+235' },
  { code: 'CL', name: 'Chile', dialCode: '+56' },
  { code: 'CN', name: 'China', dialCode: '+86' },
  { code: 'CO', name: 'Colombia', dialCode: '+57' },
  { code: 'KM', name: 'Comoros', dialCode: '+269' },
  { code: 'CG', name: 'Congo', dialCode: '+242' },
  { code: 'CD', name: 'Congo, Democratic Republic', dialCode: '+243' },
  { code: 'CR', name: 'Costa Rica', dialCode: '+506' },
  { code: 'HR', name: 'Croatia', dialCode: '+385' },
  { code: 'CU', name: 'Cuba', dialCode: '+53' },
  { code: 'CY', name: 'Cyprus', dialCode: '+357' },
  { code: 'CZ', name: 'Czech Republic', dialCode: '+420' },
  { code: 'DK', name: 'Denmark', dialCode: '+45' },
  { code: 'DJ', name: 'Djibouti', dialCode: '+253' },
  { code: 'DO', name: 'Dominican Republic', dialCode: '+1' },
  { code: 'EC', name: 'Ecuador', dialCode: '+593' },
  { code: 'EG', name: 'Egypt', dialCode: '+20' },
  { code: 'SV', name: 'El Salvador', dialCode: '+503' },
  { code: 'GQ', name: 'Equatorial Guinea', dialCode: '+240' },
  { code: 'ER', name: 'Eritrea', dialCode: '+291' },
  { code: 'EE', name: 'Estonia', dialCode: '+372' },
  { code: 'ET', name: 'Ethiopia', dialCode: '+251' },
  { code: 'FJ', name: 'Fiji', dialCode: '+679' },
  { code: 'FI', name: 'Finland', dialCode: '+358' },
  { code: 'FR', name: 'France', dialCode: '+33' },
  { code: 'GA', name: 'Gabon', dialCode: '+241' },
  { code: 'GM', name: 'Gambia', dialCode: '+220' },
  { code: 'GE', name: 'Georgia', dialCode: '+995' },
  { code: 'DE', name: 'Germany', dialCode: '+49' },
  { code: 'GH', name: 'Ghana', dialCode: '+233' },
  { code: 'GR', name: 'Greece', dialCode: '+30' },
  { code: 'GT', name: 'Guatemala', dialCode: '+502' },
  { code: 'GN', name: 'Guinea', dialCode: '+224' },
  { code: 'GW', name: 'Guinea-Bissau', dialCode: '+245' },
  { code: 'GY', name: 'Guyana', dialCode: '+592' },
  { code: 'HT', name: 'Haiti', dialCode: '+509' },
  { code: 'HN', name: 'Honduras', dialCode: '+504' },
  { code: 'HK', name: 'Hong Kong', dialCode: '+852' },
  { code: 'HU', name: 'Hungary', dialCode: '+36' },
  { code: 'IS', name: 'Iceland', dialCode: '+354' },
  { code: 'IN', name: 'India', dialCode: '+91' },
  { code: 'ID', name: 'Indonesia', dialCode: '+62' },
  { code: 'IR', name: 'Iran', dialCode: '+98' },
  { code: 'IQ', name: 'Iraq', dialCode: '+964' },
  { code: 'IE', name: 'Ireland', dialCode: '+353' },
  { code: 'IL', name: 'Israel', dialCode: '+972' },
  { code: 'IT', name: 'Italy', dialCode: '+39' },
  { code: 'JM', name: 'Jamaica', dialCode: '+1' },
  { code: 'JP', name: 'Japan', dialCode: '+81' },
  { code: 'JO', name: 'Jordan', dialCode: '+962' },
  { code: 'KZ', name: 'Kazakhstan', dialCode: '+7' },
  { code: 'KE', name: 'Kenya', dialCode: '+254' },
  { code: 'KI', name: 'Kiribati', dialCode: '+686' },
  { code: 'KP', name: 'North Korea', dialCode: '+850' },
  { code: 'KR', name: 'South Korea', dialCode: '+82' },
  { code: 'KW', name: 'Kuwait', dialCode: '+965' },
  { code: 'KG', name: 'Kyrgyzstan', dialCode: '+996' },
  { code: 'LA', name: 'Laos', dialCode: '+856' },
  { code: 'LV', name: 'Latvia', dialCode: '+371' },
  { code: 'LB', name: 'Lebanon', dialCode: '+961' },
  { code: 'LS', name: 'Lesotho', dialCode: '+266' },
  { code: 'LR', name: 'Liberia', dialCode: '+231' },
  { code: 'LY', name: 'Libya', dialCode: '+218' },
  { code: 'LI', name: 'Liechtenstein', dialCode: '+423' },
  { code: 'LT', name: 'Lithuania', dialCode: '+370' },
  { code: 'LU', name: 'Luxembourg', dialCode: '+352' },
  { code: 'MK', name: 'Macedonia', dialCode: '+389' },
  { code: 'MG', name: 'Madagascar', dialCode: '+261' },
  { code: 'MW', name: 'Malawi', dialCode: '+265' },
  { code: 'MY', name: 'Malaysia', dialCode: '+60' },
  { code: 'MV', name: 'Maldives', dialCode: '+960' },
  { code: 'ML', name: 'Mali', dialCode: '+223' },
  { code: 'MT', name: 'Malta', dialCode: '+356' },
  { code: 'MH', name: 'Marshall Islands', dialCode: '+692' },
  { code: 'MR', name: 'Mauritania', dialCode: '+222' },
  { code: 'MU', name: 'Mauritius', dialCode: '+230' },
  { code: 'MX', name: 'Mexico', dialCode: '+52' },
  { code: 'FM', name: 'Micronesia', dialCode: '+691' },
  { code: 'MD', name: 'Moldova', dialCode: '+373' },
  { code: 'MC', name: 'Monaco', dialCode: '+377' },
  { code: 'MN', name: 'Mongolia', dialCode: '+976' },
  { code: 'ME', name: 'Montenegro', dialCode: '+382' },
  { code: 'MA', name: 'Morocco', dialCode: '+212' },
  { code: 'MZ', name: 'Mozambique', dialCode: '+258' },
  { code: 'MM', name: 'Myanmar', dialCode: '+95' },
  { code: 'NA', name: 'Namibia', dialCode: '+264' },
  { code: 'NR', name: 'Nauru', dialCode: '+674' },
  { code: 'NP', name: 'Nepal', dialCode: '+977' },
  { code: 'NL', name: 'Netherlands', dialCode: '+31' },
  { code: 'NZ', name: 'New Zealand', dialCode: '+64' },
  { code: 'NI', name: 'Nicaragua', dialCode: '+505' },
  { code: 'NE', name: 'Niger', dialCode: '+227' },
  { code: 'NG', name: 'Nigeria', dialCode: '+234' },
  { code: 'NO', name: 'Norway', dialCode: '+47' },
  { code: 'OM', name: 'Oman', dialCode: '+968' },
  { code: 'PK', name: 'Pakistan', dialCode: '+92' },
  { code: 'PW', name: 'Palau', dialCode: '+680' },
  { code: 'PA', name: 'Panama', dialCode: '+507' },
  { code: 'PG', name: 'Papua New Guinea', dialCode: '+675' },
  { code: 'PY', name: 'Paraguay', dialCode: '+595' },
  { code: 'PE', name: 'Peru', dialCode: '+51' },
  { code: 'PH', name: 'Philippines', dialCode: '+63' },
  { code: 'PL', name: 'Poland', dialCode: '+48' },
  { code: 'PT', name: 'Portugal', dialCode: '+351' },
  { code: 'QA', name: 'Qatar', dialCode: '+974' },
  { code: 'RO', name: 'Romania', dialCode: '+40' },
  { code: 'RU', name: 'Russia', dialCode: '+7' },
  { code: 'RW', name: 'Rwanda', dialCode: '+250' },
  { code: 'WS', name: 'Samoa', dialCode: '+685' },
  { code: 'SM', name: 'San Marino', dialCode: '+378' },
  { code: 'ST', name: 'Sao Tome and Principe', dialCode: '+239' },
  { code: 'SA', name: 'Saudi Arabia', dialCode: '+966' },
  { code: 'SN', name: 'Senegal', dialCode: '+221' },
  { code: 'RS', name: 'Serbia', dialCode: '+381' },
  { code: 'SC', name: 'Seychelles', dialCode: '+248' },
  { code: 'SL', name: 'Sierra Leone', dialCode: '+232' },
  { code: 'SG', name: 'Singapore', dialCode: '+65' },
  { code: 'SK', name: 'Slovakia', dialCode: '+421' },
  { code: 'SI', name: 'Slovenia', dialCode: '+386' },
  { code: 'SB', name: 'Solomon Islands', dialCode: '+677' },
  { code: 'SO', name: 'Somalia', dialCode: '+252' },
  { code: 'ZA', name: 'South Africa', dialCode: '+27' },
  { code: 'SS', name: 'South Sudan', dialCode: '+211' },
  { code: 'ES', name: 'Spain', dialCode: '+34' },
  { code: 'LK', name: 'Sri Lanka', dialCode: '+94' },
  { code: 'SD', name: 'Sudan', dialCode: '+249' },
  { code: 'SR', name: 'Suriname', dialCode: '+597' },
  { code: 'SZ', name: 'Swaziland', dialCode: '+268' },
  { code: 'SE', name: 'Sweden', dialCode: '+46' },
  { code: 'CH', name: 'Switzerland', dialCode: '+41' },
  { code: 'SY', name: 'Syria', dialCode: '+963' },
  { code: 'TW', name: 'Taiwan', dialCode: '+886' },
  { code: 'TJ', name: 'Tajikistan', dialCode: '+992' },
  { code: 'TZ', name: 'Tanzania', dialCode: '+255' },
  { code: 'TH', name: 'Thailand', dialCode: '+66' },
  { code: 'TL', name: 'Timor-Leste', dialCode: '+670' },
  { code: 'TG', name: 'Togo', dialCode: '+228' },
  { code: 'TO', name: 'Tonga', dialCode: '+676' },
  { code: 'TT', name: 'Trinidad and Tobago', dialCode: '+1868' },
  { code: 'TN', name: 'Tunisia', dialCode: '+216' },
  { code: 'TR', name: 'Turkey', dialCode: '+90' },
  { code: 'TM', name: 'Turkmenistan', dialCode: '+993' },
  { code: 'TV', name: 'Tuvalu', dialCode: '+688' },
  { code: 'UG', name: 'Uganda', dialCode: '+256' },
  { code: 'UA', name: 'Ukraine', dialCode: '+380' },
  { code: 'AE', name: 'United Arab Emirates', dialCode: '+971' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44' },
  { code: 'US', name: 'United States', dialCode: '+1' },
  { code: 'UY', name: 'Uruguay', dialCode: '+598' },
  { code: 'UZ', name: 'Uzbekistan', dialCode: '+998' },
  { code: 'VU', name: 'Vanuatu', dialCode: '+678' },
  { code: 'VA', name: 'Vatican City', dialCode: '+379' },
  { code: 'VE', name: 'Venezuela', dialCode: '+58' },
  { code: 'VN', name: 'Vietnam', dialCode: '+84' },
  { code: 'YE', name: 'Yemen', dialCode: '+967' },
  { code: 'ZM', name: 'Zambia', dialCode: '+260' },
  { code: 'ZW', name: 'Zimbabwe', dialCode: '+263' },
];

// Generate days, months, and years for date picker
const days = Array.from({ length: 31 }, (_, i) => i + 1);
const months = [
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];
const currentYear = new Date().getFullYear();
const years = Array.from({ length: 100 }, (_, i) => currentYear - i);

// Form section definitions
const SECTIONS = {
  PROFILE: 'profile',
  PERSONAL: 'personal',
  CONTACT: 'contact',
  ADDRESS: 'address'
};

const SharedProfileForm = ({
  initialData = {},
  onSubmit,
  loading: isLoading = false,
  requiredFields = [],
  buttonText = 'Save',
  theme: propTheme,
  nonEditableFields = [] // Add this new prop with empty array as default
}) => {
  // Use provided theme or get default theme
  const theme = propTheme || getThemeForRole('default');

  // References for animations
  const fadeAnims = useRef({
    [SECTIONS.PROFILE]: new Animated.Value(1),
    [SECTIONS.PERSONAL]: new Animated.Value(1),
    [SECTIONS.CONTACT]: new Animated.Value(1),
    [SECTIONS.ADDRESS]: new Animated.Value(1)
  }).current;

  // State to track open/closed sections
  const [expandedSections, setExpandedSections] = useState({
    [SECTIONS.PROFILE]: true,
    [SECTIONS.PERSONAL]: true,
    [SECTIONS.CONTACT]: true,
    [SECTIONS.ADDRESS]: true
  });

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    countryCode: 'US',
    dialCode: '+1',
    address: '',
    city: '',
    dateOfBirth: '',
    gender: '',
    ...initialData
  });

  const [cities, setCities] = useState([]);
  const [loadingCities, setLoadingCities] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [datePickerValues, setDatePickerValues] = useState({
    day: 1,
    month: 1,
    year: currentYear - 30
  });
  const [countrySearch, setCountrySearch] = useState('');

  // Filter countries based on search
  const filteredCountries = countrySearch.length > 0
    ? countries.filter(country =>
        country.name.toLowerCase().includes(countrySearch.toLowerCase()))
    : countries;

  // Function to toggle section expansion
  const toggleSection = (section) => {
    // Update section state
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));

    // Animate the transition
    Animated.timing(fadeAnims[section], {
      toValue: expandedSections[section] ? 0 : 1,
      duration: 300,
      easing: Easing.ease,
      useNativeDriver: true
    }).start();
  };

  useEffect(() => {
    setFormData(prev => ({ ...prev, ...initialData }));

    // Parse date of birth if it exists
    if (initialData.dateOfBirth) {
      try {
        // Try to parse dates in MM/DD/YYYY format
        const parts = initialData.dateOfBirth.split('/');
        if (parts.length === 3) {
          setDatePickerValues({
            day: parseInt(parts[1], 10),
            month: parseInt(parts[0], 10),
            year: parseInt(parts[2], 10)
          });
        }
      } catch (error) {
        console.error('Error parsing date:', error);
      }
    }

    // Load profile image from local storage
    loadProfileImage();
  }, [initialData]);

  // Function to load profile image from local storage
  const loadProfileImage = async () => {
    try {
      const savedImageUri = await AsyncStorage.getItem(PROFILE_IMAGE_STORAGE_KEY);
      if (savedImageUri) {
        setProfileImage(savedImageUri);
      }
    } catch (error) {
      console.error('Error loading profile image:', error);
    }
  };

  // Function to save profile image to local storage and Firebase
  const saveProfileImage = async (imageUri) => {
    try {
      // Save locally first
      await AsyncStorage.setItem(PROFILE_IMAGE_STORAGE_KEY, imageUri);
      console.log('Image saved locally');

      try {
        // Import Firebase storage functions
        const { storage, ref, uploadBytes, getDownloadURL } = require('../../config/firebase');
        const { auth, updateProfile } = require('../../config/firebase');

        // Check if user is authenticated
        const currentUser = auth.currentUser;
        if (!currentUser) {
          console.log('No authenticated user found');
          showMessage({
            message: 'Success',
            description: 'Profile image saved locally only (not logged in)',
            type: 'success',
            backgroundColor: COLORS.primary,
          });
          return;
        }

        console.log('Uploading profile image to Firebase for user:', currentUser.uid);

        // Convert image to blob
        const response = await fetch(imageUri);
        const blob = await response.blob();

        // Create storage reference
        const storageRef = ref(storage, `profile_images/${currentUser.uid}`);

        // Upload image
        console.log('Starting upload...');
        await uploadBytes(storageRef, blob);
        console.log('Upload complete');

        // Get download URL
        const downloadURL = await getDownloadURL(storageRef);
        console.log('Download URL obtained:', downloadURL);

        // Update user profile in Firebase Auth
        await updateProfile(currentUser, { photoURL: downloadURL });
        console.log('User profile updated in Firebase Auth');

        showMessage({
          message: 'Success',
          description: 'Profile image saved and uploaded successfully',
          type: 'success',
          backgroundColor: COLORS.primary,
        });

        return downloadURL;
      } catch (firebaseError) {
        console.error('Error uploading to Firebase:', firebaseError);
        console.error('Error details:', firebaseError.message);

        // Continue with local storage only
        showMessage({
          message: 'Warning',
          description: 'Image saved locally but could not be uploaded to cloud',
          type: 'warning',
          backgroundColor: '#FFA500',
        });
      }
    } catch (error) {
      console.error('Error saving profile image:', error);
      showMessage({
        message: 'Error',
        description: 'Unable to save profile image',
        type: 'danger',
        backgroundColor: COLORS.error,
      });
    }
  };

  // Function to handle image selection
  const handleImageSelection = (image) => {
    setSelectedImage(image);
  };

  // Function to confirm image selection
  const confirmImageSelection = () => {
    if (selectedImage) {
      setProfileImage(selectedImage.uri);
      saveProfileImage(selectedImage.uri);
      setShowImagePickerModal(false);

      Alert.alert(
        'Image Updated',
        'Your profile picture has been successfully updated.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatPhoneNumber = (phoneNumber) => {
    // Remove all non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Apply different formatting based on country
    if (formData.countryCode === 'US') {
      // Format as (XXX) XXX-XXXX for US
      if (cleaned.length <= 3) {
        return cleaned;
      } else if (cleaned.length <= 6) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
      } else {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      }
    } else {
      // Simple formatting for other countries - just group digits
      if (cleaned.length <= 3) {
        return cleaned;
      } else if (cleaned.length <= 7) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
      } else {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7, 11)}`;
      }
    }
  };

  const handlePhoneChange = (value) => {
    const formatted = formatPhoneNumber(value);
    handleChange('phone', formatted);
  };

  const handleDateSelection = () => {
    const { day, month, year } = datePickerValues;
    // Format as MM/DD/YYYY
    const formattedDate = `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`;
    handleChange('dateOfBirth', formattedDate);
    setShowDateModal(false);
  };

  const fetchCities = async (countryCode) => {
    setLoadingCities(true);
    try {
      const country = countries.find(c => c.code === countryCode);
      const response = await axios.post('https://countriesnow.space/api/v0.1/countries/cities', {
        country: country?.name
      });

      if (response.data && response.data.data) {
        setCities(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load cities',
        type: 'danger',
      });
    } finally {
      setLoadingCities(false);
    }
  };

  const handleCountryChange = (countryCode) => {
    const country = countries.find(c => c.code === countryCode);
    if (country) {
      setFormData(prev => ({
        ...prev,
        countryCode: country.code,
        dialCode: country.dialCode,
        city: '' // Reset city when country changes
      }));
      fetchCities(countryCode);
    }
  };

  const renderField = (label, field, placeholder) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.label}>
        {label}
        {requiredFields.includes(field) && <Text style={styles.required}>*</Text>}
        {nonEditableFields.includes(field) && <Text style={styles.nonEditable}> (Non-editable)</Text>}
      </Text>
      <TextInput
        style={[
          styles.input,
          nonEditableFields.includes(field) && styles.inputDisabled
        ]}
        value={formData[field]}
        onChangeText={(value) => handleChange(field, value)}
        placeholder={placeholder}
        placeholderTextColor="#999"
        editable={!nonEditableFields.includes(field)}
      />
    </View>
  );

  const validateForm = () => {
    const errors = {};
    let isValid = true;

    // Check required fields
    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        errors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
        isValid = false;
      }
    });

    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    // Phone validation
    if (formData.phone && !/^\d{8,15}$/.test(formData.phone.replace(/\D/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
      isValid = false;
    }

    if (!isValid) {
      // Show first error message
      const firstError = Object.values(errors)[0];
      showMessage({
        message: 'Validation Error',
        description: firstError,
        type: 'danger',
        backgroundColor: COLORS.error,
      });
    }

    return isValid;
  };

  const handleFormSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Component for section header
  const SectionHeader = ({ title, icon, section, iconProvider }) => {
    const IconComponent = iconProvider || MaterialIcons;

    return (
      <TouchableOpacity
        style={styles.sectionHeader}
        onPress={() => toggleSection(section)}
        activeOpacity={0.7}
      >
        <View style={styles.sectionHeaderContent}>
          <IconComponent name={icon} size={24} color={theme.colors.primary} style={styles.sectionIcon} />
          <Text style={styles.sectionTitle}>{title}</Text>
        </View>
        <MaterialIcons
          name={expandedSections[section] ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
          size={24}
          color={theme.colors.primary}
        />
      </TouchableOpacity>
    );
  };

  // Component for section content
  const SectionContent = ({ section, children }) => {
    return (
      <Animated.View
        style={[
          styles.sectionContent,
          {
            opacity: fadeAnims[section],
            display: expandedSections[section] ? 'flex' : 'none',
          }
        ]}
      >
        {children}
      </Animated.View>
    );
  };

  // Function to render the profile image section
  const renderProfileImageSection = () => (
    <View style={styles.profileImageSection}>
      <View style={styles.profileImageContainer}>
        {profileImage ? (
          <Image
            source={{ uri: profileImage }}
            style={styles.profileImage}
          />
        ) : (
          <View style={styles.profileImagePlaceholder}>
            <Text style={styles.profileImagePlaceholderText}>
              {formData.firstName && formData.lastName
                ? `${formData.firstName.charAt(0)}${formData.lastName.charAt(0)}`
                : 'U'}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.editImageButton}
          onPress={() => setShowImagePickerModal(true)}
        >
          <Ionicons name="camera" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.form}>
        {/* Profile Picture Section */}
        <View style={styles.card}>
          <SectionHeader
            title="Profile Picture"
            icon="account-circle"
            section={SECTIONS.PROFILE}
            iconProvider={MaterialIcons}
          />
          <SectionContent section={SECTIONS.PROFILE}>
            {renderProfileImageSection()}
          </SectionContent>
        </View>

        {/* Personal Information Section */}
        <View style={styles.card}>
          <SectionHeader
            title="Personal Information"
            icon="person"
            section={SECTIONS.PERSONAL}
            iconProvider={MaterialIcons}
          />
          <SectionContent section={SECTIONS.PERSONAL}>
            {renderField('First Name', 'firstName', 'Enter your first name')}
            {renderField('Last Name', 'lastName', 'Enter your last name')}
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Date of Birth{requiredFields.includes('dateOfBirth') && <Text style={styles.required}>*</Text>}</Text>
              <TouchableOpacity
                style={styles.input}
                onPress={() => setShowDateModal(true)}
              >
                <Text style={formData.dateOfBirth ? styles.dateText : styles.placeholderText}>
                  {formData.dateOfBirth || 'Select Date of Birth'}
                </Text>
              </TouchableOpacity>
            </View>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Gender{requiredFields.includes('gender') && <Text style={styles.required}>*</Text>}</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={formData.gender}
                  style={styles.picker}
                  onValueChange={(value) => handleChange('gender', value)}
                >
                  <Picker.Item label="Select Gender" value="" />
                  <Picker.Item label="Male" value="male" />
                  <Picker.Item label="Female" value="female" />
                </Picker>
              </View>
            </View>
          </SectionContent>
        </View>

        {/* Contact Information Section */}
        <View style={styles.card}>
          <SectionHeader
            title="Contact Information"
            icon="contact-phone"
            section={SECTIONS.CONTACT}
            iconProvider={MaterialIcons}
          />
          <SectionContent section={SECTIONS.CONTACT}>
            {renderField('Email', 'email', 'Enter your email')}

            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Phone Number{requiredFields.includes('phone') && <Text style={styles.required}>*</Text>}</Text>
              <View style={styles.phoneContainer}>
                <View style={styles.dialCode}>
                  <CountryFlag isoCode={formData.countryCode} size={20} />
                  <Text style={styles.dialCodeText}>{formData.dialCode}</Text>
                </View>
                <TextInput
                  style={styles.phoneInput}
                  value={formData.phone}
                  onChangeText={handlePhoneChange}
                  placeholder="Enter phone number"
                  placeholderTextColor="#999"
                  keyboardType="phone-pad"
                />
              </View>
            </View>
          </SectionContent>
        </View>

        {/* Address Section */}
        <View style={styles.card}>
          <SectionHeader
            title="Address"
            icon="location-on"
            section={SECTIONS.ADDRESS}
            iconProvider={MaterialIcons}
          />
          <SectionContent section={SECTIONS.ADDRESS}>
            <View style={styles.fieldContainer}>
              <Text style={styles.label}>Country{requiredFields.includes('countryCode') && <Text style={styles.required}>*</Text>}</Text>
              <TouchableOpacity
                style={styles.input}
                onPress={() => setShowCountryModal(true)}
              >
                <View style={styles.selectedCountry}>
                  {formData.countryCode && (
                    <CountryFlag isoCode={formData.countryCode} size={20} style={styles.countryFlag} />
                  )}
                  <Text style={styles.countryText}>
                    {formData.countryCode ? countries.find(c => c.code === formData.countryCode)?.name : 'Select Country'}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.fieldContainer}>
              <Text style={styles.label}>City{requiredFields.includes('city') && <Text style={styles.required}>*</Text>}</Text>
              <View style={styles.pickerContainer}>
                {loadingCities ? (
                  <ActivityIndicator style={styles.cityLoading} color="rgba(16, 107, 0, 1)" />
                ) : (
                  <Picker
                    selectedValue={formData.city}
                    style={styles.picker}
                    onValueChange={(value) => handleChange('city', value)}
                    enabled={cities.length > 0}
                  >
                    <Picker.Item label="Select City" value="" />
                    {cities.map((city, index) => (
                      <Picker.Item key={index} label={city} value={city} />
                    ))}
                  </Picker>
                )}
              </View>
            </View>

            {renderField('Address', 'address', 'Enter your address')}
          </SectionContent>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: theme?.colors.primary },
              (isLoading) && styles.submitButtonDisabled
            ]}
            onPress={handleFormSubmit}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFF" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>{buttonText}</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Date Picker Modal */}
      <Modal
        visible={showDateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Date of Birth</Text>

            <View style={styles.datePickerContainer}>
              {/* Month Picker */}
              <View style={styles.datePickerColumn}>
                <Text style={styles.datePickerLabel}>Month</Text>
                <Picker
                  selectedValue={datePickerValues.month}
                  style={styles.datePicker}
                  onValueChange={(value) => setDatePickerValues(prev => ({ ...prev, month: value }))}
                >
                  {months.map(month => (
                    <Picker.Item key={month.value} label={month.label} value={month.value} />
                  ))}
                </Picker>
              </View>

              {/* Day Picker */}
              <View style={styles.datePickerColumn}>
                <Text style={styles.datePickerLabel}>Day</Text>
                <Picker
                  selectedValue={datePickerValues.day}
                  style={styles.datePicker}
                  onValueChange={(value) => setDatePickerValues(prev => ({ ...prev, day: value }))}
                >
                  {days.map(day => (
                    <Picker.Item key={day} label={day.toString()} value={day} />
                  ))}
                </Picker>
              </View>

              {/* Year Picker */}
              <View style={styles.datePickerColumn}>
                <Text style={styles.datePickerLabel}>Year</Text>
                <Picker
                  selectedValue={datePickerValues.year}
                  style={styles.datePicker}
                  onValueChange={(value) => setDatePickerValues(prev => ({ ...prev, year: value }))}
                >
                  {years.map(year => (
                    <Picker.Item key={year} label={year.toString()} value={year} />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButtonCancel}
                onPress={() => setShowDateModal(false)}
              >
                <Text style={styles.modalButtonTextCancel}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalButtonConfirm}
                onPress={handleDateSelection}
              >
                <Text style={styles.modalButtonTextConfirm}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Country Picker Modal */}
      <Modal
        visible={showCountryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCountryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, styles.countryModalContent]}>
            <Text style={styles.modalTitle}>Select Country</Text>

            <TextInput
              style={[styles.input, styles.searchInput]}
              value={countrySearch}
              onChangeText={setCountrySearch}
              placeholder="Search countries..."
              placeholderTextColor="#999"
              autoFocus
            />

            <ScrollView style={styles.countryList}>
              {filteredCountries.map((country) => (
                <TouchableOpacity
                  key={country.code}
                  style={[
                    styles.countryItem,
                    formData.countryCode === country.code && styles.countryItemSelected
                  ]}
                  onPress={() => {
                    handleCountryChange(country.code);
                    setShowCountryModal(false);
                    setCountrySearch('');
                  }}
                >
                  <CountryFlag isoCode={country.code} size={24} style={styles.countryItemFlag} />
                  <View style={styles.countryItemInfo}>
                    <Text style={styles.countryItemName}>{country.name}</Text>
                    <Text style={styles.countryItemCode}>{country.dialCode}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={styles.modalButtonCancel}
              onPress={() => {
                setShowCountryModal(false);
                setCountrySearch('');
              }}
            >
              <Text style={styles.modalButtonTextCancel}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Profile image selection modal */}
      <Modal
        visible={showImagePickerModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImagePickerModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.profileModalContent}>
            <View style={styles.modalHeaderBar}>
              <Text style={styles.modalTitle}>Choose Profile Picture</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowImagePickerModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>



            <View style={styles.modalInstructions}>
              <Text style={styles.modalInstructionsText}>
                Select a method to set your profile picture
              </Text>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.profileModalButton}
                onPress={async () => {
                  try {
                    // Request camera permissions
                    const { status } = await ImagePicker.requestCameraPermissionsAsync();
                    if (status !== 'granted') {
                      Alert.alert('Permission Required', 'Camera permission is needed to take photos');
                      return;
                    }

                    // Launch camera
                    const result = await ImagePicker.launchCameraAsync({
                      allowsEditing: true,
                      aspect: [1, 1],
                      quality: 0.5,
                    });

                    if (!result.canceled) {
                      const imageUri = result.assets[0].uri;
                      setSelectedImage({ uri: imageUri, id: Date.now() });
                      setProfileImage(imageUri);
                      saveProfileImage(imageUri);
                      setShowImagePickerModal(false);
                    }
                  } catch (error) {
                    console.error('Error taking photo:', error);
                    Alert.alert('Error', 'Failed to take photo: ' + error.message);
                  }
                }}
              >
                <View style={styles.profileButtonIconContainer}>
                  <Ionicons name="camera-outline" size={32} color="#555555" />
                </View>
                <Text style={styles.profileModalButtonText}>Take Photo</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileModalButton}
                onPress={async () => {
                  try {
                    // Request media library permissions
                    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
                    if (status !== 'granted') {
                      Alert.alert('Permission Required', 'Photo library permission is needed to select images');
                      return;
                    }

                    // Launch gallery
                    const result = await ImagePicker.launchImageLibraryAsync({
                      mediaTypes: ImagePicker.MediaTypeOptions.Images,
                      allowsEditing: true,
                      aspect: [1, 1],
                      quality: 0.5,
                    });

                    if (!result.canceled) {
                      const imageUri = result.assets[0].uri;
                      setSelectedImage({ uri: imageUri, id: Date.now() });
                      setProfileImage(imageUri);
                      saveProfileImage(imageUri);
                      setShowImagePickerModal(false);
                    }
                  } catch (error) {
                    console.error('Error picking image:', error);
                    Alert.alert('Error', 'Failed to pick image: ' + error.message);
                  }
                }}
              >
                <View style={styles.profileButtonIconContainer}>
                  <Ionicons name="image-outline" size={32} color="#555555" />
                </View>
                <Text style={styles.profileModalButtonText}>Gallery</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowImagePickerModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5', // Light gray background to contrast with cards
  },
  form: {
    padding: 15,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  sectionContent: {
    padding: 15,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  required: {
    color: 'rgba(16, 107, 0, 1)',
    marginLeft: 2,
  },
  input: {
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    justifyContent: 'center',
  },
  dateText: {
    color: '#000',
  },
  placeholderText: {
    color: '#999',
  },
  pickerContainer: {
    backgroundColor: '#f8f8f8',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  phoneContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  dialCode: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryLighter,
    paddingHorizontal: 10,
    paddingVertical: 12,
    borderRightWidth: 1,
    borderRightColor: '#ddd',
  },
  dialCodeText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
  },
  phoneInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  helperText: {
    fontSize: 12,
    color: '#777',
    marginTop: 4,
    marginLeft: 4,
  },
  cityLoading: {
    padding: 15,
  },
  buttonContainer: {
    marginTop: 30,
    marginBottom: 20,
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  // Styles for profile image section
  profileImageSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  profileImageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 15,
  },
  profileImageContainer: {
    position: 'relative',
    width: 150, // Larger
    height: 150, // Larger
    borderRadius: 75,
    overflow: 'visible',
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 3,
    borderColor: '#FF5722',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
  },
  profileImagePlaceholder: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FF5722',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 8,
  },
  profileImagePlaceholderText: {
    fontSize: 50,
    fontWeight: 'bold',
    color: '#FF5722',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: '#FF5722', // Bright orange for better visibility
    width: 45, // Slightly larger
    height: 45, // Slightly larger
    borderRadius: 23,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8, // More elevation for a more pronounced effect
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    borderWidth: 2,
    borderColor: '#FFF', // White border for contrast
  },
  // Styles for no images container
  noImagesContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    marginVertical: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    height: 200,
  },
  noImagesText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 10,
  },
  noImagesSubText: {
    fontSize: 14,
    color: '#9e9e9e',
    textAlign: 'center',
    marginTop: 5,
  },
  modalButtonDisabled: {
    opacity: 0.6,
    backgroundColor: '#E0E0E0',
    borderWidth: 1,
    borderColor: '#BDBDBD',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 25,
    width: '90%',
    maxWidth: 450,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.5,
    shadowRadius: 15,
    elevation: 15,
    minHeight: 300,
  },
  // Profile modal specific styles
  profileModalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxWidth: 450,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  modalHeaderBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalDivider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    width: '100%',
  },
  modalInstructions: {
    padding: 15,
    alignItems: 'center',
  },
  modalInstructionsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  closeButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 0.5,
  },
  datePickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  datePickerColumn: {
    flex: 1,
    alignItems: 'center',
  },
  datePickerLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  datePicker: {
    width: '100%',
    height: 120,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    marginTop: 10,
    paddingHorizontal: 20,
  },
  modalButton: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    marginHorizontal: 5,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#fff',
    height: 100,
  },
  modalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  // Profile modal specific button styles
  profileModalButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 10,
    borderRadius: 12,
    height: 120,
    backgroundColor: '#ffffff',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  profileButtonIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  profileModalButtonText: {
    color: '#333333',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  cancelButton: {
    padding: 15,
    margin: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#555',
    fontWeight: '600',
  },
  modalButtonCancel: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    marginHorizontal: 5,
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  modalButtonConfirm: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    backgroundColor: '#FF5722', // Bright orange to match the camera button
    marginLeft: 8,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  modalButtonTextCancel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  modalButtonTextConfirm: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  inputFocus: {
    borderColor: COLORS.primary,
    borderWidth: 2,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownItemSelected: {
    backgroundColor: COLORS.primaryLighter,
  },
  addPhotoButton: {
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 20,
    position: 'absolute',
    bottom: 0,
    right: 0,
  },
  nonEditable: {
    color: '#888',
    fontSize: 12,
    fontStyle: 'italic',
  },
  inputDisabled: {
    backgroundColor: '#f0f0f0',
    color: '#666',
  },
  searchInput: {
    marginBottom: 8,
  },
  selectedCountry: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryFlag: {
    marginRight: 8,
  },
  countryText: {
    color: '#333',
  },
  countryModalContent: {
    padding: 20,
  },
  countryList: {
    maxHeight: 200,
  },
  countryItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryItemSelected: {
    backgroundColor: COLORS.primaryLighter,
  },
  countryItemFlag: {
    marginRight: 8,
  },
  countryItemInfo: {
    flexDirection: 'column',
  },
  countryItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  countryItemCode: {
    fontSize: 14,
    color: '#666',
  },
});

export default SharedProfileForm;