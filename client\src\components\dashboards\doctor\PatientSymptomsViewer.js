import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ROLE_COLORS } from '../../../config/theme';
import { firebaseDoctorPatientsService } from '../../../services/firebaseDoctorPatientsService';

const PatientSymptomsViewer = ({ patientId, patientName }) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [symptoms, setSymptoms] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSymptomLog, setSelectedSymptomLog] = useState(null);
  const [timeRange, setTimeRange] = useState('week');
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientSymptoms();
  }, [patientId]);

  const fetchPatientSymptoms = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch symptoms for the selected patient from Firebase
      const symptomsData = await firebaseDoctorPatientsService.getPatientSymptoms(patientId);
      console.log(`Fetched ${symptomsData.length} symptom logs for patient ${patientId}`);
      setSymptoms(symptomsData || []);
    } catch (error) {
      console.error('Error fetching patient symptoms from Firebase:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  const openSymptomDetails = (symptomLog) => {
    setSelectedSymptomLog(symptomLog);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    if (severity >= 8) return '#D32F2F'; // High severity - red
    if (severity >= 5) return '#FFA000'; // Medium severity - amber
    return '#388E3C'; // Low severity - green
  };

  // Get severity text
  const getSeverityText = (severity) => {
    if (severity >= 8) return 'High';
    if (severity >= 5) return 'Medium';
    return 'Low';
  };

  // Filter symptoms by time range
  const getFilteredSymptoms = () => {
    const now = new Date();
    const cutoffDate = new Date();

    if (timeRange === 'week') {
      cutoffDate.setDate(now.getDate() - 7);
    } else if (timeRange === 'month') {
      cutoffDate.setDate(now.getDate() - 30);
    } else {
      cutoffDate.setDate(now.getDate() - 90);
    }

    return symptoms.filter(log => new Date(log.timestamp) >= cutoffDate)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  };

  // Render the symptoms list
  const renderSymptomsList = () => {
    const filteredSymptoms = getFilteredSymptoms();

    if (filteredSymptoms.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text-outline" size={48} color={doctorColors.primary} />
          <Text style={styles.emptyText}>No symptom logs found for this time period</Text>
        </View>
      );
    }

    return (
      <View>
        {filteredSymptoms.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.symptomCard}
            onPress={() => openSymptomDetails(item)}
          >
            <View style={styles.symptomHeader}>
              <View style={styles.dateContainer}>
                <Ionicons name="calendar-outline" size={16} color="#666" />
                <Text style={styles.dateText}>{formatDate(item.timestamp)}</Text>
              </View>
              <View style={styles.moodContainer}>
                <Ionicons name="happy-outline" size={16} color="#666" />
                <Text style={styles.moodText}>{item.mood}</Text>
              </View>
            </View>

            <View style={styles.symptomsContainer}>
              {item.symptoms.slice(0, 2).map((symptom, index) => (
                <View key={index} style={styles.symptomItem}>
                  <View style={styles.symptomNameContainer}>
                    <Text style={styles.symptomName}>{symptom.type}</Text>
                    <View style={[styles.severityBadge, { backgroundColor: getSeverityColor(symptom.severity) }]}>
                      <Text style={styles.severityText}>{getSeverityText(symptom.severity)}</Text>
                    </View>
                  </View>
                  <Text style={styles.symptomDescription} numberOfLines={1}>
                    {symptom.description}
                  </Text>
                </View>
              ))}

              {item.symptoms.length > 2 && (
                <Text style={styles.moreSymptoms}>
                  +{item.symptoms.length - 2} more symptoms
                </Text>
              )}
            </View>

            {item.notes && (
              <Text style={styles.notes} numberOfLines={1}>
                Notes: {item.notes}
              </Text>
            )}

            <Ionicons name="chevron-forward" size={24} color="#ccc" style={styles.chevron} />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedSymptomLog) return null;

    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Symptom Log Details</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent} nestedScrollEnabled={true}>
              <View style={styles.modalDateContainer}>
                <Ionicons name="calendar" size={20} color={doctorColors.primary} />
                <Text style={styles.modalDateText}>
                  {formatDate(selectedSymptomLog.timestamp)}
                </Text>
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Mood</Text>
                <View style={styles.modalMoodContainer}>
                  <Ionicons name="happy" size={24} color="#666" />
                  <Text style={styles.modalMoodText}>{selectedSymptomLog.mood}</Text>
                </View>
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Symptoms</Text>
                {selectedSymptomLog.symptoms.map((symptom, index) => (
                  <View key={index} style={styles.modalSymptomItem}>
                    <View style={styles.modalSymptomHeader}>
                      <Text style={styles.modalSymptomName}>{symptom.type}</Text>
                      <View style={[styles.modalSeverityBadge, { backgroundColor: getSeverityColor(symptom.severity) }]}>
                        <Text style={styles.modalSeverityText}>
                          Severity: {symptom.severity}/10
                        </Text>
                      </View>
                    </View>
                    <Text style={styles.modalSymptomDescription}>
                      {symptom.description}
                    </Text>
                  </View>
                ))}
              </View>

              {selectedSymptomLog.activities && selectedSymptomLog.activities.length > 0 && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Activities</Text>
                  <View style={styles.modalActivitiesContainer}>
                    {selectedSymptomLog.activities.map((activity, index) => (
                      <View key={index} style={styles.modalActivityItem}>
                        <Ionicons name="fitness" size={16} color="#666" />
                        <Text style={styles.modalActivityText}>{activity}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {selectedSymptomLog.notes && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Notes</Text>
                  <Text style={styles.modalNotesText}>{selectedSymptomLog.notes}</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {patientName ? `${patientName}'s Symptoms` : 'Patient Symptoms'}
        </Text>
        <Text style={styles.headerSubtitle}>
          Track your patient's reported symptoms
        </Text>
      </View>

      {/* Time Range Selector */}
      <View style={styles.timeRangeContainer}>
        <Text style={styles.timeRangeLabel}>Time Range:</Text>
        <View style={styles.timeRangeButtons}>
          <TouchableOpacity
            style={[styles.timeRangeButton, timeRange === 'week' && styles.selectedTimeRange]}
            onPress={() => handleTimeRangeChange('week')}
          >
            <Text style={[styles.timeRangeText, timeRange === 'week' && styles.selectedTimeRangeText]}>
              Week
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.timeRangeButton, timeRange === 'month' && styles.selectedTimeRange]}
            onPress={() => handleTimeRangeChange('month')}
          >
            <Text style={[styles.timeRangeText, timeRange === 'month' && styles.selectedTimeRangeText]}>
              Month
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.timeRangeButton, timeRange === 'quarter' && styles.selectedTimeRange]}
            onPress={() => handleTimeRangeChange('quarter')}
          >
            <Text style={[styles.timeRangeText, timeRange === 'quarter' && styles.selectedTimeRangeText]}>
              3 Months
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading patient symptoms...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          {/* Symptoms List */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Symptom Logs</Text>
            {renderSymptomsList()}
          </View>
        </View>
      )}

      {renderDetailModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 12,
  },
  timeRangeLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  timeRangeButtons: {
    flexDirection: 'row',
  },
  timeRangeButton: {
    paddingHorizontal: 14,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  selectedTimeRange: {
    backgroundColor: '#f0f7ff',
    borderColor: ROLE_COLORS.doctor.primary,
  },
  timeRangeText: {
    fontSize: 13,
    color: '#666',
    fontWeight: '500',
  },
  selectedTimeRangeText: {
    color: ROLE_COLORS.doctor.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  symptomCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  symptomHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  moodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moodText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  symptomsContainer: {
    marginBottom: 8,
  },
  symptomItem: {
    marginBottom: 8,
  },
  symptomNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  symptomName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  severityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  severityText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  symptomDescription: {
    fontSize: 12,
    color: '#666',
  },
  moreSymptoms: {
    fontSize: 12,
    color: ROLE_COLORS.doctor.primary,
    fontStyle: 'italic',
  },
  notes: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  chevron: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 6,
  },
  modalContent: {
    padding: 16,
  },
  modalDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalDateText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
    fontWeight: 'bold',
  },
  modalSection: {
    marginBottom: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 14,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  modalMoodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalMoodText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  modalSymptomItem: {
    marginBottom: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#ccc',
    paddingLeft: 8,
  },
  modalSymptomHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  modalSymptomName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  modalSeverityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  modalSeverityText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  modalSymptomDescription: {
    fontSize: 14,
    color: '#666',
  },
  modalActivitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  modalActivityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eee',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  modalActivityText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 4,
  },
  modalNotesText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});

export default PatientSymptomsViewer;
