import { Platform } from 'react-native';

/**
 * Initialize Firebase Cloud Messaging
 * 
 * This function handles all the Firebase Cloud Messaging
 * initialization in a way that prevents the RNFBAppModule not found errors
 * during development.
 */
export const initializeMessaging = async () => {
  // Only proceed on native platforms
  if (Platform.OS !== 'android' && Platform.OS !== 'ios') {
    console.log('Skipping Firebase messaging on non-native platform');
    return { token: null, enabled: false };
  }

  try {
    // Check if the Firebase messaging module is available
    let messaging;
    try {
      // Import the messaging module dynamically
      messaging = require('@react-native-firebase/messaging').default;
    } catch (error) {
      console.log('Firebase messaging module not available:', error.message);
      return { token: null, enabled: false, error };
    }
    
    if (!messaging) {
      console.log('Firebase messaging module loaded but is undefined');
      return { token: null, enabled: false };
    }
    
    // Check for permissions
    const authStatus = await messaging().requestPermission();
    const enabled = 
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      
    if (!enabled) {
      console.log('Firebase messaging permissions not granted');
      return { token: null, enabled: false };
    }
    
    // Get FCM token
    const token = await messaging().getToken();
    console.log('Firebase token:', token ? token.substring(0, 10) + '...' : 'null');
    
    // Configure foreground notification handling
    messaging().onMessage(async remoteMessage => {
      console.log('Foreground message received:', remoteMessage);
      // Here you can add a custom notification display for foreground messages
    });
    
    // Return the token and enabled status
    return { token, enabled: true };
  } catch (error) {
    console.log('Error initializing Firebase messaging:', error.message);
    return { token: null, enabled: false, error };
  }
};

/**
 * Register the device's FCM token with your backend
 */
export const registerDeviceForNotifications = async (userId, token, apiToken) => {
  if (!token) {
    console.log('No token provided for registration');
    return false;
  }
  
  try {
    console.log('Registering device for user:', userId);
    
    // Make an API call to your backend to register the FCM token
    const response = await fetch('http://192.168.90.134:3000/api/users/register-device', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`
      },
      body: JSON.stringify({
        userId,
        token,
        device: Platform.OS
      })
    });
    
    const data = await response.json();
    console.log('Device registration response:', data);
    return data.success;
  } catch (error) {
    console.error('Error registering device for notifications:', error);
    return false;
  }
};

export default {
  initializeMessaging,
  registerDeviceForNotifications
}; 