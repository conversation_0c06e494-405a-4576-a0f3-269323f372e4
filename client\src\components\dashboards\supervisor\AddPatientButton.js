import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PatientScannerModal from '../../scanner/PatientScannerModal';
import { ROLE_COLORS } from '../../../config/theme';

const AddPatientButton = ({ onPatientAdded, headerStyle }) => {
  const [showScanner, setShowScanner] = useState(false);
  const supervisorColors = ROLE_COLORS.supervisor;

  const handlePatientAdded = (patient) => {
    if (onPatientAdded) {
      onPatientAdded(patient);
    }
  };

  return (
    <>
      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: headerStyle ? '#fff' : supervisorColors.primary },
          headerStyle && styles.headerButton
        ]}
        onPress={() => setShowScanner(true)}
      >
        <View style={styles.buttonContent}>
          <Ionicons
            name="person-add"
            size={headerStyle ? 16 : 20}
            color={headerStyle ? supervisorColors.primary : "#fff"}
          />
          <Text style={[
            styles.buttonText,
            headerStyle && { color: supervisorColors.primary }
          ]}>
            {headerStyle ? 'Add Patient' : 'Add Patient'}
          </Text>
        </View>
      </TouchableOpacity>

      <PatientScannerModal
        visible={showScanner}
        onClose={() => setShowScanner(false)}
        onSuccess={handlePatientAdded}
        scannerTitle="Add New Patient"
        relationshipType="supervisor-patient"
      />
    </>
  );
};

const styles = StyleSheet.create({
  addButton: {
    borderRadius: 8,
    padding: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  headerButton: {
    borderRadius: 20,
    padding: 6,
    paddingHorizontal: 10,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    maxWidth: 120,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'nowrap',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 4,
    fontSize: 12,
    letterSpacing: 0.2,
    flexShrink: 1,
  },
});

export default AddPatientButton;
