import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DashboardChart from '../DashboardChart';
import { localVitalsService } from '../../../services/localStorageService';
import { ROLE_COLORS } from '../../../config/theme';
import PatientSelector from './PatientSelector';

const { width } = Dimensions.get('window');

const PatientVitalsCharts = () => {
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('week');
  const [vitalsData, setVitalsData] = useState({
    heartRate: [],
    bloodPressure: [],
    bloodGlucose: [],
    weight: []
  });
  const doctorColors = ROLE_COLORS.doctor;

  // Fetch vitals data when patient or time range changes
  useEffect(() => {
    if (selectedPatient) {
      fetchPatientVitals();
    }
  }, [selectedPatient, timeRange]);

  const fetchPatientVitals = async () => {
    if (!selectedPatient) return;

    setLoading(true);
    try {
      // Fetch all vitals for the selected patient
      const allVitals = await localVitalsService.getPatientVitals(selectedPatient.id);
      
      // Filter by time range
      const filteredVitals = filterVitalsByTimeRange(allVitals);
      
      // Group by vital type
      const groupedVitals = {
        heartRate: filteredVitals.filter(v => v.vitalType === 'heartRate'),
        bloodPressure: filteredVitals.filter(v => v.vitalType === 'bloodPressure'),
        bloodGlucose: filteredVitals.filter(v => v.vitalType === 'bloodGlucose'),
        weight: filteredVitals.filter(v => v.vitalType === 'weight')
      };
      
      setVitalsData(groupedVitals);
    } catch (error) {
      console.error('Error fetching patient vitals:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterVitalsByTimeRange = (vitals) => {
    const now = new Date();
    const cutoffDate = new Date();

    if (timeRange === 'week') {
      cutoffDate.setDate(now.getDate() - 7);
    } else if (timeRange === 'month') {
      cutoffDate.setMonth(now.getMonth() - 1);
    } else if (timeRange === 'quarter') {
      cutoffDate.setMonth(now.getMonth() - 3);
    }

    return vitals.filter(vital => new Date(vital.timestamp) >= cutoffDate);
  };

  const handleSelectPatient = (patient) => {
    setSelectedPatient(patient);
  };

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  // Format date for chart labels
  const formatDateForLabel = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Prepare chart data for heart rate
  const prepareHeartRateChartData = () => {
    if (vitalsData.heartRate.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{ data: [0] }],
        legend: ['Heart Rate (bpm)']
      };
    }

    // Sort by timestamp
    const sortedData = [...vitalsData.heartRate].sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    return {
      labels: sortedData.map(item => formatDateForLabel(item.timestamp)),
      datasets: [
        {
          data: sortedData.map(item => item.values.value),
          color: (opacity = 1) => `rgba(234, 67, 53, ${opacity})`, // Red color for heart rate
          strokeWidth: 2
        }
      ],
      legend: ['Heart Rate (bpm)']
    };
  };

  // Prepare chart data for blood pressure
  const prepareBloodPressureChartData = () => {
    if (vitalsData.bloodPressure.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{ data: [0] }, { data: [0] }],
        legend: ['Systolic (mmHg)', 'Diastolic (mmHg)']
      };
    }

    // Sort by timestamp
    const sortedData = [...vitalsData.bloodPressure].sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    return {
      labels: sortedData.map(item => formatDateForLabel(item.timestamp)),
      datasets: [
        {
          data: sortedData.map(item => item.values.systolic),
          color: (opacity = 1) => `rgba(66, 133, 244, ${opacity})`, // Blue for systolic
          strokeWidth: 2
        },
        {
          data: sortedData.map(item => item.values.diastolic),
          color: (opacity = 1) => `rgba(66, 133, 244, ${opacity * 0.7})`, // Lighter blue for diastolic
          strokeWidth: 2
        }
      ],
      legend: ['Systolic (mmHg)', 'Diastolic (mmHg)']
    };
  };

  // Prepare chart data for blood glucose
  const prepareBloodGlucoseChartData = () => {
    if (vitalsData.bloodGlucose.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{ data: [0] }],
        legend: ['Blood Glucose (mg/dL)']
      };
    }

    // Sort by timestamp
    const sortedData = [...vitalsData.bloodGlucose].sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    return {
      labels: sortedData.map(item => formatDateForLabel(item.timestamp)),
      datasets: [
        {
          data: sortedData.map(item => item.values.value),
          color: (opacity = 1) => `rgba(251, 188, 5, ${opacity})`, // Yellow for blood glucose
          strokeWidth: 2
        }
      ],
      legend: ['Blood Glucose (mg/dL)']
    };
  };

  // Prepare chart data for weight
  const prepareWeightChartData = () => {
    if (vitalsData.weight.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{ data: [0] }],
        legend: ['Weight (kg)']
      };
    }

    // Sort by timestamp
    const sortedData = [...vitalsData.weight].sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    return {
      labels: sortedData.map(item => formatDateForLabel(item.timestamp)),
      datasets: [
        {
          data: sortedData.map(item => item.values.value),
          color: (opacity = 1) => `rgba(52, 168, 83, ${opacity})`, // Green for weight
          strokeWidth: 2
        }
      ],
      legend: ['Weight (kg)']
    };
  };

  // Render patient selector or charts
  const renderContent = () => {
    if (!selectedPatient) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Select a patient to view vitals charts</Text>
          <PatientSelector onSelectPatient={handleSelectPatient} />
        </View>
      );
    }

    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading patient vitals data...</Text>
        </View>
      );
    }

    return (
      <ScrollView style={styles.chartsContainer}>
        <View style={styles.patientInfoContainer}>
          <Text style={styles.patientName}>
            {selectedPatient.firstName} {selectedPatient.lastName}
          </Text>
          <TouchableOpacity
            style={styles.changePatientButton}
            onPress={() => setSelectedPatient(null)}
          >
            <Text style={styles.changePatientText}>Change Patient</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.timeRangeContainer}>
          <Text style={styles.timeRangeLabel}>Time Range:</Text>
          <View style={styles.timeRangeButtons}>
            <TouchableOpacity
              style={[styles.timeRangeButton, timeRange === 'week' && styles.selectedTimeRange]}
              onPress={() => handleTimeRangeChange('week')}
            >
              <Text style={[styles.timeRangeText, timeRange === 'week' && styles.selectedTimeRangeText]}>
                Week
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.timeRangeButton, timeRange === 'month' && styles.selectedTimeRange]}
              onPress={() => handleTimeRangeChange('month')}
            >
              <Text style={[styles.timeRangeText, timeRange === 'month' && styles.selectedTimeRangeText]}>
                Month
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.timeRangeButton, timeRange === 'quarter' && styles.selectedTimeRange]}
              onPress={() => handleTimeRangeChange('quarter')}
            >
              <Text style={[styles.timeRangeText, timeRange === 'quarter' && styles.selectedTimeRangeText]}>
                3 Months
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.chartSection}>
          <DashboardChart
            title="Heart Rate Trends"
            subtitle={`${timeRange === 'week' ? 'Last 7 days' : timeRange === 'month' ? 'Last 30 days' : 'Last 90 days'}`}
            data={prepareHeartRateChartData()}
            type="line"
            color="rgba(234, 67, 53, 1)"
            height={220}
          />
        </View>

        <View style={styles.chartSection}>
          <DashboardChart
            title="Blood Pressure Trends"
            subtitle={`${timeRange === 'week' ? 'Last 7 days' : timeRange === 'month' ? 'Last 30 days' : 'Last 90 days'}`}
            data={prepareBloodPressureChartData()}
            type="line"
            color="rgba(66, 133, 244, 1)"
            height={220}
          />
        </View>

        <View style={styles.chartSection}>
          <DashboardChart
            title="Blood Glucose Trends"
            subtitle={`${timeRange === 'week' ? 'Last 7 days' : timeRange === 'month' ? 'Last 30 days' : 'Last 90 days'}`}
            data={prepareBloodGlucoseChartData()}
            type="line"
            color="rgba(251, 188, 5, 1)"
            height={220}
          />
        </View>

        <View style={styles.chartSection}>
          <DashboardChart
            title="Weight Trends"
            subtitle={`${timeRange === 'week' ? 'Last 7 days' : timeRange === 'month' ? 'Last 30 days' : 'Last 90 days'}`}
            data={prepareWeightChartData()}
            type="line"
            color="rgba(52, 168, 83, 1)"
            height={220}
          />
        </View>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Patient Vitals Charts</Text>
        <Text style={styles.headerSubtitle}>Track vitals over time</Text>
      </View>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 10,
  },
  chartsContainer: {
    flex: 1,
    padding: 16,
  },
  patientInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  changePatientButton: {
    backgroundColor: ROLE_COLORS.doctor.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  changePatientText: {
    color: ROLE_COLORS.doctor.primary,
    fontSize: 12,
    fontWeight: '500',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  timeRangeLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  timeRangeButtons: {
    flexDirection: 'row',
  },
  timeRangeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginHorizontal: 4,
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  selectedTimeRange: {
    backgroundColor: '#f0f7ff',
    borderColor: ROLE_COLORS.doctor.primary,
  },
  timeRangeText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  selectedTimeRangeText: {
    color: ROLE_COLORS.doctor.primary,
    fontWeight: '600',
  },
  chartSection: {
    marginBottom: 16,
  },
});

export default PatientVitalsCharts;
