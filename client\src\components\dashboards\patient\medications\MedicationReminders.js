import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, SectionList, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMedications } from '../../../../contexts/MedicationContext';

const MedicationReminders = () => {
  const { user } = useAuth();
  const { reminders, loading, error, updateReminderStatus } = useMedications();
  const [groupedReminders, setGroupedReminders] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    // Group reminders by time whenever reminders change
    if (reminders && reminders.length > 0) {
      groupRemindersByTime(reminders);
    } else {
      setGroupedReminders([]);
    }
  }, [reminders]);

  // Function to group reminders by time
  const groupRemindersByTime = (reminderData) => {
    // Filter only scheduled or overdue reminders
    const activeReminders = reminderData.filter(r =>
      r.status === 'scheduled' ||
      (r.status !== 'completed' && new Date(r.scheduledTime) < new Date())
    );

    // Create a map to group reminders by hour
    const reminderGroups = {};

    activeReminders.forEach(reminder => {
      const reminderTime = new Date(reminder.scheduledTime);

      // Create a key in format "YYYY-MM-DD HH:00" (hour precision)
      const dateKey = reminderTime.toISOString().split(':')[0] + ':00';

      if (!reminderGroups[dateKey]) {
        reminderGroups[dateKey] = {
          title: formatTimeHeader(reminderTime),
          time: reminderTime,
          data: []
        };
      }

      reminderGroups[dateKey].data.push(reminder);
    });

    // Convert to array and sort by time
    const groupedArray = Object.values(reminderGroups);
    groupedArray.sort((a, b) => a.time - b.time);

    // Only show the next 3 time slots
    const limitedGroups = groupedArray.slice(0, 3);

    setGroupedReminders(limitedGroups);
  };

  // Format the time header
  const formatTimeHeader = (time) => {
    const now = new Date();
    const reminderDate = new Date(time);

    // Format the time part
    const timeStr = reminderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    // If it's today
    if (reminderDate.toDateString() === now.toDateString()) {
      return `Today at ${timeStr}`;
    }

    // If it's tomorrow
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    if (reminderDate.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${timeStr}`;
    }

    // Otherwise show the full date
    return `${reminderDate.toLocaleDateString()} at ${timeStr}`;
  };

  const handleMarkAsTaken = async (reminderId) => {
    setLocalLoading(true);
    try {
      await updateReminderStatus(reminderId, 'completed');
    } catch (error) {
      console.error('Error marking medication as taken:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleSkip = async (reminderId) => {
    setLocalLoading(true);
    try {
      await updateReminderStatus(reminderId, 'missed');
    } catch (error) {
      console.error('Error marking medication as skipped:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const getStatusColor = (status, dueTime) => {
    if (status === 'completed') return '#4CAF50'; // Green
    if (status === 'missed') return '#F44336'; // Red

    // Check if the medication is due or overdue
    const now = new Date();
    const scheduledTime = new Date(dueTime);

    if (scheduledTime < now) {
      // Overdue
      return '#FF9800'; // Orange
    }

    // Calculate if due within the next hour
    const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
    if (scheduledTime <= oneHourFromNow) {
      return '#2196F3'; // Blue - due soon
    }

    return '#757575'; // Gray - scheduled for later
  };

  const getTimeDisplay = (scheduledTime) => {
    const now = new Date();
    const reminderTime = new Date(scheduledTime);

    // If it's today
    if (reminderTime.toDateString() === now.toDateString()) {
      return `Today at ${reminderTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // If it's tomorrow
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    if (reminderTime.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${reminderTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Otherwise show the full date
    return reminderTime.toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDueStatus = (scheduledTime, status) => {
    if (status === 'completed') return 'Taken';
    if (status === 'missed') return 'Skipped';

    const now = new Date();
    const reminderTime = new Date(scheduledTime);

    if (reminderTime < now) {
      const diffMs = now - reminderTime;
      const diffMins = Math.floor(diffMs / 60000);

      if (diffMins < 60) {
        return `${diffMins} min overdue`;
      } else {
        const diffHours = Math.floor(diffMins / 60);
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} overdue`;
      }
    } else {
      const diffMs = reminderTime - now;
      const diffMins = Math.floor(diffMs / 60000);

      if (diffMins < 60) {
        return `Due in ${diffMins} min`;
      } else {
        const diffHours = Math.floor(diffMins / 60);
        return `Due in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
      }
    }
  };

  const renderReminderItem = ({ item }) => {
    const statusColor = getStatusColor(item.status, item.scheduledTime);
    const timeDisplay = getTimeDisplay(item.scheduledTime);
    const dueStatus = getDueStatus(item.scheduledTime, item.status);

    return (
      <View style={styles.reminderItem}>
        <View style={styles.reminderHeader}>
          <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
          <Text style={styles.medicationName}>{item.medicationName}</Text>
          <Text style={styles.dosage}>{item.dosage}</Text>
        </View>

        <View style={styles.reminderDetails}>
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={16} color="#757575" />
            <Text style={styles.timeText}>{timeDisplay}</Text>
          </View>

          <Text style={[styles.statusText, { color: statusColor }]}>{dueStatus}</Text>
        </View>

        <Text style={styles.instructions}>{item.instructions}</Text>

        {item.status === 'scheduled' && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.takenButton]}
              onPress={() => handleMarkAsTaken(item.id)}
            >
              <Ionicons name="checkmark" size={16} color="#fff" />
              <Text style={styles.actionButtonText}>Mark as Taken</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.skipButton]}
              onPress={() => handleSkip(item.id)}
            >
              <Ionicons name="close" size={16} color="#fff" />
              <Text style={styles.actionButtonText}>Skip</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  if (loading || localLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4285F4" />
        <Text style={styles.loadingText}>Loading reminders...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={24} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!reminders || reminders.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="medkit-outline" size={48} color="#BDBDBD" />
        <Text style={styles.emptyText}>No medication reminders</Text>
      </View>
    );
  }

  // Render a section header
  const renderSectionHeader = ({ section }) => (
    <View style={styles.sectionHeader}>
      <View style={styles.timeHeaderContainer}>
        <Ionicons name="time-outline" size={20} color="#4285F4" />
        <Text style={styles.timeHeader}>{section.title}</Text>
      </View>
      <Text style={styles.medicationCount}>
        {section.data.length} medication{section.data.length !== 1 ? 's' : ''}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <SectionList
        sections={groupedReminders}
        renderItem={renderReminderItem}
        renderSectionHeader={renderSectionHeader}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        stickySectionHeadersEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
    margin: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#D32F2F',
    marginLeft: 8,
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    marginBottom: 12,
  },
  timeHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4285F4',
    marginLeft: 8,
  },
  medicationCount: {
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
  },
  container: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 16,
  },
  reminderItem: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  reminderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    flex: 1,
  },
  dosage: {
    fontSize: 14,
    color: '#616161',
    marginLeft: 8,
  },
  reminderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  instructions: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 4,
  },
  takenButton: {
    backgroundColor: '#4CAF50',
  },
  skipButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#757575',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
  },
});

export default MedicationReminders;
