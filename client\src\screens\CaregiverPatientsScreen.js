import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Avatar, Searchbar } from 'react-native-paper';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '../contexts/AuthContext';
import { ROLE_COLORS } from '../config/theme';
import { getDoc, doc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';

const CaregiverPatientsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const caregiverColors = ROLE_COLORS.caregiver;

  // Check if we're in navigation mode
  const { navigateMode } = route.params || {};

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [patients, setPatients] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadLinkedPatients();
  }, []);

  // Function to load patients linked to this caregiver
  const loadLinkedPatients = async () => {
    try {
      setLoading(true);

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', user.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        setPatients([]);
        setLoading(false);
        return;
      }

      // Get user documents for all linked patients
      const usersCollection = collection(db, 'users');
      const patientDocs = [];

      for (const patientId of linkedPatientIds) {
        const patientDocRef = doc(usersCollection, patientId);
        const patientDocSnap = await getDoc(patientDocRef);

        if (patientDocSnap.exists()) {
          patientDocs.push({
            uid: patientDocSnap.id,
            ...patientDocSnap.data()
          });
        }
      }

      setPatients(patientDocs);
    } catch (error) {
      console.error('Error loading linked patients:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load patients. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadLinkedPatients();
  };

  const handlePatientSelect = (patient) => {
    if (navigateMode) {
      // Navigate to the patient navigation screen
      navigation.navigate('CaregiverPatientNavigation', { patient });
    } else {
      // Navigate to the patient detail screen
      navigation.navigate('CaregiverPatientDetail', { patient });
    }
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName || ''} ${patient.lastName || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const renderPatientItem = ({ item }) => {
    const initials = `${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`;
    const displayName = `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown Patient';

    return (
      <Card style={styles.patientCard} onPress={() => handlePatientSelect(item)}>
        <Card.Content style={styles.patientCardContent}>
          <Avatar.Text
            size={50}
            label={initials}
            backgroundColor={caregiverColors.primary}
            color="#fff"
          />
          <View style={styles.patientInfo}>
            <Text style={styles.patientName}>{displayName}</Text>
            <Text style={styles.patientEmail}>{item.email || 'No email'}</Text>
            <Text style={styles.patientDetails}>
              {item.condition || 'No condition specified'}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.viewButton}
            onPress={() => handlePatientSelect(item)}
          >
            <Ionicons name="chevron-forward" size={24} color={caregiverColors.primary} />
          </TouchableOpacity>
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No patients found</Text>
      <Text style={styles.emptySubtext}>
        {navigateMode
          ? 'You need to have patients assigned to you before you can navigate them'
          : 'Patients assigned to you will appear here'
        }
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {navigateMode ? 'Select Patient to Navigate' : 'My Patients'}
        </Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
        >
          <Ionicons name="refresh" size={24} color={caregiverColors.primary} />
        </TouchableOpacity>
      </View>

      <Searchbar
        placeholder={navigateMode ? "Search patient to navigate" : "Search patients"}
        onChangeText={handleSearchChange}
        value={searchQuery}
        style={styles.searchBar}
        iconColor={caregiverColors.primary}
      />

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={caregiverColors.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredPatients}
          renderItem={renderPatientItem}
          keyExtractor={(item) => item.uid}
          contentContainerStyle={
            filteredPatients.length === 0 ? styles.emptyListContent : styles.listContent
          }
          ListEmptyComponent={renderEmptyList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[caregiverColors.primary]}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  searchBar: {
    margin: 16,
    elevation: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  patientCard: {
    marginBottom: 12,
    elevation: 2,
    borderRadius: 8,
  },
  patientCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  patientInfo: {
    flex: 1,
    marginLeft: 12,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  patientEmail: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  patientDetails: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  viewButton: {
    padding: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    maxWidth: '80%',
  },
});

export default CaregiverPatientsScreen;
