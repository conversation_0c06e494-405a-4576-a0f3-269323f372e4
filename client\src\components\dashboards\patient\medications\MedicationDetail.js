import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMedications } from '../../../../contexts/MedicationContext';

const MedicationDetail = () => {
  const { user } = useAuth();
  const {
    medications,
    reminders,
    loading,
    error,
    deleteMedication,
    updateReminderStatus
  } = useMedications();
  const navigation = useNavigation();
  const route = useRoute();
  const { id } = route.params;

  const [medication, setMedication] = useState(null);
  const [medicationReminders, setMedicationReminders] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    // Find the medication in the context
    if (medications && medications.length > 0) {
      const med = medications.find(m => m.id === id);
      if (med) {
        setMedication(med);
      }
    }
  }, [medications, id]);

  useEffect(() => {
    // Filter reminders for this medication
    if (reminders && reminders.length > 0 && id) {
      const medReminders = reminders.filter(r => r.medicationId === id);
      setMedicationReminders(medReminders);
    } else {
      setMedicationReminders([]);
    }
  }, [reminders, id]);

  const handleDeleteMedication = async () => {
    Alert.alert(
      "Delete Medication",
      "Are you sure you want to delete this medication? This will also delete all reminders for this medication.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            setLocalLoading(true);
            try {
              await deleteMedication(id);
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting medication:', error);
              Alert.alert('Error', 'Failed to delete medication');
            } finally {
              setLocalLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleAddReminder = () => {
    // Navigate to add reminder screen
    navigation.navigate('Medications', {
      screen: 'AddReminder',
      params: { medicationId: id }
    });
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (loading || localLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4285F4" />
        <Text style={styles.loadingText}>Loading medication details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={48} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!medication) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={48} color="#F44336" />
        <Text style={styles.errorText}>Medication not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Medication Details</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.medicationCard}>
          <View style={styles.medicationHeader}>
            <Ionicons name="medkit" size={32} color="#4285F4" />
            <View style={styles.medicationTitleContainer}>
              <Text style={styles.medicationName}>{medication.name}</Text>
              <Text style={styles.medicationDosage}>{medication.dosage}</Text>
            </View>
          </View>

          <View style={styles.detailsSection}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Frequency:</Text>
              <Text style={styles.detailValue}>{medication.frequency || 'Not specified'}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Instructions:</Text>
              <Text style={styles.detailValue}>{medication.instructions || 'No special instructions'}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Added on:</Text>
              <Text style={styles.detailValue}>{formatDate(medication.createdAt)}</Text>
            </View>
          </View>
        </View>

        <View style={styles.remindersSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Reminders</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddReminder}
            >
              <Ionicons name="add" size={20} color="#4285F4" />
              <Text style={styles.addButtonText}>Add Reminder</Text>
            </TouchableOpacity>
          </View>

          {medicationReminders.length === 0 ? (
            <View style={styles.emptyReminders}>
              <Ionicons name="time-outline" size={48} color="#BDBDBD" />
              <Text style={styles.emptyRemindersText}>No reminders set</Text>
              <Text style={styles.emptyRemindersSubtext}>
                Add reminders to help you remember when to take your medication
              </Text>
            </View>
          ) : (
            medicationReminders.map((reminder) => (
              <View key={reminder.id} style={styles.reminderItem}>
                <View style={styles.reminderHeader}>
                  <Ionicons
                    name={
                      reminder.status === 'completed' ? 'checkmark-circle' :
                      reminder.status === 'missed' ? 'close-circle' : 'time'
                    }
                    size={24}
                    color={
                      reminder.status === 'completed' ? '#4CAF50' :
                      reminder.status === 'missed' ? '#F44336' : '#4285F4'
                    }
                  />
                  <Text style={styles.reminderTime}>{formatDate(reminder.scheduledTime)}</Text>
                  <View
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor:
                          reminder.status === 'completed' ? '#E8F5E9' :
                          reminder.status === 'missed' ? '#FFEBEE' : '#E3F2FD'
                      }
                    ]}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        {
                          color:
                            reminder.status === 'completed' ? '#4CAF50' :
                            reminder.status === 'missed' ? '#F44336' : '#4285F4'
                        }
                      ]}
                    >
                      {reminder.status.charAt(0).toUpperCase() + reminder.status.slice(1)}
                    </Text>
                  </View>
                </View>

                {reminder.instructions && (
                  <Text style={styles.reminderInstructions}>{reminder.instructions}</Text>
                )}

                {reminder.status === 'scheduled' && (
                  <View style={styles.reminderActions}>
                    <TouchableOpacity
                      style={[styles.reminderAction, styles.takenAction]}
                      onPress={() => updateReminderStatus(reminder.id, 'completed')}
                    >
                      <Ionicons name="checkmark" size={16} color="#fff" />
                      <Text style={styles.actionText}>Mark as Taken</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.reminderAction, styles.missedAction]}
                      onPress={() => updateReminderStatus(reminder.id, 'missed')}
                    >
                      <Ionicons name="close" size={16} color="#fff" />
                      <Text style={styles.actionText}>Skip</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  reminderActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  reminderAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 4,
  },
  takenAction: {
    backgroundColor: '#4CAF50',
  },
  missedAction: {
    backgroundColor: '#F44336',
  },
  actionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#757575',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    color: '#F44336',
    marginBottom: 24,
  },
  backButtonText: {
    color: '#4285F4',
    fontSize: 16,
    fontWeight: 'bold',
  },
  medicationCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  medicationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  medicationTitleContainer: {
    marginLeft: 16,
    flex: 1,
  },
  medicationName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
  },
  medicationDosage: {
    fontSize: 16,
    color: '#757575',
    marginTop: 4,
  },
  detailsSection: {
    marginBottom: 8,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#616161',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#212121',
  },
  remindersSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  addButtonText: {
    fontSize: 14,
    color: '#4285F4',
    marginLeft: 4,
  },
  emptyReminders: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  emptyRemindersText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
    marginBottom: 8,
  },
  emptyRemindersSubtext: {
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
  },
  reminderItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  reminderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reminderTime: {
    fontSize: 16,
    color: '#212121',
    flex: 1,
    marginLeft: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  reminderInstructions: {
    fontSize: 14,
    color: '#757575',
    marginTop: 8,
    marginLeft: 36,
  },
});

export default MedicationDetail;
