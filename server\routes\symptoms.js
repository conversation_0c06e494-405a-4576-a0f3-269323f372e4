const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');

// Initialize Firestore
const db = admin.firestore();

// Log symptoms
router.post('/log', verifyToken, async (req, res) => {
    try {
        const { symptoms, mood, activities, notes } = req.body;
        const userId = req.user.uid;

        const symptomLog = {
            userId,
            symptoms,
            mood,
            activities,
            notes,
            timestamp: admin.firestore.FieldValue.serverTimestamp()
        };

        const docRef = await db.collection('symptomLogs').add(symptomLog);

        res.status(201).json({
            success: true,
            logId: docRef.id
        });
    } catch (error) {
        console.error('Error logging symptoms:', error);
        res.status(500).json({ error: 'Failed to log symptoms' });
    }
});

// Get symptom logs
router.get('/logs', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const { days = 7 } = req.query;

        const startDate = new Date();
        startDate.setDate(startDate.getDate() - parseInt(days));

        const logsSnapshot = await db.collection('symptomLogs')
            .where('userId', '==', userId)
            .where('timestamp', '>=', startDate)
            .orderBy('timestamp', 'desc')
            .get();

        const logs = [];
        logsSnapshot.forEach(doc => {
            logs.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(logs);
    } catch (error) {
        console.error('Error fetching symptom logs:', error);
        res.status(500).json({ error: 'Failed to fetch symptom logs' });
    }
});

module.exports = router;
