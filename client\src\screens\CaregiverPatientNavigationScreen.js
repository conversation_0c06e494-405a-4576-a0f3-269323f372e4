import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
  Dimensions,
  TextInput,
  Modal,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Avatar, Button } from 'react-native-paper';
import * as Location from 'expo-location';
import { ROLE_COLORS } from '../config/theme';
import { collection, query, where, onSnapshot, orderBy, limit, doc, getDoc, addDoc, serverTimestamp } from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import MapView, { <PERSON><PERSON>, <PERSON>yline, PROVIDER_GOOGLE } from 'react-native-maps';
import { firebaseLocationService } from '../services/firebaseLocationService';
import { firebaseNavigationService } from '../services/firebaseNavigationService';

const { width, height } = Dimensions.get('window');

// Google Maps style - Standard style with slight modifications for better readability
const googleMapsStyle = [
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.business",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  }
];

const CaregiverPatientNavigationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const caregiverColors = ROLE_COLORS.caregiver;
  const mapRef = useRef(null);

  // Get patient data from route params if available
  const { patient } = route.params || {};

  // State variables
  const [loading, setLoading] = useState(true);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [patientLocation, setPatientLocation] = useState(null);
  const [mapRegion, setMapRegion] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [destinationCoords, setDestinationCoords] = useState(null);
  const [destination, setDestination] = useState('');
  const [routeType, setRouteType] = useState('walking'); // walking, driving, transit
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeDistance, setRouteDistance] = useState(null);
  const [routeDuration, setRouteDuration] = useState(null);
  const [instructions, setInstructions] = useState('');
  const [showSendGuidanceModal, setShowSendGuidanceModal] = useState(false);
  const [patientLocationsListener, setPatientLocationsListener] = useState(null);
  const [showPatientSelectModal, setShowPatientSelectModal] = useState(false);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(patient || null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
    loadLinkedPatients();

    // Clean up function
    return () => {
      if (patientLocationsListener) {
        patientLocationsListener();
      }
    };
  }, []);

  // Effect to listen to patient location when selected patient changes
  useEffect(() => {
    if (selectedPatient && selectedPatient.uid) {
      listenToPatientLocation(selectedPatient.uid);
    }
  }, [selectedPatient]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMessage('Permission to access location was denied');
        setLoading(false);
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      if (location) {
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        // Set initial map region
        setMapRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      }

      // If no patient is selected and we came directly to this screen,
      // show the patient selection modal
      if (!selectedPatient && !patient) {
        setShowPatientSelectModal(true);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      setErrorMessage('Failed to load navigation data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to load patients linked to this caregiver
  const loadLinkedPatients = async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        setPatients([]);
        return;
      }

      // Get user documents for all linked patients
      const usersCollection = collection(db, 'users');
      const patientDocs = [];

      for (const patientId of linkedPatientIds) {
        const patientDocRef = doc(usersCollection, patientId);
        const patientDocSnap = await getDoc(patientDocRef);

        if (patientDocSnap.exists()) {
          patientDocs.push({
            uid: patientDocSnap.id,
            ...patientDocSnap.data()
          });
        }
      }

      setPatients(patientDocs);
    } catch (error) {
      console.error('Error loading linked patients:', error);
      Alert.alert('Error', 'Failed to load patients. Please try again.');
    }
  };

  // Listen to patient's location updates
  const listenToPatientLocation = (patientId) => {
    try {
      const locationsCollection = collection(db, 'patientLocations');
      const q = query(
        locationsCollection,
        where('userId', '==', patientId),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      // Set up real-time listener
      const unsubscribe = onSnapshot(q, (snapshot) => {
        if (!snapshot.empty) {
          const locationData = snapshot.docs[0].data();
          setPatientLocation({
            latitude: locationData.latitude,
            longitude: locationData.longitude,
          });

          // If this is the first location update and we don't have a map region yet,
          // center the map on the patient's location
          if (!mapRegion) {
            setMapRegion({
              latitude: locationData.latitude,
              longitude: locationData.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            });
          }
        }
      }, (error) => {
        console.error('Error listening to patient location:', error);
        Alert.alert('Error', 'Failed to get real-time location updates.');
      });

      setPatientLocationsListener(unsubscribe);
    } catch (error) {
      console.error('Error setting up location listener:', error);
    }
  };

  // Handle map press to set destination
  const handleMapPress = (event) => {
    const { coordinate } = event.nativeEvent;

    // Set the pressed location as destination
    setDestinationCoords(coordinate);

    // Get address for the selected location
    Location.reverseGeocodeAsync(coordinate)
      .then((addresses) => {
        if (addresses && addresses.length > 0) {
          const address = addresses[0];
          const formattedAddress = [
            address.name,
            address.street,
            address.city,
            address.region,
            address.postalCode,
            address.country,
          ]
            .filter(Boolean)
            .join(', ');

          setDestination(formattedAddress);

          // Calculate route
          if (patientLocation) {
            calculateRoute(patientLocation, coordinate, routeType);
          }
        }
      })
      .catch((error) => {
        console.error('Error reverse geocoding:', error);
      });
  };

  // Calculate route between two points
  const calculateRoute = async (origin, destination, mode) => {
    try {
      // Use our navigation service to calculate the route
      const routeInfo = await firebaseNavigationService.calculateRoute(origin, destination, mode);

      // Update state with route information
      setRouteCoordinates(routeInfo.routeCoordinates);
      setRouteDistance(routeInfo.distance);
      setRouteDuration(routeInfo.duration);
    } catch (error) {
      console.error('Error calculating route:', error);
      Alert.alert('Error', 'Failed to calculate route. Please try again.');
    }
  };

  // Calculate distance between two coordinates in kilometers
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    return distance;
  };

  const deg2rad = (deg) => {
    return deg * (Math.PI / 180);
  };

  // Handle patient selection
  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    setShowPatientSelectModal(false);

    // If we have patient location, update the map region
    if (patientLocation) {
      setMapRegion({
        latitude: patientLocation.latitude,
        longitude: patientLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  // Send guidance to patient
  const handleSendGuidance = async () => {
    if (!selectedPatient || !selectedPatient.uid) {
      Alert.alert('Error', 'No patient selected');
      return;
    }

    if (!destinationCoords) {
      Alert.alert('Error', 'Please select a destination on the map');
      return;
    }

    if (!instructions.trim()) {
      Alert.alert('Error', 'Please provide instructions for the patient');
      return;
    }

    try {
      setLoading(true);

      // Create guidance data
      const guidanceData = {
        patientId: selectedPatient.uid,
        destination,
        destinationCoords: {
          latitude: destinationCoords.latitude,
          longitude: destinationCoords.longitude,
        },
        routeType,
        routeCoordinates,
        routeDistance,
        routeDuration,
        instructions,
      };

      // Use our navigation service to send the guidance
      await firebaseNavigationService.sendPatientGuidance(guidanceData);

      Alert.alert(
        'Success',
        `Guidance sent to ${selectedPatient.firstName}`,
        [{ text: 'OK', onPress: () => setShowSendGuidanceModal(false) }]
      );
    } catch (error) {
      console.error('Error sending guidance:', error);
      Alert.alert('Error', 'Failed to send guidance. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={caregiverColors.primary} />
        <Text style={styles.loadingText}>Loading navigation...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {selectedPatient ? `Navigate ${selectedPatient.firstName}` : 'Patient Navigation'}
        </Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowPatientSelectModal(true)}
          >
            <Ionicons name="people" size={24} color={caregiverColors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={loadInitialData}
          >
            <Ionicons name="refresh" size={24} color={caregiverColors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Map Section */}
      <View style={styles.mapContainer}>
        {mapRegion ? (
          <MapView
            ref={mapRef}
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={mapRegion}
            customMapStyle={googleMapsStyle}
            onPress={handleMapPress}
            showsUserLocation={false}
            showsMyLocationButton={false}
            showsCompass={true}
            showsScale={true}
            showsTraffic={false}
            showsBuildings={true}
            showsIndoors={true}
            zoomControlEnabled={true}
            rotateEnabled={true}
            scrollEnabled={true}
            pitchEnabled={true}
          >
            {/* Caregiver Location Marker */}
            {currentLocation && (
              <Marker
                coordinate={currentLocation}
                title="Your Location"
                description="Your current location"
              >
                <View style={styles.caregiverLocationMarker}>
                  <Ionicons name="person" size={18} color="#fff" />
                </View>
              </Marker>
            )}

            {/* Patient Location Marker */}
            {patientLocation && (
              <Marker
                coordinate={patientLocation}
                title={patient ? `${patient.firstName}'s Location` : 'Patient Location'}
                description="Patient's current location"
              >
                <View style={styles.patientLocationMarker}>
                  <Ionicons name="person" size={18} color="#fff" />
                </View>
              </Marker>
            )}

            {/* Destination Marker */}
            {destinationCoords && (
              <Marker
                coordinate={destinationCoords}
                title="Destination"
                description={destination || "Selected destination"}
              >
                <View style={styles.destinationMarkerContainer}>
                  <View style={styles.destinationMarker}>
                    <Ionicons name="location" size={18} color="#fff" />
                  </View>
                  <View style={styles.destinationMarkerTail} />
                </View>
              </Marker>
            )}

            {/* Route Polyline */}
            {routeCoordinates.length > 0 && (
              <>
                {/* Background line (shadow effect) */}
                <Polyline
                  coordinates={routeCoordinates}
                  strokeWidth={8}
                  strokeColor="rgba(0, 0, 0, 0.2)"
                  lineCap="round"
                  lineJoin="round"
                />
                {/* Main route line */}
                <Polyline
                  coordinates={routeCoordinates}
                  strokeWidth={4}
                  strokeColor="#4285F4"
                  lineCap="round"
                  lineJoin="round"
                />
              </>
            )}
          </MapView>
        ) : (
          <View style={styles.mapErrorContainer}>
            <Text style={styles.mapErrorText}>Unable to load map</Text>
            <TouchableOpacity
              style={styles.mapErrorButton}
              onPress={loadInitialData}
            >
              <Text style={styles.mapErrorButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Route Type Selection */}
      <View style={styles.routeTypeContainer}>
        <TouchableOpacity
          style={[
            styles.routeTypeButton,
            routeType === 'walking' && styles.routeTypeButtonActive
          ]}
          onPress={() => {
            setRouteType('walking');
            if (patientLocation && destinationCoords) {
              calculateRoute(patientLocation, destinationCoords, 'walking');
            }
          }}
        >
          <Ionicons
            name="walk"
            size={24}
            color={routeType === 'walking' ? '#fff' : '#333'}
          />
          <Text
            style={[
              styles.routeTypeText,
              routeType === 'walking' && styles.routeTypeTextActive
            ]}
          >
            Walking
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.routeTypeButton,
            routeType === 'driving' && styles.routeTypeButtonActive
          ]}
          onPress={() => {
            setRouteType('driving');
            if (patientLocation && destinationCoords) {
              calculateRoute(patientLocation, destinationCoords, 'driving');
            }
          }}
        >
          <Ionicons
            name="car"
            size={24}
            color={routeType === 'driving' ? '#fff' : '#333'}
          />
          <Text
            style={[
              styles.routeTypeText,
              routeType === 'driving' && styles.routeTypeTextActive
            ]}
          >
            Driving
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.routeTypeButton,
            routeType === 'transit' && styles.routeTypeButtonActive
          ]}
          onPress={() => {
            setRouteType('transit');
            if (patientLocation && destinationCoords) {
              calculateRoute(patientLocation, destinationCoords, 'transit');
            }
          }}
        >
          <Ionicons
            name="bus"
            size={24}
            color={routeType === 'transit' ? '#fff' : '#333'}
          />
          <Text
            style={[
              styles.routeTypeText,
              routeType === 'transit' && styles.routeTypeTextActive
            ]}
          >
            Transit
          </Text>
        </TouchableOpacity>
      </View>

      {/* Route Information */}
      {destinationCoords && (
        <View style={styles.routeInfoSection}>
          <Text style={styles.routeInfoTitle}>Route Information</Text>

          <View style={styles.routeInfoRow}>
            <Text style={styles.routeInfoLabel}>Destination:</Text>
            <Text style={styles.routeInfoValue}>{destination || 'Selected location'}</Text>
          </View>

          {routeDistance && (
            <View style={styles.routeInfoRow}>
              <Text style={styles.routeInfoLabel}>Distance:</Text>
              <Text style={styles.routeInfoValue}>{routeDistance.toFixed(2)} km</Text>
            </View>
          )}

          {routeDuration && (
            <View style={styles.routeInfoRow}>
              <Text style={styles.routeInfoLabel}>Duration:</Text>
              <Text style={styles.routeInfoValue}>
                {routeDuration > 60
                  ? `${Math.floor(routeDuration / 60)}h ${routeDuration % 60}min`
                  : `${routeDuration} min`}
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={styles.sendGuidanceButton}
            onPress={() => setShowSendGuidanceModal(true)}
          >
            <Ionicons name="navigate" size={20} color="#fff" />
            <Text style={styles.sendGuidanceButtonText}>Send Guidance to Patient</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Send Guidance Modal */}
      <Modal
        visible={showSendGuidanceModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSendGuidanceModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Send Navigation Guidance</Text>

            <View style={styles.modalBody}>
              <Text style={styles.modalLabel}>Destination:</Text>
              <Text style={styles.modalValue}>{destination || 'Selected location'}</Text>

              <Text style={styles.modalLabel}>Instructions for Patient:</Text>
              <TextInput
                style={styles.instructionsInput}
                placeholder="Enter instructions for the patient..."
                value={instructions}
                onChangeText={setInstructions}
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowSendGuidanceModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.sendButton]}
                onPress={handleSendGuidance}
              >
                <Text style={styles.sendButtonText}>Send</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Patient Selection Modal */}
      <Modal
        visible={showPatientSelectModal}
        transparent
        animationType="slide"
        onRequestClose={() => {
          if (selectedPatient) {
            setShowPatientSelectModal(false);
          } else {
            // If no patient is selected and user tries to close modal, show alert
            Alert.alert(
              'No Patient Selected',
              'You need to select a patient to navigate.',
              [
                { text: 'OK' }
              ]
            );
          }
        }}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { maxHeight: '80%' }]}>
            <Text style={styles.modalTitle}>Select Patient to Navigate</Text>

            {patients.length === 0 ? (
              <View style={styles.emptyPatientsContainer}>
                <Ionicons name="people" size={60} color="#ccc" />
                <Text style={styles.emptyPatientsText}>No patients found</Text>
                <Text style={styles.emptyPatientsSubtext}>
                  You need to have patients assigned to you before you can navigate them
                </Text>
              </View>
            ) : (
              <FlatList
                data={patients}
                keyExtractor={(item) => item.uid}
                style={styles.patientsList}
                renderItem={({ item }) => {
                  const initials = `${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`;
                  const displayName = `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown Patient';

                  return (
                    <TouchableOpacity
                      style={styles.patientItem}
                      onPress={() => handlePatientSelect(item)}
                    >
                      <View style={styles.patientAvatar}>
                        <Text style={styles.patientInitials}>{initials}</Text>
                      </View>
                      <View style={styles.patientInfo}>
                        <Text style={styles.patientName}>{displayName}</Text>
                        <Text style={styles.patientEmail}>{item.email || 'No email'}</Text>
                      </View>
                      <Ionicons name="chevron-forward" size={24} color={caregiverColors.primary} />
                    </TouchableOpacity>
                  );
                }}
              />
            )}

            {selectedPatient && (
              <View style={styles.modalFooter}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => setShowPatientSelectModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Close</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  mapContainer: {
    height: '50%',
    width: '100%',
    backgroundColor: '#e0e0e0',
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  mapErrorText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  mapErrorButton: {
    backgroundColor: '#4285F4',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 4,
  },
  mapErrorButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  caregiverLocationMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: ROLE_COLORS.caregiver.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  patientLocationMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: ROLE_COLORS.patient.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  destinationMarkerContainer: {
    alignItems: 'center',
  },
  destinationMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#DB4437',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 5,
  },
  destinationMarkerTail: {
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#DB4437',
    marginTop: -2,
  },
  routeTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  routeTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
  },
  routeTypeButtonActive: {
    backgroundColor: ROLE_COLORS.caregiver.primary,
  },
  routeTypeText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  routeTypeTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  routeInfoSection: {
    padding: 16,
    backgroundColor: '#fff',
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 8,
    elevation: 2,
  },
  routeInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  routeInfoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  routeInfoLabel: {
    width: 100,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  routeInfoValue: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  sendGuidanceButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ROLE_COLORS.caregiver.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 16,
  },
  sendGuidanceButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalBody: {
    marginBottom: 16,
  },
  modalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  modalValue: {
    fontSize: 16,
    color: '#333',
    marginBottom: 16,
  },
  instructionsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f1f2f6',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  sendButton: {
    backgroundColor: ROLE_COLORS.caregiver.primary,
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  patientsList: {
    maxHeight: 400,
  },
  patientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  patientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ROLE_COLORS.caregiver.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  patientInitials: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  patientInfo: {
    flex: 1,
    marginLeft: 12,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  patientEmail: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  emptyPatientsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyPatientsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
  },
  emptyPatientsSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    maxWidth: '80%',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
});

export default CaregiverPatientNavigationScreen;
