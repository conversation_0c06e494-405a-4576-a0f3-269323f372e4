import React, { useMemo } from 'react';
import { MenuProvider } from 'react-native-popup-menu';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from '../contexts/AuthContext';
import FlashMessage from 'react-native-flash-message';
import HealthRecords from '../components/dashboards/patient/healthRecords/HealthRecords';
import Appointments from '../components/dashboards/patient/appointments/Appointments';
import Medications from '../components/dashboards/patient/medications/Medications';
import MedicationDetail from '../components/dashboards/patient/medications/MedicationDetail';
import PatientPrescriptions from '../components/dashboards/patient/prescriptions/PatientPrescriptions';
import DoctorAppointments from '../components/dashboards/doctor/appointments/DoctorAppointments';
import RecordVitals from '../screens/RecordVitals';
import MapScreen from '../screens/MapScreen';
import PatientVideoCallScreen from '../screens/PatientVideoCallScreen';
import PatientGuidanceScreen from '../screens/PatientGuidanceScreen';
import SupervisorPatientTrackingScreen from '../screens/SupervisorPatientTrackingScreen';
import SupervisorGuidanceScreen from '../screens/SupervisorGuidanceScreen';
import SupervisorAssignCaregiverScreen from '../screens/SupervisorAssignCaregiverScreen';
import SupervisorCaregiversScreen from '../screens/SupervisorCaregiversScreen';
import SupervisorAppointmentsScreen from '../screens/SupervisorAppointmentsScreen';
import CaregiverPatientsScreen from '../screens/CaregiverPatientsScreen';
import CaregiverPatientDetailScreen from '../screens/CaregiverPatientDetailScreen';
import CaregiverRecordVitalsScreen from '../screens/CaregiverRecordVitalsScreen';
import CaregiverRecordActivityScreen from '../screens/CaregiverRecordActivityScreen';
import CaregiverPatientNavigationScreen from '../screens/CaregiverPatientNavigationScreen';
import CaregiverAppointmentsScreen from '../screens/CaregiverAppointmentsScreen';
import SupervisorCaregiverManagementScreen from '../screens/SupervisorCaregiverManagementScreen';
import SupervisorPatientHealthScreen from '../screens/SupervisorPatientHealthScreen';
import ScanCaregiverQRScreen from '../screens/ScanCaregiverQRScreen';
import { ROLE_COLORS } from '../config/theme';

// Components
import Login from '../components/login/Login';
import SignUp from '../components/signUp/SignUp';
import HomeScreen from '../components/homeScreen/HomeScreen';
import Dashboard from '../screens/Dashboard';
import Profile from '../components/profile/Profile';
import ForgotPassword from '../components/auth/ForgotPassword';
import ProfileCompletion from '../components/profile/ProfileCompletion';
import UserQRCode from '../components/profile/UserQRCode';
import AddPatientScreen from '../screens/AddPatientScreen';
import ScanPatientQRScreen from '../screens/ScanPatientQRScreen';
import PatientListScreen from '../components/dashboards/PatientListScreen';
import ProfileCompletionScreen from '../screens/ProfileCompletionScreen';
import ProfileScreen from '../screens/ProfileScreen';
import UserQRCodeScreen from '../screens/UserQRCodeScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import UserManagement from '../components/dashboards/admin/management/UserManagement';
import ChatRoomScreen from '../screens/ChatRoomScreen';
import PatientConsultationScreen from '../screens/PatientConsultationScreen';
import PatientHealthMonitoringScreen from '../screens/PatientHealthMonitoringScreen';
import PrescriptionsScreen from '../screens/PrescriptionsScreen';
import NewPrescriptionScreen from '../screens/NewPrescriptionScreen';
import MyDoctorsScreen from '../screens/MyDoctorsScreen';
import MessagesScreen from '../screens/MessagesScreen';
import SettingsScreen from '../screens/SettingsScreen';
import ChangePasswordScreen from '../screens/ChangePasswordScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { user, loading, isRegistering } = useAuth(); // Get isRegistering state

  // Get the primary color based on user role
  const primaryColor = useMemo(() => {
    if (!user) return ROLE_COLORS.default.primary;
    const role = user.role?.toLowerCase() || 'default';
    return (ROLE_COLORS[role] || ROLE_COLORS.default).primary;
  }, [user]);

  if (loading) {
    return null;
  }

  return (
    <MenuProvider>
      <Stack.Navigator>
        {!user || isRegistering ? ( // Check both conditions
          // Auth Stack
          <Stack.Group screenOptions={{ headerShown: false }}>
            <Stack.Screen name="Welcome" component={HomeScreen} />
            <Stack.Screen name="Login" component={Login} />
            <Stack.Screen name="Signup" component={SignUp} />
            <Stack.Screen name="ForgotPassword" component={ForgotPassword} options={{ headerShown: false }} />
          </Stack.Group>
        ) : (
          // App Stack - Only show when user is authenticated AND not in registration process
          <Stack.Group>
            <Stack.Screen
              name="Dashboard"
              component={Dashboard}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Profile"
              component={ProfileScreen}
              options={{
                title: 'My Profile',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="UserQRCode"
              component={UserQRCodeScreen}
              options={{
                title: 'My QR Code',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="AddPatient"
              component={AddPatientScreen}
              options={{
                title: 'Add Patient',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              }}
            />
            <Stack.Screen
              name="ScanPatientQRScreen"
              component={ScanPatientQRScreen}
              options={{
                title: 'Scan Patient QR Code',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              }}
            />
            <Stack.Screen
              name="Patients"
              component={PatientListScreen}
              options={{
                title: 'My Patients',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              }}
            />
            <Stack.Screen
              name="Notifications"
              component={NotificationsScreen}
              options={{
                title: 'Notifications',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="UserManagement"
              component={UserManagement}
              options={{
                title: 'Users management',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="HealthRecords"
              component={HealthRecords}
              options={{
                title: 'Health Records',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="Appointments"
              component={Appointments}
              options={{
                title: 'Appointments',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="RecordVitals"
              component={RecordVitals}
              options={{
                title: 'Record Vitals',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="Medications"
              component={Medications}
              options={{
                title: 'Medications',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="MedicationDetail"
              component={MedicationDetail}
              options={{
                title: 'Medication Details',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="Chatroom"
              component={ChatRoomScreen}
              options={{
                title: 'Video Consultation',
                headerShown: false,
                cardStyle: { backgroundColor: 'transparent' },
                cardOverlayEnabled: true,
                cardStyleInterpolator: ({ current: { progress } }) => ({
                  cardStyle: {
                    opacity: progress.interpolate({
                      inputRange: [0, 0.5, 0.9, 1],
                      outputRange: [0, 0.25, 0.7, 1],
                    }),
                    transform: [
                      {
                        scale: progress.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.9, 1],
                          extrapolate: 'clamp',
                        }),
                      },
                    ],
                  },
                  overlayStyle: {
                    opacity: progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 0.5],
                      extrapolate: 'clamp',
                    }),
                  },
                }),
              }}
            />
            <Stack.Screen
              name="PatientConsultation"
              component={PatientConsultationScreen}
              options={{
                title: 'Patient Consultations',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="PatientHealthMonitoring"
              component={PatientHealthMonitoringScreen}
              options={{
                title: 'Patient Health Monitoring',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="Prescriptions"
              component={PrescriptionsScreen}
              options={{
                title: 'Prescriptions',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="NewPrescription"
              component={NewPrescriptionScreen}
              options={{
                title: 'New Prescription',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="MapScreen"
              component={MapScreen}
              options={{
                title: 'My Locations',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="PatientGuidance"
              component={PatientGuidanceScreen}
              options={{
                title: 'Navigation Guidance',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorPatientTracking"
              component={SupervisorPatientTrackingScreen}
              options={{
                title: 'Patient Tracking',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorGuidanceScreen"
              component={SupervisorGuidanceScreen}
              options={{
                title: 'Send Guidance',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorAssignCaregiverScreen"
              component={SupervisorAssignCaregiverScreen}
              options={{
                title: 'Assign Caregiver',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="ScanCaregiverQRScreen"
              component={ScanCaregiverQRScreen}
              options={{
                title: 'Scan Caregiver Code',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorCaregiversScreen"
              component={SupervisorCaregiversScreen}
              options={{
                title: 'Assigned Caregivers',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorCaregiverManagement"
              component={SupervisorCaregiverManagementScreen}
              options={{
                title: 'Caregiver Management',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorPatientHealth"
              component={SupervisorPatientHealthScreen}
              options={{
                title: 'Patient Health Monitoring',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="MyDoctors"
              component={MyDoctorsScreen}
              options={{
                title: 'My Doctors',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="DoctorAppointments"
              component={DoctorAppointments}
              options={{
                title: 'Appointments',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="PatientPrescriptions"
              component={PatientPrescriptions}
              options={{
                title: 'My Prescriptions',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="PatientVideoCall"
              component={PatientVideoCallScreen}
              options={{
                title: 'Video Consultation',
                headerShown: false,
                cardStyle: { backgroundColor: 'transparent' },
                cardOverlayEnabled: true,
                cardStyleInterpolator: ({ current: { progress } }) => ({
                  cardStyle: {
                    opacity: progress.interpolate({
                      inputRange: [0, 0.5, 0.9, 1],
                      outputRange: [0, 0.25, 0.7, 1],
                    }),
                    transform: [
                      {
                        scale: progress.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.9, 1],
                          extrapolate: 'clamp',
                        }),
                      },
                    ],
                  },
                  overlayStyle: {
                    opacity: progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 0.5],
                      extrapolate: 'clamp',
                    }),
                  },
                }),
              }}
            />
            <Stack.Screen
              name="Messages"
              component={MessagesScreen}
              options={{
                title: 'Chat Consultations',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="Settings"
              component={SettingsScreen}
              options={{
                title: 'Settings',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="ChangePassword"
              component={ChangePasswordScreen}
              options={{
                title: 'Change Password',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="CaregiverPatients"
              component={CaregiverPatientsScreen}
              options={{
                title: 'My Patients',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="CaregiverPatientDetail"
              component={CaregiverPatientDetailScreen}
              options={{
                title: 'Patient Details',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="CaregiverRecordVitals"
              component={CaregiverRecordVitalsScreen}
              options={{
                title: 'Record Vitals',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="CaregiverRecordActivity"
              component={CaregiverRecordActivityScreen}
              options={{
                title: 'Record Activity',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="CaregiverPatientNavigation"
              component={CaregiverPatientNavigationScreen}
              options={{
                title: 'Patient Navigation',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="CaregiverAppointments"
              component={CaregiverAppointmentsScreen}
              options={{
                title: 'Patient Appointments',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
            <Stack.Screen
              name="SupervisorAppointments"
              component={SupervisorAppointmentsScreen}
              options={{
                title: 'Patient Appointments',
                headerStyle: {
                  backgroundColor: primaryColor,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                }
              }}
            />
          </Stack.Group>
        )}
      </Stack.Navigator>
      <FlashMessage position="top" />
    </MenuProvider>
  );
};

export default AppNavigator;