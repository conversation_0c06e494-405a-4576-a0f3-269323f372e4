import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ImageBackground, Alert, ActivityIndicator } from 'react-native';
import CheckBox from 'expo-checkbox';
import { useNavigation, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showMessage } from 'react-native-flash-message';
import Icon from 'react-native-vector-icons/FontAwesome';
import { useAuth } from '../../contexts/AuthContext';

export default function Login() {
  const navigation = useNavigation();
  const route = useRoute();
  const { login } = useAuth();
  const [isSelected, setSelection] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Get email from route params if coming from signup
  useEffect(() => {
    if (route.params?.email) {
      setEmail(route.params.email);
    }
  }, [route.params]);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      showMessage({
        message: 'Error',
        description: 'Please enter both email and password',
        type: 'danger',
      });
      return;
    }

    try {
      setLoading(true);
      await login(email, password);

      if (isSelected) {
        await AsyncStorage.setItem('userEmail', email);
      } else {
        await AsyncStorage.removeItem('userEmail');
      }

      // Show success message
      showMessage({
        message: 'Success',
        description: 'Login successful',
        type: 'success',
      });

      // Wait a short moment before navigating
      setTimeout(() => {
        // Navigate to the dashboard or another page
        navigation.navigate('Dashboard');
      }, 1000); // 1 second delay
    } catch (error) {
      let errorMessage = 'An error occurred during login';

      switch (error.code) {
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address';
          break;
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email';
          break;
        case 'auth/wrong-password':
          errorMessage = 'Incorrect password';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later';
          break;
        default:
          errorMessage = error.message || 'Login failed. Please try again.';
      }

      showMessage({
        message: 'Error',
        description: errorMessage,
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    const checkSavedEmail = async () => {
      try {
        const savedEmail = await AsyncStorage.getItem('userEmail');
        if (savedEmail) {
          setEmail(savedEmail);
          setSelection(true);
        }
      } catch (error) {
        console.error('Error checking saved email:', error);
      }
    };

    checkSavedEmail();
  }, []);

  return (
    <ImageBackground
      source={require('../../../assets/Backgrounds/pexels-pixabay-40568.jpg')}
      style={styles.wrapper}
      imageStyle={{ opacity: 0.9 }}
    >
      <View style={styles.overlay}>
        <View style={styles.formCard}>
          <Text style={styles.formLogin}>Welcome Back</Text>
          <Text style={styles.formSubtitle}>Sign in to continue</Text>

          <View style={styles.inputBox}>
            <Icon name="envelope" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
            <TextInput
              style={styles.input}
              placeholder="Email"
              placeholderTextColor="#999"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputBox}>
            <Icon name="lock" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
            <TextInput
              style={styles.input}
              placeholder="Password"
              placeholderTextColor="#999"
              secureTextEntry={!showPassword}
              value={password}
              onChangeText={setPassword}
            />
            <TouchableOpacity
              style={styles.passwordIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Icon
                name={showPassword ? "eye-slash" : "eye"}
                size={20}
                color="rgba(16, 107, 0, 1)"
              />
            </TouchableOpacity>
          </View>

          <View style={styles.rememberForgot}>
            <View style={styles.checkboxContainer}>
              <CheckBox
                value={isSelected}
                onValueChange={setSelection}
                style={styles.checkbox}
                tintColors={{ true: 'rgba(16, 107, 0, 1)', false: '#999' }}
              />
              <Text style={styles.label}>Remember Me</Text>
            </View>
            <TouchableOpacity>
              <Text style={styles.forgotPassword} onPress={() => navigation.navigate('ForgotPassword')}>
                Forgot Password?
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.btn, loading && styles.btnDisabled]}
            onPress={handleLogin}
            disabled={loading}
            activeOpacity={0.8}
          >
            <Text style={styles.btnText}>{loading ? 'Logging in...' : 'Login'}</Text>
          </TouchableOpacity>

          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>OR</Text>
            <View style={styles.dividerLine} />
          </View>

          <View style={styles.registerLink}>
            <Text style={styles.registerText}>
              Don't have an account?{' '}
            </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Signup')}>
              <Text style={styles.registerLinkText}>
                Register
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  formCard: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
    alignItems: 'center',
  },
  formLogin: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#333',
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 30,
  },
  inputBox: {
    position: 'relative',
    width: '100%',
    height: 55,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    position: 'absolute',
    left: 15,
    zIndex: 1,
  },
  passwordIcon: {
    position: 'absolute',
    right: 15,
    padding: 5,
    zIndex: 1,
  },
  input: {
    flex: 1,
    height: '100%',
    backgroundColor: 'rgba(240, 245, 250, 0.8)',
    borderColor: '#E1E8ED',
    borderWidth: 1,
    borderRadius: 12,
    fontSize: 16,
    color: '#333',
    paddingHorizontal: 20,
    paddingLeft: 45, // Add padding to make space for the icon
    paddingRight: 45, // Add padding to make space for the password icon
  },
  rememberForgot: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 25,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    marginRight: 8,
    borderRadius: 4,
  },
  label: {
    color: '#666',
    fontSize: 14,
  },
  forgotPassword: {
    color: 'rgba(16, 107, 0, 1)',
    fontSize: 14,
    fontWeight: '500',
  },
  btn: {
    width: '100%',
    height: 45,
    backgroundColor: '#fff',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  btnDisabled: {
    opacity: 0.7,
  },
  btnText: {
    color: 'rgba(16, 107, 0, 1)',
    fontSize: 16,
    fontWeight: 'bold',
  },
  divider: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 15,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E1E8ED',
  },
  dividerText: {
    color: '#999',
    paddingHorizontal: 10,
    fontSize: 14,
  },
  registerLink: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  registerText: {
    color: '#666',
    textAlign: 'center',
    fontSize: 15,
  },
  registerLinkText: {
    color: 'rgba(16, 107, 0, 1)',
    fontWeight: 'bold',
    fontSize: 15,
  },
});