import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Dimensions
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { Card, Divider, List, Surface } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { getThemeForRole, COLORS } from '../../config/theme';
import QRCode from 'react-native-qrcode-svg';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');
const QR_SIZE = width * 0.6;

const Settings = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const theme = getThemeForRole(user?.role || 'default');
  const primaryColor = theme.colors.primary;

  // Settings state
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('English');
  const [qrError, setQrError] = useState(false);

  // Toggle functions
  const toggleNotifications = () => setNotifications(!notifications);
  const toggleDarkMode = () => setDarkMode(!darkMode);

  return (
    <ScrollView style={styles.container}>
      <Surface style={styles.content} elevation={0}>
        {/* QR Code Section */}
        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <Text style={[styles.sectionTitle, { color: primaryColor }]}>My QR Code</Text>
            <Divider style={styles.divider} />

            <View style={styles.qrContainer}>
              {user?.userCode ? (
                <QRCode
                  value={user.userCode}
                  size={QR_SIZE}
                  color={primaryColor}
                  backgroundColor="white"
                  ecl="M"
                  onError={(e) => {
                    console.error('QR Code Error:', e);
                    setQrError(true);
                  }}
                />
              ) : (
                <View style={styles.placeholderQR}>
                  <Ionicons name="qr-code-outline" size={QR_SIZE * 0.6} color="#ccc" />
                </View>
              )}
            </View>

            <View style={styles.codeContainer}>
              <Text style={styles.codeLabel}>Your Unique Code</Text>
              <Text style={[styles.codeText, { color: primaryColor }]}>{user?.userCode || 'CODE NOT FOUND'}</Text>
              <Text style={styles.codeDescription}>
                Share this code with healthcare providers to connect with them
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* General Settings */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: primaryColor }]}>General Settings</Text>
            <Divider style={styles.divider} />

            <List.Item
              title="Notifications"
              description="Enable push notifications"
              left={props => <List.Icon {...props} icon="bell" color={primaryColor} />}
              right={props => <Switch value={notifications} onValueChange={toggleNotifications} color={primaryColor} />}
            />

            <List.Item
              title="Dark Mode"
              description="Enable dark theme"
              left={props => <List.Icon {...props} icon="weather-night" color={primaryColor} />}
              right={props => <Switch value={darkMode} onValueChange={toggleDarkMode} color={primaryColor} />}
            />

            <List.Item
              title="Language"
              description={language}
              left={props => <List.Icon {...props} icon="earth" color={primaryColor} />}
              right={props => <Ionicons name="chevron-forward" size={24} color={COLORS.textMedium} />}
              onPress={() => {/* Open language selection */}}
            />
          </Card.Content>
        </Card>

        {/* Privacy & Security */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: primaryColor }]}>Privacy & Security</Text>
            <Divider style={styles.divider} />

            <List.Item
              title="Change Password"
              left={props => <List.Icon {...props} icon="lock" color={primaryColor} />}
              right={props => <Ionicons name="chevron-forward" size={24} color={COLORS.textMedium} />}
              onPress={() => navigation.navigate('ChangePassword')}
            />

            <List.Item
              title="Privacy Policy"
              left={props => <List.Icon {...props} icon="shield" color={primaryColor} />}
              right={props => <Ionicons name="chevron-forward" size={24} color={COLORS.textMedium} />}
              onPress={() => {/* Open privacy policy */}}
            />

            <List.Item
              title="Terms of Service"
              left={props => <List.Icon {...props} icon="file-document" color={primaryColor} />}
              right={props => <Ionicons name="chevron-forward" size={24} color={COLORS.textMedium} />}
              onPress={() => {/* Open terms of service */}}
            />
          </Card.Content>
        </Card>

        {/* About */}
        <Card style={[styles.card, styles.lastCard]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: primaryColor }]}>About</Text>
            <Divider style={styles.divider} />

            <List.Item
              title="App Version"
              description="1.0.0"
              left={props => <List.Icon {...props} icon="information" color={primaryColor} />}
            />

            <List.Item
              title="Contact Support"
              left={props => <List.Icon {...props} icon="help-circle" color={primaryColor} />}
              right={props => <Ionicons name="chevron-forward" size={24} color={COLORS.textMedium} />}
              onPress={() => {/* Open support contact */}}
            />
          </Card.Content>
        </Card>
      </Surface>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    padding: 16,
    backgroundColor: 'transparent',
  },
  card: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  lastCard: {
    marginBottom: 32,
  },
  cardContent: {
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  divider: {
    height: 1,
    marginBottom: 16,
  },
  qrContainer: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  placeholderQR: {
    width: QR_SIZE,
    height: QR_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  codeContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  codeLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: COLORS.textDark,
  },
  codeText: {
    fontSize: 20,
    fontWeight: 'bold',
    letterSpacing: 1,
    marginBottom: 8,
  },
  codeDescription: {
    fontSize: 14,
    color: COLORS.textMedium,
    textAlign: 'center',
  },
});

export default Settings;
