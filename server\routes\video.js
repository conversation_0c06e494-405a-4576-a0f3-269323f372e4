const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const db = admin.firestore();
const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Middleware to verify authentication
const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Invalid token' });
    }
};

// Create a call record
router.post('/create-call', authenticateUser, async (req, res) => {
    try {
        const { patientId, roomName } = req.body;
        const doctorId = req.user.uid;
        
        // Verify doctor has a relationship with the patient
        const doctorPatientRel = await db.collection('userRelationships')
            .where('initiatorId', '==', doctorId)
            .where('targetId', '==', patientId)
            .where('status', '==', 'active')
            .limit(1)
            .get();
            
        if (doctorPatientRel.empty) {
            return res.status(403).json({ error: 'Not authorized to call this patient' });
        }

        // Create call record in Firestore
        const callData = {
            roomName,
            doctorId,
            patientId,
            startTime: admin.firestore.FieldValue.serverTimestamp(),
            status: 'active',
        };
        
        const callRef = await db.collection('videoCalls').add(callData);
        
        // Get patient FCM token for notification (handled by caller)
        const patientDoc = await db.collection('users').doc(patientId).get();
        let fcmToken = null;
        
        if (patientDoc.exists) {
            const patientData = patientDoc.data();
            fcmToken = patientData.fcmToken;
        }
        
        res.json({ 
            success: true, 
            callId: callRef.id, 
            roomName, 
            fcmToken 
        });
    } catch (error) {
        console.error('Error creating call:', error);
        res.status(500).json({ error: 'Failed to create call' });
    }
});

// Configure multer for file storage
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const dir = path.join(__dirname, '../uploads/call-recordings');
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        cb(null, dir);
    },
    filename: (req, file, cb) => {
        cb(null, `${uuidv4()}-${file.originalname}`);
    }
});

const upload = multer({ 
    storage,
    limits: { fileSize: 100 * 1024 * 1024 }, // 100 MB limit
    fileFilter: (req, file, cb) => {
        const filetypes = /webm|mp4|mov/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('Only video files are allowed'));
    }
});

// Upload call recording
router.post('/upload-recording', authenticateUser, upload.single('recording'), async (req, res) => {
    try {
        const file = req.file;
        const { patientId } = req.body;
        const doctorId = req.user.uid;
        
        if (!file) {
            return res.status(400).json({ error: 'No recording file provided' });
        }
        
        // Store recording metadata in Firestore
        const recordingData = {
            patientId,
            doctorId,
            filePath: file.path,
            fileName: file.filename,
            fileSize: file.size,
            mimeType: file.mimetype,
            uploadedAt: admin.firestore.FieldValue.serverTimestamp(),
        };
        
        const recordingRef = await db.collection('callRecordings').add(recordingData);
        
        // Add recording reference to patient's medical records
        await db.collection('medicalRecords').add({
            patientId,
            type: 'video_consultation',
            title: `Video Call Recording - ${new Date().toLocaleString()}`,
            description: 'Video consultation recording',
            date: admin.firestore.FieldValue.serverTimestamp(),
            doctorId,
            attachments: [recordingRef.id],
            notes: '',
            followUpRequired: false
        });
        
        res.status(201).json({ 
            id: recordingRef.id,
            message: 'Recording uploaded successfully' 
        });
    } catch (error) {
        console.error('Error uploading recording:', error);
        res.status(500).json({ error: 'Failed to upload recording' });
    }
});

// Get a specific call by ID
router.get('/:callId', authenticateUser, async (req, res) => {
    try {
        const { callId } = req.params;
        const userId = req.user.uid;
        
        const callDoc = await db.collection('videoCalls').doc(callId).get();
        
        if (!callDoc.exists) {
            return res.status(404).json({ error: 'Call not found' });
        }
        
        const callData = callDoc.data();
        
        // Only doctor or patient involved in the call can access it
        if (callData.doctorId !== userId && callData.patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to access this call' });
        }
        
        res.json(callData);
    } catch (error) {
        console.error('Error getting call:', error);
        res.status(500).json({ error: 'Failed to get call' });
    }
});

// End a call
router.post('/:callId/end', authenticateUser, async (req, res) => {
    try {
        const { callId } = req.params;
        const userId = req.user.uid;
        
        const callDoc = await db.collection('videoCalls').doc(callId).get();
        
        if (!callDoc.exists) {
            return res.status(404).json({ error: 'Call not found' });
        }
        
        const callData = callDoc.data();
        
        // Only doctor or patient involved in the call can end it
        if (callData.doctorId !== userId && callData.patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to end this call' });
        }
        
        // Update call status to ended
        await db.collection('videoCalls').doc(callId).update({
            status: 'ended',
            endTime: admin.firestore.FieldValue.serverTimestamp(),
            endedBy: userId
        });
        
        res.json({ message: 'Call ended successfully' });
    } catch (error) {
        console.error('Error ending call:', error);
        res.status(500).json({ error: 'Failed to end call' });
    }
});

module.exports = router; 