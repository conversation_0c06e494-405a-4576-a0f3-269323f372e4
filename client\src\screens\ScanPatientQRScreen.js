import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Dimensions,
  Image
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { usersAPI } from '../config/api';
import { useAuth } from '../contexts/AuthContext';
import { showMessage } from 'react-native-flash-message';
import { COLORS } from '../config/theme';
import * as ImagePicker from 'expo-image-picker';

const { width } = Dimensions.get('window');
const qrSize = width * 0.7;

const ScanPatientQRScreen = () => {
  // State variables
  const [loading, setLoading] = useState(false);
  const [patientCode, setPatientCode] = useState('');
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);

  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();

  // Check if we should show camera or manual entry
  const mode = route.params?.mode || 'manual';
  const showCamera = mode === 'camera';

  // Request camera permission when showing camera
  useEffect(() => {
    if (showCamera) {
      const getBarCodeScannerPermissions = async () => {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
      };

      getBarCodeScannerPermissions();
    }
  }, [showCamera]);

  // Handle manual code changes
  const handleCodeChange = (text) => {
    // Auto uppercase the input
    setPatientCode(text.toUpperCase());
  };

  // Check if a code is valid
  const isValidCode = (code) => {
    return code && code.length === 8 && /^[A-Z0-9]{8}$/.test(code);
  };

  // Manual code submission
  const handleSubmit = () => {
    if (!isValidCode(patientCode)) {
      showMessage({
        message: 'Invalid Code Format',
        description: 'Please enter a valid 8-character code (letters and numbers only)',
        type: 'danger',
        backgroundColor: COLORS.primary,
      });
      return;
    }

    processUserCode(patientCode);
  };

  // Handle taking a picture
  const takePicture = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        // In a real app, you would process the image to extract QR code
        // For now, we'll just simulate a successful scan
        showMessage({
          message: 'QR Code Captured',
          description: 'Processing the image...',
          type: 'info',
          backgroundColor: COLORS.primary,
        });

        // Simulate finding a valid code after a delay
        setTimeout(() => {
          // Generate a random 8-character code
          const randomCode = Math.random().toString(36).substring(2, 10).toUpperCase();
          processUserCode(randomCode);
        }, 2000);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to capture image',
        type: 'danger',
        backgroundColor: COLORS.primary,
      });
    }
  };

  // Process a user code (from QR or manual entry)
  const processUserCode = async (code) => {
    setLoading(true);

    try {
      console.log('Processing code:', code);

      // Verify the code belongs to a patient
      const userResponse = await usersAPI.getUserByCode(code);
      console.log('User found:', userResponse);

      if (!userResponse || userResponse.role !== 'patient') {
        showMessage({
          message: 'Invalid User',
          description: 'This code does not belong to a patient',
          type: 'danger',
          backgroundColor: COLORS.primary,
        });
        setLoading(false);
        setScanned(false);
        return;
      }

      // Get relationship type from route params or determine based on user role
      let relationshipType = route.params?.relationshipType || 'doctor-patient';

      // If not provided in route params, determine based on user role
      if (!route.params?.relationshipType) {
        if (user.role === 'nurse') {
          relationshipType = 'nurse-patient';
        } else if (user.role === 'pharmacist') {
          relationshipType = 'pharmacist-patient';
        } else if (user.role === 'supervisor') {
          relationshipType = 'supervisor-patient';
        }
      }

      console.log('Linking user with type:', relationshipType);
      const linkResponse = await usersAPI.linkUser(code, relationshipType);
      console.log('Link response:', linkResponse);

      showMessage({
        message: 'Success',
        description: `Patient ${userResponse.displayName} added successfully`,
        type: 'success',
        backgroundColor: COLORS.primary,
      });

      // Go back to the previous screen
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    } catch (error) {
      console.error('Error processing code:', error);
      let errorMessage = 'Failed to add patient. Please try again.';

      if (error?.responseData?.error === 'User not found with this code') {
        errorMessage = 'Invalid code. No patient found with this code.';
      }

      showMessage({
        message: 'Error',
        description: errorMessage,
        type: 'danger',
        backgroundColor: COLORS.primary,
      });

      setScanned(false);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Adding patient...</Text>
      </View>
    );
  }

  // Camera scanning mode
  if (showCamera) {
    // Handle permission states
    if (hasPermission === null) {
      return (
        <View style={styles.container}>
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      );
    }

    if (hasPermission === false) {
      return (
        <View style={styles.container}>
          <Text style={styles.permissionText}>No access to camera</Text>
          <TouchableOpacity
            style={[styles.button, { marginTop: 20, backgroundColor: COLORS.primary }]}
            onPress={() => navigation.navigate('ScanPatientQRScreen', { mode: 'manual' })}
          >
            <Text style={styles.buttonText}>Enter Code Manually</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.container}>
        <View style={styles.cameraContainer}>
          <View style={styles.scanArea}>
            {scanned && !loading && (
              <View style={styles.scanningAnimation}>
                <ActivityIndicator size="large" color="#fff" />
                <Text style={styles.scanningText}>Processing...</Text>
              </View>
            )}
          </View>

          <TouchableOpacity
            style={styles.captureButton}
            onPress={takePicture}
            disabled={scanned}
          >
            <Ionicons name="camera" size={32} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Informational note for users */}
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            Position the patient's QR code within the frame to scan.
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Position QR code inside the square
          </Text>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.button}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>

            {scanned && !loading && (
              <TouchableOpacity
                style={styles.button}
                onPress={() => setScanned(false)}
              >
                <Text style={styles.buttonText}>Scan Again</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.button}
              onPress={() => navigation.navigate('ScanPatientQRScreen', { mode: 'manual' })}
            >
              <Text style={styles.buttonText}>Enter Manually</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  // Manual entry mode
  return (
    <View style={styles.container}>
      <View style={styles.formContainer}>
        <Ionicons name="qr-code" size={80} color={COLORS.primary} style={styles.qrIcon} />

        <Text style={styles.title}>Add Patient by Code</Text>
        <Text style={styles.subtitle}>
          Enter the 8-character code from patient's profile
        </Text>

        <TextInput
          style={styles.input}
          value={patientCode}
          onChangeText={handleCodeChange}
          placeholder="Enter code (e.g. ABCD1234)"
          placeholderTextColor="#999"
          autoCapitalize="characters"
          maxLength={8}
          keyboardType="default"
        />

        <View style={styles.helperContainer}>
          <Text style={styles.helperText}>
            {patientCode.length}/8 characters
          </Text>
          {patientCode.length > 0 && (
            <Text style={[
              styles.validationText,
              isValidCode(patientCode) ? styles.validText : styles.invalidText
            ]}>
              {isValidCode(patientCode) ? '✓ Valid code' : '✗ Invalid format'}
            </Text>
          )}
        </View>

        <TouchableOpacity
          style={[
            styles.button,
            isValidCode(patientCode) && !loading ?
              { backgroundColor: '#fff', borderWidth: 2, borderColor: '#4CAF50' } :
              styles.buttonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!isValidCode(patientCode) || loading}
        >
          <Text style={[
            styles.buttonText,
            { color: isValidCode(patientCode) ? '#4CAF50' : '#999' }
          ]}>
            {isValidCode(patientCode) ? 'Connect Patient' : 'Enter Valid Code'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
          disabled={loading}
        >
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cameraContainer: {
    width: '100%',
    height: '70%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#333',
  },
  scanArea: {
    width: qrSize,
    height: qrSize,
    borderWidth: 2,
    borderColor: '#fff',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningAnimation: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  scanningText: {
    color: '#fff',
    fontWeight: 'bold',
    marginTop: 10,
  },
  captureButton: {
    position: 'absolute',
    bottom: 30,
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  infoBox: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 15,
    borderRadius: 10,
  },
  infoText: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    alignItems: 'center',
  },
  footerText: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  qrIcon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  input: {
    width: '100%',
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    fontSize: 18,
    textAlign: 'center',
    letterSpacing: 2,
  },
  helperContainer: {
    width: '100%',
    marginTop: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  helperText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
  },
  validationText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
  },
  validText: {
    color: '#4CAF50',
  },
  invalidText: {
    color: '#F44336',
  },
  button: {
    backgroundColor: '#fff',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4,
    width: '100%',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  buttonDisabled: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ddd',
  },
  buttonText: {
    color: '#000',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 16,
    padding: 12,
  },
  cancelText: {
    color: '#666',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#121212',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#fff',
  },
  permissionText: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    padding: 20,
  }
});

export default ScanPatientQRScreen;