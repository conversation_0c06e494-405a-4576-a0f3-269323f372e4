import { auth, db } from '../config/firebase';
import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';

/**
 * Service for managing medications in Firebase
 */
export const firebaseMedicationsService = {
  /**
   * Save a new medication to Firebase
   * @param {Object} medicationData - The medication data to save
   * @returns {Promise<Object>} - The saved medication record with ID
   */
  saveMedication: async (medicationData) => {
    try {
      // Get current user
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create medication data with patient ID
      let medicationToSave = {
        ...medicationData,
        patientId: medicationData.patientId || currentUser.uid
      };

      // Add timestamps manually if serverTimestamp is not available
      try {
        medicationToSave.createdAt = serverTimestamp();
        medicationToSave.updatedAt = serverTimestamp();
      } catch (timestampError) {
        console.warn('serverTimestamp not available, using Date object instead');
        const now = new Date().toISOString();
        medicationToSave.createdAt = now;
        medicationToSave.updatedAt = now;
      }

      // Add to Firestore
      const medicationsCollection = collection(db, 'medications');
      const docRef = await addDoc(medicationsCollection, medicationToSave);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: docRef.id,
        ...newDocData,
      };

      // Ensure timestamps are strings if they're not already
      if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
        result.createdAt = newDocData.createdAt.toDate().toISOString();
      }
      if (newDocData.updatedAt && typeof newDocData.updatedAt.toDate === 'function') {
        result.updatedAt = newDocData.updatedAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error saving medication to Firebase:', error);
      throw new Error('Failed to save medication');
    }
  },

  /**
   * Get all medications for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication records
   */
  getPatientMedications: async (patientId) => {
    try {
      // Get current user if patientId not provided
      if (!patientId) {
        const currentUser = auth.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }
        patientId = currentUser.uid;
      }

      // Query Firestore for medications
      const medicationsCollection = collection(db, 'medications');

      try {
        // Try with ordering (requires index)
        const q = query(
          medicationsCollection,
          where('patientId', '==', patientId),
          orderBy('createdAt', 'desc')
        );

        const querySnapshot = await getDocs(q);

        // Convert to array of medications
        const medications = [];
        querySnapshot.forEach((doc) => {
          medications.push({
            id: doc.id,
            ...doc.data()
          });
        });

        return medications;
      } catch (indexError) {
        // If index error, fall back to simpler query without ordering
        if (indexError.toString().includes('requires an index')) {
          console.warn('Index not found, falling back to simple query');

          const simpleQuery = query(
            medicationsCollection,
            where('patientId', '==', patientId)
          );

          const simpleSnapshot = await getDocs(simpleQuery);

          // Convert to array of medications and sort manually
          const medications = [];
          simpleSnapshot.forEach((doc) => {
            medications.push({
              id: doc.id,
              ...doc.data()
            });
          });

          // Sort manually by createdAt if available
          return medications.sort((a, b) => {
            if (!a.createdAt || !b.createdAt) return 0;

            const dateA = a.createdAt instanceof Date ? a.createdAt :
                         typeof a.createdAt === 'string' ? new Date(a.createdAt) :
                         a.createdAt.toDate ? a.createdAt.toDate() : new Date(0);

            const dateB = b.createdAt instanceof Date ? b.createdAt :
                         typeof b.createdAt === 'string' ? new Date(b.createdAt) :
                         b.createdAt.toDate ? b.createdAt.toDate() : new Date(0);

            return dateB - dateA; // descending order
          });
        } else {
          // If not an index error, rethrow
          throw indexError;
        }
      }
    } catch (error) {
      console.error('Error getting medications from Firebase:', error);
      return [];
    }
  },

  /**
   * Get a specific medication by ID
   * @param {string} medicationId - The medication ID
   * @returns {Promise<Object|null>} - The medication or null if not found
   */
  getMedicationById: async (medicationId) => {
    try {
      const medicationRef = doc(db, 'medications', medicationId);
      const medicationDoc = await getDoc(medicationRef);

      if (medicationDoc.exists()) {
        return {
          id: medicationDoc.id,
          ...medicationDoc.data()
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting medication by ID from Firebase:', error);
      return null;
    }
  },

  /**
   * Update a medication
   * @param {string} medicationId - The medication ID to update
   * @param {Object} updateData - The data to update
   * @returns {Promise<Object|null>} - The updated medication or null if not found
   */
  updateMedication: async (medicationId, updateData) => {
    try {
      const medicationRef = doc(db, 'medications', medicationId);
      const medicationDoc = await getDoc(medicationRef);

      if (!medicationDoc.exists()) {
        return null;
      }

      // Add updated timestamp with error handling
      let dataToUpdate = { ...updateData };

      // Add timestamps manually if serverTimestamp is not available
      try {
        dataToUpdate.updatedAt = serverTimestamp();
      } catch (timestampError) {
        console.warn('serverTimestamp not available, using Date object instead');
        dataToUpdate.updatedAt = new Date().toISOString();
      }

      await updateDoc(medicationRef, dataToUpdate);

      // Get the updated document
      const updatedDoc = await getDoc(medicationRef);
      const updatedData = updatedDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: medicationId,
        ...updatedData,
      };

      // Ensure updatedAt is a string if it's not already
      if (updatedData.updatedAt && typeof updatedData.updatedAt.toDate === 'function') {
        result.updatedAt = updatedData.updatedAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error updating medication in Firebase:', error);
      return null;
    }
  },

  /**
   * Delete a medication
   * @param {string} medicationId - The medication ID to delete
   * @returns {Promise<boolean>} - True if deleted, false if not found
   */
  deleteMedication: async (medicationId) => {
    try {
      const medicationRef = doc(db, 'medications', medicationId);
      const medicationDoc = await getDoc(medicationRef);

      if (!medicationDoc.exists()) {
        return false;
      }

      await deleteDoc(medicationRef);
      return true;
    } catch (error) {
      console.error('Error deleting medication from Firebase:', error);
      return false;
    }
  },

  /**
   * Save a medication reminder
   * @param {Object} reminderData - The reminder data to save
   * @returns {Promise<Object>} - The saved reminder with ID
   */
  saveReminder: async (reminderData) => {
    try {
      // Get current user
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create reminder data with timestamp handling
      let reminderToSave = {
        ...reminderData,
        patientId: reminderData.patientId || currentUser.uid,
        status: reminderData.status || 'scheduled' // scheduled, completed, missed
      };

      // Add timestamps manually if serverTimestamp is not available
      try {
        reminderToSave.createdAt = serverTimestamp();
      } catch (timestampError) {
        console.warn('serverTimestamp not available, using Date object instead');
        reminderToSave.createdAt = new Date().toISOString();
      }

      // Add to Firestore
      const remindersCollection = collection(db, 'medicationReminders');
      const docRef = await addDoc(remindersCollection, reminderToSave);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: docRef.id,
        ...newDocData,
      };

      // Ensure createdAt is a string if it's not already
      if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
        result.createdAt = newDocData.createdAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error saving medication reminder to Firebase:', error);
      throw new Error('Failed to save medication reminder');
    }
  },

  /**
   * Get all medication reminders for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication reminders
   */
  getPatientReminders: async (patientId) => {
    try {
      // Get current user if patientId not provided
      if (!patientId) {
        const currentUser = auth.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }
        patientId = currentUser.uid;
      }

      // Query Firestore for reminders
      const remindersCollection = collection(db, 'medicationReminders');

      try {
        // Try with ordering (requires index)
        const q = query(
          remindersCollection,
          where('patientId', '==', patientId),
          orderBy('scheduledTime', 'asc')
        );

        const querySnapshot = await getDocs(q);

        // Convert to array of reminders
        const reminders = [];
        querySnapshot.forEach((doc) => {
          reminders.push({
            id: doc.id,
            ...doc.data()
          });
        });

        return reminders;
      } catch (indexError) {
        // If index error, fall back to simpler query without ordering
        if (indexError.toString().includes('requires an index')) {
          console.warn('Index not found, falling back to simple query');

          const simpleQuery = query(
            remindersCollection,
            where('patientId', '==', patientId)
          );

          const simpleSnapshot = await getDocs(simpleQuery);

          // Convert to array of reminders and sort manually
          const reminders = [];
          simpleSnapshot.forEach((doc) => {
            reminders.push({
              id: doc.id,
              ...doc.data()
            });
          });

          // Sort manually by scheduledTime if available
          return reminders.sort((a, b) => {
            if (!a.scheduledTime || !b.scheduledTime) return 0;

            const dateA = a.scheduledTime instanceof Date ? a.scheduledTime :
                         typeof a.scheduledTime === 'string' ? new Date(a.scheduledTime) :
                         a.scheduledTime.toDate ? a.scheduledTime.toDate() : new Date(0);

            const dateB = b.scheduledTime instanceof Date ? b.scheduledTime :
                         typeof b.scheduledTime === 'string' ? new Date(b.scheduledTime) :
                         b.scheduledTime.toDate ? b.scheduledTime.toDate() : new Date(0);

            return dateA - dateB; // ascending order
          });
        } else {
          // If not an index error, rethrow
          throw indexError;
        }
      }
    } catch (error) {
      console.error('Error getting medication reminders from Firebase:', error);
      return [];
    }
  },

  /**
   * Update a medication reminder status
   * @param {string} reminderId - The reminder ID to update
   * @param {string} status - The new status (scheduled, completed, missed)
   * @returns {Promise<Object|null>} - The updated reminder or null if not found
   */
  updateReminderStatus: async (reminderId, status) => {
    try {
      const reminderRef = doc(db, 'medicationReminders', reminderId);
      const reminderDoc = await getDoc(reminderRef);

      if (!reminderDoc.exists()) {
        return null;
      }

      // Update the status with timestamp handling
      let updateData = { status };

      // Add timestamps manually if serverTimestamp is not available
      try {
        updateData.updatedAt = serverTimestamp();
      } catch (timestampError) {
        console.warn('serverTimestamp not available, using Date object instead');
        updateData.updatedAt = new Date().toISOString();
      }

      await updateDoc(reminderRef, updateData);

      // Get the updated document
      const updatedDoc = await getDoc(reminderRef);
      const updatedData = updatedDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: reminderId,
        ...updatedData,
      };

      // Ensure updatedAt is a string if it's not already
      if (updatedData.updatedAt && typeof updatedData.updatedAt.toDate === 'function') {
        result.updatedAt = updatedData.updatedAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error updating medication reminder status in Firebase:', error);
      return null;
    }
  }
};

/**
 * Utility function to delete all medications and reminders for a user
 * @param {string} patientId - The patient ID
 * @returns {Promise<boolean>} - True if successful
 */
export const clearAllMedicationsForUser = async (patientId) => {
  try {
    // Get current user if patientId not provided
    if (!patientId) {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }
      patientId = currentUser.uid;
    }

    // Get all medications for the user
    const medicationsCollection = collection(db, 'medications');
    const medsQuery = query(
      medicationsCollection,
      where('patientId', '==', patientId)
    );

    const medsSnapshot = await getDocs(medsQuery);

    // Delete each medication
    const medDeletePromises = [];
    medsSnapshot.forEach((doc) => {
      medDeletePromises.push(deleteDoc(doc.ref));
    });

    // Get all reminders for the user
    const remindersCollection = collection(db, 'medicationReminders');
    const remsQuery = query(
      remindersCollection,
      where('patientId', '==', patientId)
    );

    const remsSnapshot = await getDocs(remsQuery);

    // Delete each reminder
    const remDeletePromises = [];
    remsSnapshot.forEach((doc) => {
      remDeletePromises.push(deleteDoc(doc.ref));
    });

    // Wait for all deletions to complete
    await Promise.all([...medDeletePromises, ...remDeletePromises]);

    console.log(`Deleted ${medDeletePromises.length} medications and ${remDeletePromises.length} reminders`);
    return true;
  } catch (error) {
    console.error('Error clearing medications and reminders:', error);
    return false;
  }
};

export default firebaseMedicationsService;
