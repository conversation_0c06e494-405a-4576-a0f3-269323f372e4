import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const SAVED_LOCATIONS_KEY = '@neurocare:saved_locations';
const SAVED_ROUTES_KEY = '@neurocare:saved_routes';

/**
 * Request location permissions from the user
 * @returns {Promise<boolean>} Whether permission was granted
 */
export const requestLocationPermissions = async () => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error requesting location permissions:', error);
    return false;
  }
};

/**
 * Get the current location of the device
 * @returns {Promise<Location.LocationObject|null>} The current location or null if unavailable
 */
export const getCurrentLocation = async () => {
  try {
    const hasPermission = await requestLocationPermissions();
    if (!hasPermission) {
      return null;
    }

    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
    });
    return location;
  } catch (error) {
    console.error('Error getting current location:', error);
    return null;
  }
};

/**
 * Save a location to AsyncStorage
 * @param {Object} location - The location to save
 * @param {string} location.name - The name of the location
 * @param {number} location.latitude - The latitude of the location
 * @param {number} location.longitude - The longitude of the location
 * @param {string} location.address - The address of the location (optional)
 * @param {string} location.notes - Notes about the location (optional)
 * @returns {Promise<boolean>} Whether the location was saved successfully
 */
export const saveLocation = async (location) => {
  try {
    // Get existing saved locations
    const savedLocationsJson = await AsyncStorage.getItem(SAVED_LOCATIONS_KEY);
    const savedLocations = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];

    // Add the new location with a unique ID and timestamp
    const newLocation = {
      ...location,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };

    // Save the updated locations
    const updatedLocations = [...savedLocations, newLocation];
    await AsyncStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(updatedLocations));
    return true;
  } catch (error) {
    console.error('Error saving location:', error);
    return false;
  }
};

/**
 * Get all saved locations
 * @returns {Promise<Array>} Array of saved locations
 */
export const getSavedLocations = async () => {
  try {
    const savedLocationsJson = await AsyncStorage.getItem(SAVED_LOCATIONS_KEY);
    return savedLocationsJson ? JSON.parse(savedLocationsJson) : [];
  } catch (error) {
    console.error('Error getting saved locations:', error);
    return [];
  }
};

/**
 * Delete a saved location
 * @param {string} locationId - The ID of the location to delete
 * @returns {Promise<boolean>} Whether the location was deleted successfully
 */
export const deleteLocation = async (locationId) => {
  try {
    const savedLocationsJson = await AsyncStorage.getItem(SAVED_LOCATIONS_KEY);
    const savedLocations = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];

    const updatedLocations = savedLocations.filter(location => location.id !== locationId);
    await AsyncStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(updatedLocations));
    return true;
  } catch (error) {
    console.error('Error deleting location:', error);
    return false;
  }
};

/**
 * Save a route to AsyncStorage
 * @param {Object} route - The route to save
 * @param {string} route.name - The name of the route
 * @param {Array} route.coordinates - Array of coordinates in the route
 * @param {string} route.notes - Notes about the route (optional)
 * @returns {Promise<boolean>} Whether the route was saved successfully
 */
export const saveRoute = async (route) => {
  try {
    // Get existing saved routes
    const savedRoutesJson = await AsyncStorage.getItem(SAVED_ROUTES_KEY);
    const savedRoutes = savedRoutesJson ? JSON.parse(savedRoutesJson) : [];

    // Add the new route with a unique ID and timestamp
    const newRoute = {
      ...route,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };

    // Save the updated routes
    const updatedRoutes = [...savedRoutes, newRoute];
    await AsyncStorage.setItem(SAVED_ROUTES_KEY, JSON.stringify(updatedRoutes));
    return true;
  } catch (error) {
    console.error('Error saving route:', error);
    return false;
  }
};

/**
 * Get all saved routes
 * @returns {Promise<Array>} Array of saved routes
 */
export const getSavedRoutes = async () => {
  try {
    const savedRoutesJson = await AsyncStorage.getItem(SAVED_ROUTES_KEY);
    return savedRoutesJson ? JSON.parse(savedRoutesJson) : [];
  } catch (error) {
    console.error('Error getting saved routes:', error);
    return [];
  }
};

/**
 * Delete a saved route
 * @param {string} routeId - The ID of the route to delete
 * @returns {Promise<boolean>} Whether the route was deleted successfully
 */
export const deleteRoute = async (routeId) => {
  try {
    const savedRoutesJson = await AsyncStorage.getItem(SAVED_ROUTES_KEY);
    const savedRoutes = savedRoutesJson ? JSON.parse(savedRoutesJson) : [];

    const updatedRoutes = savedRoutes.filter(route => route.id !== routeId);
    await AsyncStorage.setItem(SAVED_ROUTES_KEY, JSON.stringify(updatedRoutes));
    return true;
  } catch (error) {
    console.error('Error deleting route:', error);
    return false;
  }
};

/**
 * Start tracking a route
 * @returns {Promise<Location.LocationSubscription>} The location subscription
 */
export const startRouteTracking = async (onLocationUpdate) => {
  try {
    const hasPermission = await requestLocationPermissions();
    if (!hasPermission) {
      return null;
    }

    // Start watching position
    const subscription = await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.BestForNavigation,
        timeInterval: 5000,
        distanceInterval: 10,
      },
      onLocationUpdate
    );

    return subscription;
  } catch (error) {
    console.error('Error starting route tracking:', error);
    return null;
  }
};

/**
 * Get the address for a location
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 * @returns {Promise<string|null>} The address or null if unavailable
 */
export const getAddressFromCoordinates = async (latitude, longitude) => {
  try {
    const addresses = await Location.reverseGeocodeAsync({ latitude, longitude });
    if (addresses && addresses.length > 0) {
      const address = addresses[0];
      return `${address.street || ''} ${address.name || ''}, ${address.city || ''}, ${address.region || ''} ${address.postalCode || ''}, ${address.country || ''}`.trim();
    }
    return null;
  } catch (error) {
    console.error('Error getting address from coordinates:', error);
    return null;
  }
};
