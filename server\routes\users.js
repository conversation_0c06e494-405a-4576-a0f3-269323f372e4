const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');
const db = admin.firestore();

// Get user by code
router.get('/code/:userCode', verifyToken, async (req, res) => {
    try {
        const { userCode } = req.params;

        // Find the user with this code
        const usersSnapshot = await admin.firestore()
            .collection('users')
            .where('userCode', '==', userCode)
            .limit(1)
            .get();

        if (usersSnapshot.empty) {
            return res.status(404).json({ error: 'User not found with this code' });
        }

        const userDoc = usersSnapshot.docs[0];
        const userData = userDoc.data();

        // Don't send sensitive information
        const sanitizedUserData = {
            uid: userData.uid,
            displayName: userData.displayName,
            role: userData.role,
            userCode: userData.userCode
        };

        res.json(sanitizedUserData);
    } catch (error) {
        console.error('Error finding user by code:', error);
        res.status(500).json({ error: 'Failed to find user' });
    }
});

// Link a user (doctor-patient, nurse-patient, etc.)
router.post('/link', verifyToken, async (req, res) => {
    try {
        const currentUserId = req.user.uid;
        const { targetUserCode, relationshipType } = req.body;

        // Verify relationship type is valid
        const validRelationships = ['doctor-patient', 'nurse-patient', 'pharmacist-patient', 'supervisor-patient'];
        if (!validRelationships.includes(relationshipType)) {
            return res.status(400).json({ error: 'Invalid relationship type' });
        }

        // Find target user by code
        const usersSnapshot = await admin.firestore()
            .collection('users')
            .where('userCode', '==', targetUserCode)
            .limit(1)
            .get();

        if (usersSnapshot.empty) {
            return res.status(404).json({ error: 'User not found with this code' });
        }

        const targetUserDoc = usersSnapshot.docs[0];
        const targetUserData = targetUserDoc.data();
        const targetUserId = targetUserData.uid;

        // Create relationship in Firestore
        await admin.firestore().collection('userRelationships').add({
            initiatorId: currentUserId,
            targetId: targetUserId,
            relationshipType,
            status: 'active',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Update the user's linked users arrays
        if (relationshipType === 'doctor-patient') {
            // Update doctor's patients
            await admin.firestore()
                .collection('users')
                .doc(currentUserId)
                .update({
                    linkedPatients: admin.firestore.FieldValue.arrayUnion(targetUserId)
                });

            // Update patient's doctors
            await admin.firestore()
                .collection('users')
                .doc(targetUserId)
                .update({
                    linkedDoctors: admin.firestore.FieldValue.arrayUnion(currentUserId)
                });
        } else if (relationshipType === 'nurse-patient') {
            await admin.firestore()
                .collection('users')
                .doc(currentUserId)
                .update({
                    linkedPatients: admin.firestore.FieldValue.arrayUnion(targetUserId)
                });

            await admin.firestore()
                .collection('users')
                .doc(targetUserId)
                .update({
                    linkedNurses: admin.firestore.FieldValue.arrayUnion(currentUserId)
                });
        } else if (relationshipType === 'pharmacist-patient') {
            await admin.firestore()
                .collection('users')
                .doc(currentUserId)
                .update({
                    linkedPatients: admin.firestore.FieldValue.arrayUnion(targetUserId)
                });

            await admin.firestore()
                .collection('users')
                .doc(targetUserId)
                .update({
                    linkedPharmacists: admin.firestore.FieldValue.arrayUnion(currentUserId)
                });
        } else if (relationshipType === 'supervisor-patient') {
            await admin.firestore()
                .collection('users')
                .doc(currentUserId)
                .update({
                    linkedPatients: admin.firestore.FieldValue.arrayUnion(targetUserId)
                });

            await admin.firestore()
                .collection('users')
                .doc(targetUserId)
                .update({
                    linkedSupervisors: admin.firestore.FieldValue.arrayUnion(currentUserId)
                });
        }

        res.status(201).json({
            success: true,
            message: 'User linked successfully',
            linkedUser: {
                uid: targetUserId,
                displayName: targetUserData.displayName,
                role: targetUserData.role
            }
        });
    } catch (error) {
        console.error('Error linking user:', error);
        res.status(500).json({ error: 'Failed to link user' });
    }
});

// Get linked users
router.get('/linked/:relationshipType', verifyToken, async (req, res) => {
    try {
        const currentUserId = req.user.uid;
        const { relationshipType } = req.params;

        const userDoc = await admin.firestore()
            .collection('users')
            .doc(currentUserId)
            .get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        const userData = userDoc.data();
        let linkedUserIds = [];

        // Get the right array based on relationship type and user role
        if (userData.role === 'doctor' && relationshipType === 'patients') {
            linkedUserIds = userData.linkedPatients || [];
        } else if (userData.role === 'nurse' && relationshipType === 'patients') {
            linkedUserIds = userData.linkedPatients || [];
        } else if (userData.role === 'pharmacist' && relationshipType === 'patients') {
            linkedUserIds = userData.linkedPatients || [];
        } else if (userData.role === 'supervisor' && relationshipType === 'patients') {
            linkedUserIds = userData.linkedPatients || [];
        } else if (userData.role === 'patient' && relationshipType === 'doctors') {
            linkedUserIds = userData.linkedDoctors || [];
        } else if (userData.role === 'patient' && relationshipType === 'nurses') {
            linkedUserIds = userData.linkedNurses || [];
        } else if (userData.role === 'patient' && relationshipType === 'pharmacists') {
            linkedUserIds = userData.linkedPharmacists || [];
        } else if (userData.role === 'patient' && relationshipType === 'supervisors') {
            linkedUserIds = userData.linkedSupervisors || [];
        } else {
            return res.status(400).json({ error: 'Invalid relationship type for this user role' });
        }

        // If no linked users, return empty array
        if (linkedUserIds.length === 0) {
            return res.json([]);
        }

        // Get user data for all linked users
        const linkedUsers = [];
        for (const userId of linkedUserIds) {
            const userRef = admin.firestore().collection('users').doc(userId);
            const linkedUserDoc = await userRef.get();

            if (linkedUserDoc.exists) {
                const linkedUserData = linkedUserDoc.data();
                linkedUsers.push({
                    uid: linkedUserData.uid,
                    displayName: linkedUserData.displayName,
                    role: linkedUserData.role
                });
            }
        }

        res.json(linkedUsers);
    } catch (error) {
        console.error('Error getting linked users:', error);
        res.status(500).json({ error: 'Failed to get linked users' });
    }
});

// Admin routes for user management
// Middleware to check if user is admin
const isAdmin = async (req, res, next) => {
    try {
        // Remove the development bypass
        const userId = req.user.uid;
        const userDoc = await admin.firestore()
            .collection('users')
            .doc(userId)
            .get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        const userData = userDoc.data();
        if (userData.role !== 'admin') {
            return res.status(403).json({ error: 'Access denied. Admin privileges required.' });
        }

        next();
    } catch (error) {
        console.error('Error checking admin status:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Get all users (admin only)
router.get('/admin/all', verifyToken, isAdmin, async (req, res) => {
    try {
        const usersSnapshot = await admin.firestore()
            .collection('users')
            .get();

        const users = [];
        usersSnapshot.forEach(doc => {
            const userData = doc.data();
            let createdAtDate = null;

            // Safely handle createdAt conversion
            if (userData.createdAt) {
                // Check if it's a Firestore timestamp (has toDate method)
                if (typeof userData.createdAt.toDate === 'function') {
                    createdAtDate = userData.createdAt.toDate();
                } else if (userData.createdAt instanceof Date) {
                    // It's already a Date object
                    createdAtDate = userData.createdAt;
                } else if (typeof userData.createdAt === 'string') {
                    // It's a string, try to parse it
                    createdAtDate = new Date(userData.createdAt);
                }
            }

            users.push({
                uid: userData.uid,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                displayName: userData.displayName || '',
                email: userData.email || '',
                role: userData.role || '',
                speciality: userData.speciality || '',
                location: userData.location || { city: '', country: '' },
                status: userData.status || 'active',
                createdAt: createdAtDate
            });
        });

        res.json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ error: 'Failed to fetch users' });
    }
});

// Get user by ID (admin only)
router.get('/admin/:uid', verifyToken, isAdmin, async (req, res) => {
    try {
        const { uid } = req.params;
        const userDoc = await admin.firestore()
            .collection('users')
            .doc(uid)
            .get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        const userData = userDoc.data();
        res.json(userData);
    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({ error: 'Failed to fetch user' });
    }
});

// Create new user (admin only)
router.post('/admin', verifyToken, isAdmin, async (req, res) => {
    try {
        const { email, password, firstName, lastName, role, speciality } = req.body;

        // Create user in Firebase Auth
        const userRecord = await admin.auth().createUser({
            email,
            password,
            displayName: `${firstName} ${lastName}`
        });

        // Create user document in Firestore
        const userData = {
            uid: userRecord.uid,
            email,
            firstName,
            lastName,
            displayName: `${firstName} ${lastName}`,
            role: role || 'patient',
            speciality: speciality || '',
            status: 'active',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        };

        await admin.firestore()
            .collection('users')
            .doc(userRecord.uid)
            .set(userData);

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            user: {
                uid: userRecord.uid,
                email,
                displayName: `${firstName} ${lastName}`,
                role: userData.role
            }
        });
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ error: 'Failed to create user', message: error.message });
    }
});

// Update user (admin only)
router.put('/admin/:uid', verifyToken, isAdmin, async (req, res) => {
    try {
        const { uid } = req.params;
        const { firstName, lastName, role, speciality, status, location } = req.body;

        // First check if user exists
        const userDoc = await admin.firestore()
            .collection('users')
            .doc(uid)
            .get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Update user data
        const updateData = {
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        };

        if (firstName && lastName) {
            updateData.firstName = firstName;
            updateData.lastName = lastName;
            updateData.displayName = `${firstName} ${lastName}`;

            // Update Auth displayName too
            try {
                await admin.auth().updateUser(uid, {
                    displayName: `${firstName} ${lastName}`
                });
            } catch (authError) {
                console.log(`User ${uid} not found in Auth, skipping Auth update: ${authError.message}`);
            }
        }

        if (role) updateData.role = role;
        if (speciality) updateData.speciality = speciality;
        if (status) updateData.status = status;

        // Handle location update
        if (location) {
            updateData.location = {
                city: location.city || '',
                country: location.country || ''
            };
        }

        await admin.firestore()
            .collection('users')
            .doc(uid)
            .update(updateData);

        res.json({
            success: true,
            message: 'User updated successfully'
        });
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ error: 'Failed to update user' });
    }
});

// Delete user (admin only)
router.delete('/admin/:uid', verifyToken, isAdmin, async (req, res) => {
    try {
        const { uid } = req.params;

        if (!uid) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'A valid user ID is required to delete a user'
            });
        }

        // First check if user exists in Firestore
        const userDoc = await admin.firestore()
            .collection('users')
            .doc(uid)
            .get();

        if (!userDoc.exists) {
            return res.status(404).json({
                error: 'User not found',
                message: 'The specified user does not exist in the database'
            });
        }

        // Try to delete from Auth (might fail if user was created in Firestore directly)
        try {
            await admin.auth().deleteUser(uid);
            console.log(`User auth record deleted for ${uid}`);
        } catch (authError) {
            // If there's no auth user, just log the error and continue with Firestore deletion
            console.log(`No auth record found for ${uid}: ${authError.message}`);
        }

        // Delete from Firestore
        await admin.firestore()
            .collection('users')
            .doc(uid)
            .delete();

        res.json({
            success: true,
            message: 'User deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({
            error: 'Failed to delete user',
            message: error.message
        });
    }
});

// Ban/unban user (admin only)
router.put('/admin/:uid/ban', verifyToken, isAdmin, async (req, res) => {
    try {
        const { uid } = req.params;
        const { banned } = req.body;

        // Update the user Auth status
        await admin.auth().updateUser(uid, {
            disabled: banned
        });

        // Update user document
        await admin.firestore()
            .collection('users')
            .doc(uid)
            .update({
                status: banned ? 'banned' : 'active',
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });

        res.json({
            success: true,
            message: banned ? 'User banned successfully' : 'User unbanned successfully'
        });
    } catch (error) {
        console.error('Error banning/unbanning user:', error);
        res.status(500).json({ error: 'Failed to ban/unban user' });
    }
});

// Middleware to verify authentication
const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Invalid token' });
    }
};

// Update FCM token
router.post('/fcm-token', authenticateUser, async (req, res) => {
    try {
        const { fcmToken } = req.body;
        const userId = req.user.uid;

        if (!fcmToken) {
            return res.status(400).json({ error: 'FCM token is required' });
        }

        await db.collection('users').doc(userId).update({
            fcmToken,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        res.json({ message: 'FCM token updated successfully' });
    } catch (error) {
        console.error('Update FCM token error:', error);
        res.status(500).json({ error: 'Failed to update FCM token' });
    }
});

// Supervisor assigns caregiver to patient
router.post('/assign-caregiver', verifyToken, async (req, res) => {
    try {
        const supervisorId = req.user.uid;
        const { patientId, caregiverId } = req.body;

        if (!patientId || !caregiverId) {
            return res.status(400).json({ error: 'Patient ID and Caregiver ID are required' });
        }

        // Verify the current user is a supervisor
        const supervisorDoc = await admin.firestore()
            .collection('users')
            .doc(supervisorId)
            .get();

        if (!supervisorDoc.exists) {
            return res.status(404).json({ error: 'Supervisor not found' });
        }

        const supervisorData = supervisorDoc.data();
        if (supervisorData.role !== 'supervisor') {
            return res.status(403).json({ error: 'Only supervisors can assign caregivers' });
        }

        // Verify the patient exists and is linked to this supervisor
        const patientDoc = await admin.firestore()
            .collection('users')
            .doc(patientId)
            .get();

        if (!patientDoc.exists) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        const patientData = patientDoc.data();
        if (patientData.role !== 'patient') {
            return res.status(400).json({ error: 'Target user is not a patient' });
        }

        // Check if patient is linked to this supervisor
        const linkedSupervisors = patientData.linkedSupervisors || [];
        if (!linkedSupervisors.includes(supervisorId)) {
            return res.status(403).json({ error: 'Patient is not linked to this supervisor' });
        }

        // Verify the caregiver exists
        const caregiverDoc = await admin.firestore()
            .collection('users')
            .doc(caregiverId)
            .get();

        if (!caregiverDoc.exists) {
            return res.status(404).json({ error: 'Caregiver not found' });
        }

        const caregiverData = caregiverDoc.data();
        if (caregiverData.role !== 'caregiver') {
            return res.status(400).json({ error: 'Target user is not a caregiver' });
        }

        // Create caregiver relationship
        const relationshipData = {
            patientId,
            caregiverId,
            supervisorId,
            status: 'active', // Automatically active since assigned by supervisor
            permissions: ['view_profile', 'view_medications', 'view_appointments'], // Default permissions
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            acceptedAt: admin.firestore.FieldValue.serverTimestamp() // Auto-accepted
        };

        const relationshipRef = await admin.firestore()
            .collection('caregiverRelationships')
            .add(relationshipData);

        // Update caregiver's linkedPatients array
        await admin.firestore()
            .collection('users')
            .doc(caregiverId)
            .update({
                linkedPatients: admin.firestore.FieldValue.arrayUnion(patientId)
            });

        // Update patient's linkedCaregivers array
        await admin.firestore()
            .collection('users')
            .doc(patientId)
            .update({
                linkedCaregivers: admin.firestore.FieldValue.arrayUnion(caregiverId)
            });

        res.status(201).json({
            success: true,
            message: 'Caregiver assigned to patient successfully',
            relationshipId: relationshipRef.id
        });
    } catch (error) {
        console.error('Error assigning caregiver:', error);
        res.status(500).json({ error: 'Failed to assign caregiver' });
    }
});

// Supervisor assigns caregiver to patient by code
router.post('/assign-caregiver-by-code', verifyToken, async (req, res) => {
    try {
        const supervisorId = req.user.uid;
        const { patientId, caregiverCode } = req.body;

        if (!patientId || !caregiverCode) {
            return res.status(400).json({ error: 'Patient ID and Caregiver Code are required' });
        }

        // Verify the current user is a supervisor
        const supervisorDoc = await admin.firestore()
            .collection('users')
            .doc(supervisorId)
            .get();

        if (!supervisorDoc.exists) {
            return res.status(404).json({ error: 'Supervisor not found' });
        }

        const supervisorData = supervisorDoc.data();
        if (supervisorData.role !== 'supervisor') {
            return res.status(403).json({ error: 'Only supervisors can assign caregivers' });
        }

        // Verify the patient exists and is linked to this supervisor
        const patientDoc = await admin.firestore()
            .collection('users')
            .doc(patientId)
            .get();

        if (!patientDoc.exists) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        const patientData = patientDoc.data();
        if (patientData.role !== 'patient') {
            return res.status(400).json({ error: 'Target user is not a patient' });
        }

        // Check if patient is linked to this supervisor
        const linkedSupervisors = patientData.linkedSupervisors || [];
        if (!linkedSupervisors.includes(supervisorId)) {
            return res.status(403).json({ error: 'Patient is not linked to this supervisor' });
        }

        // Find caregiver by code
        const caregiversSnapshot = await admin.firestore()
            .collection('users')
            .where('userCode', '==', caregiverCode)
            .where('role', '==', 'caregiver')
            .limit(1)
            .get();

        if (caregiversSnapshot.empty) {
            return res.status(404).json({ error: 'No caregiver found with this code' });
        }

        const caregiverDoc = caregiversSnapshot.docs[0];
        const caregiverData = caregiverDoc.data();
        const caregiverId = caregiverData.uid;

        // Create caregiver relationship
        const relationshipData = {
            patientId,
            caregiverId,
            supervisorId,
            status: 'active', // Automatically active since assigned by supervisor
            permissions: ['view_profile', 'view_medications', 'view_appointments'], // Default permissions
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            acceptedAt: admin.firestore.FieldValue.serverTimestamp() // Auto-accepted
        };

        const relationshipRef = await admin.firestore()
            .collection('caregiverRelationships')
            .add(relationshipData);

        // Update caregiver's linkedPatients array
        await admin.firestore()
            .collection('users')
            .doc(caregiverId)
            .update({
                linkedPatients: admin.firestore.FieldValue.arrayUnion(patientId)
            });

        // Update patient's linkedCaregivers array
        await admin.firestore()
            .collection('users')
            .doc(patientId)
            .update({
                linkedCaregivers: admin.firestore.FieldValue.arrayUnion(caregiverId)
            });

        res.status(201).json({
            success: true,
            message: 'Caregiver assigned to patient successfully',
            relationshipId: relationshipRef.id,
            caregiverName: caregiverData.displayName || `${caregiverData.firstName || ''} ${caregiverData.lastName || ''}`.trim()
        });
    } catch (error) {
        console.error('Error assigning caregiver by code:', error);
        res.status(500).json({ error: 'Failed to assign caregiver by code' });
    }
});

// Get available caregivers for supervisor
router.get('/available-caregivers', verifyToken, async (req, res) => {
    try {
        const supervisorId = req.user.uid;

        // Verify the current user is a supervisor
        const supervisorDoc = await admin.firestore()
            .collection('users')
            .doc(supervisorId)
            .get();

        if (!supervisorDoc.exists) {
            return res.status(404).json({ error: 'Supervisor not found' });
        }

        const supervisorData = supervisorDoc.data();
        if (supervisorData.role !== 'supervisor') {
            return res.status(403).json({ error: 'Only supervisors can view available caregivers' });
        }

        // Get all users with role 'caregiver'
        const caregiversSnapshot = await admin.firestore()
            .collection('users')
            .where('role', '==', 'caregiver')
            .get();

        const caregivers = [];
        caregiversSnapshot.forEach(doc => {
            const caregiverData = doc.data();
            caregivers.push({
                uid: caregiverData.uid,
                displayName: caregiverData.displayName || `${caregiverData.firstName || ''} ${caregiverData.lastName || ''}`.trim(),
                firstName: caregiverData.firstName || '',
                lastName: caregiverData.lastName || '',
                email: caregiverData.email || '',
                speciality: caregiverData.speciality || '',
                location: caregiverData.location || { city: '', country: '' }
            });
        });

        res.json(caregivers);
    } catch (error) {
        console.error('Error getting available caregivers:', error);
        res.status(500).json({ error: 'Failed to get available caregivers' });
    }
});

// Get caregivers assigned to a patient
router.get('/patient-caregivers/:patientId', verifyToken, async (req, res) => {
    try {
        const supervisorId = req.user.uid;
        const { patientId } = req.params;

        // Verify the current user is a supervisor
        const supervisorDoc = await admin.firestore()
            .collection('users')
            .doc(supervisorId)
            .get();

        if (!supervisorDoc.exists) {
            return res.status(404).json({ error: 'Supervisor not found' });
        }

        const supervisorData = supervisorDoc.data();
        if (supervisorData.role !== 'supervisor') {
            return res.status(403).json({ error: 'Only supervisors can view patient caregivers' });
        }

        // Verify the patient exists and is linked to this supervisor
        const patientDoc = await admin.firestore()
            .collection('users')
            .doc(patientId)
            .get();

        if (!patientDoc.exists) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        // Check if patient is linked to this supervisor
        const patientData = patientDoc.data();
        const linkedSupervisors = patientData.linkedSupervisors || [];
        if (!linkedSupervisors.includes(supervisorId)) {
            return res.status(403).json({ error: 'Patient is not linked to this supervisor' });
        }

        // Get all caregiver relationships for this patient
        const relationshipsSnapshot = await admin.firestore()
            .collection('caregiverRelationships')
            .where('patientId', '==', patientId)
            .where('status', '==', 'active')
            .get();

        const caregiverIds = [];
        relationshipsSnapshot.forEach(doc => {
            const relationshipData = doc.data();
            caregiverIds.push(relationshipData.caregiverId);
        });

        // If no caregivers found, return empty array
        if (caregiverIds.length === 0) {
            return res.json([]);
        }

        // Get caregiver details
        const caregivers = [];
        for (const caregiverId of caregiverIds) {
            const caregiverDoc = await admin.firestore()
                .collection('users')
                .doc(caregiverId)
                .get();

            if (caregiverDoc.exists) {
                const caregiverData = caregiverDoc.data();
                caregivers.push({
                    uid: caregiverData.uid,
                    displayName: caregiverData.displayName || `${caregiverData.firstName || ''} ${caregiverData.lastName || ''}`.trim(),
                    firstName: caregiverData.firstName || '',
                    lastName: caregiverData.lastName || '',
                    email: caregiverData.email || '',
                    speciality: caregiverData.speciality || ''
                });
            }
        }

        res.json(caregivers);
    } catch (error) {
        console.error('Error getting patient caregivers:', error);
        res.status(500).json({ error: 'Failed to get patient caregivers' });
    }
});

// Remove caregiver assignment
router.delete('/remove-caregiver-assignment', verifyToken, async (req, res) => {
    try {
        const supervisorId = req.user.uid;
        const { patientId, caregiverId } = req.body;

        if (!patientId || !caregiverId) {
            return res.status(400).json({ error: 'Patient ID and Caregiver ID are required' });
        }

        // Verify the current user is a supervisor
        const supervisorDoc = await admin.firestore()
            .collection('users')
            .doc(supervisorId)
            .get();

        if (!supervisorDoc.exists) {
            return res.status(404).json({ error: 'Supervisor not found' });
        }

        const supervisorData = supervisorDoc.data();
        if (supervisorData.role !== 'supervisor') {
            return res.status(403).json({ error: 'Only supervisors can remove caregiver assignments' });
        }

        // Find the relationship
        const relationshipsSnapshot = await admin.firestore()
            .collection('caregiverRelationships')
            .where('patientId', '==', patientId)
            .where('caregiverId', '==', caregiverId)
            .where('status', '==', 'active')
            .get();

        if (relationshipsSnapshot.empty) {
            return res.status(404).json({ error: 'Caregiver relationship not found' });
        }

        // Update the relationship status to 'inactive'
        const batch = admin.firestore().batch();
        relationshipsSnapshot.forEach(doc => {
            batch.update(doc.ref, {
                status: 'inactive',
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
        });

        await batch.commit();

        // Remove from linked arrays
        await admin.firestore()
            .collection('users')
            .doc(caregiverId)
            .update({
                linkedPatients: admin.firestore.FieldValue.arrayRemove(patientId)
            });

        await admin.firestore()
            .collection('users')
            .doc(patientId)
            .update({
                linkedCaregivers: admin.firestore.FieldValue.arrayRemove(caregiverId)
            });

        res.json({
            success: true,
            message: 'Caregiver assignment removed successfully'
        });
    } catch (error) {
        console.error('Error removing caregiver assignment:', error);
        res.status(500).json({ error: 'Failed to remove caregiver assignment' });
    }
});

module.exports = router;