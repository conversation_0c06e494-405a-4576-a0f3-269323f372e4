import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { ROLE_COLORS } from '../config/theme';
import { LinearGradient } from 'expo-linear-gradient';
import { db, collection, query, where, getDocs } from '../config/firebase';

const MessagesScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isStartingCall, setIsStartingCall] = useState(false);

  const doctorColors = ROLE_COLORS.doctor;

  // Fetch patients linked to the doctor
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        console.log('Fetching patients for doctor:', user);

        // Query users collection for patients linked to this doctor
        const usersCollection = collection(db, 'users');
        const patientsQuery = query(
          usersCollection,
          where('role', '==', 'patient')
        );

        const querySnapshot = await getDocs(patientsQuery);
        console.log('Found', querySnapshot.size, 'patients in total');

        // Filter patients to get only those linked to this doctor
        const patientsList = [];
        querySnapshot.forEach((doc) => {
          const userData = doc.data();
          // Check if this patient is linked to the current doctor
          const linkedDoctors = userData.linkedDoctors || [];
          console.log('Patient:', doc.id, 'linkedDoctors:', linkedDoctors);
          console.log('Current doctor uid:', user.uid);

          if (linkedDoctors.includes(user.uid)) {
            patientsList.push({
              id: doc.id,
              firstName: userData.firstName || '',
              lastName: userData.lastName || '',
              email: userData.email || '',
              phone: userData.phone || '',
              lastConsultation: userData.lastVisit || new Date().toISOString().split('T')[0],
              status: Math.random() > 0.5 ? 'online' : 'offline', // Simulate online status randomly
              profileImage: userData.profileImage || null
            });
          }
        });

        console.log('Filtered patients linked to doctor:', patientsList.length);
        setPatients(patientsList);
      } catch (error) {
        console.error('Error fetching patients:', error);
        Alert.alert('Error', 'Failed to load patients. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, [user.uid]);

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Start video call with selected patient
  const startVideoCall = async (patient) => {
    if (isStartingCall) return;

    try {
      setIsStartingCall(true);

      // Generate a unique room name
      const roomName = `neurocare_${Date.now()}_${patient.id}`;

      // Navigate to the ChatRoom screen with the patient info and room name
      navigation.navigate('Chatroom', {
        patientInfo: patient,
        startVideoCall: true,
        roomName: roomName
      });
    } catch (error) {
      console.error('Error starting video call:', error);
      Alert.alert('Error', 'Failed to start video call. Please try again.');
    } finally {
      setIsStartingCall(false);
    }
  };

  // Start chat with selected patient
  const startChat = (patient) => {
    navigation.navigate('Chatroom', {
      patientInfo: patient
    });
  };

  // Render patient item
  const renderPatientItem = ({ item }) => {
    const isSelected = selectedPatient && selectedPatient.id === item.id;

    return (
      <TouchableOpacity
        style={[
          styles.patientCard,
          isSelected && styles.selectedPatientCard
        ]}
        onPress={() => setSelectedPatient(item)}
      >
        <View style={styles.patientAvatarContainer}>
          <Text style={styles.patientAvatarText}>
            {item.firstName.charAt(0)}{item.lastName.charAt(0)}
          </Text>
          <View style={[
            styles.statusIndicator,
            item.status === 'online' ? styles.statusOnline : styles.statusOffline
          ]} />
        </View>

        <View style={styles.patientInfo}>
          <Text style={styles.patientName}>{item.firstName} {item.lastName}</Text>
          <Text style={styles.patientEmail}>{item.email}</Text>
          <Text style={styles.patientPhone}>{item.phone || 'No phone number'}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search patients..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      ) : (
        <View style={styles.contentContainer}>
          {filteredPatients.length > 0 ? (
            <FlatList
              data={filteredPatients}
              renderItem={renderPatientItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.patientList}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="people" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No patients found</Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? 'Try a different search term' : 'You don\'t have any patients linked to your account yet'}
              </Text>
            </View>
          )}

          {selectedPatient && (
            <View style={styles.selectedPatientOverlay}>
              <View style={styles.patientDetailContainer}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setSelectedPatient(null)}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>

                <View style={styles.patientDetailHeader}>
                  <View style={styles.patientDetailAvatar}>
                    <Text style={styles.patientDetailAvatarText}>
                      {selectedPatient.firstName.charAt(0)}{selectedPatient.lastName.charAt(0)}
                    </Text>
                  </View>
                  <View>
                    <Text style={styles.patientDetailName}>
                      {selectedPatient.firstName} {selectedPatient.lastName}
                    </Text>
                    <Text style={styles.patientDetailInfo}>{selectedPatient.email}</Text>
                    <Text style={styles.patientDetailInfo}>
                      {selectedPatient.phone || 'No phone number'}
                    </Text>
                    <View style={styles.statusContainer}>
                      <View style={[
                        styles.statusDot,
                        selectedPatient.status === 'online' ? styles.onlineDot : styles.offlineDot
                      ]} />
                      <Text style={styles.statusText}>
                        {selectedPatient.status === 'online' ? 'Online' : 'Offline'}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.actionButtonsContainer}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.videoCallButton]}
                    onPress={() => startVideoCall(selectedPatient)}
                    disabled={isStartingCall}
                  >
                    <Ionicons name="videocam" size={24} color="#fff" />
                    <Text style={styles.actionButtonText}>Start Video Call</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.actionButton, styles.chatButton]}
                    onPress={() => startChat(selectedPatient)}
                  >
                    <Ionicons name="chatbubble" size={24} color="#fff" />
                    <Text style={styles.actionButtonText}>Start Chat</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.infoContainer}>
                  <Text style={styles.infoTitle}>Last Consultation</Text>
                  <Text style={styles.infoValue}>
                    {new Date(selectedPatient.lastConsultation).toLocaleDateString()}
                  </Text>
                </View>
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    paddingTop: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 16,
    paddingHorizontal: 12,
    height: 50,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: '#333',
  },
  patientList: {
    padding: 8,
  },
  patientCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#eee',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  selectedPatientCard: {
    borderColor: ROLE_COLORS.doctor.primary,
    borderWidth: 2,
    backgroundColor: '#f0f7ff',
  },
  patientAvatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ROLE_COLORS.doctor.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    position: 'relative',
  },
  patientAvatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    position: 'absolute',
    bottom: 0,
    right: 0,
    borderWidth: 2,
    borderColor: '#fff',
  },
  statusOnline: {
    backgroundColor: '#4CAF50',
  },
  statusOffline: {
    backgroundColor: '#9E9E9E',
  },
  patientInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  patientEmail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  patientPhone: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 8,
    marginTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 5,
  },
  selectedPatientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  patientDetailContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1,
    padding: 5,
  },
  patientDetailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  patientDetailAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: ROLE_COLORS.doctor.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  patientDetailAvatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  patientDetailName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  patientDetailInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 5,
  },
  onlineDot: {
    backgroundColor: '#4CAF50',
  },
  offlineDot: {
    backgroundColor: '#9E9E9E',
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
  actionButtonsContainer: {
    flexDirection: 'column',
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  videoCallButton: {
    backgroundColor: ROLE_COLORS.doctor.primary,
  },
  chatButton: {
    backgroundColor: '#2196F3',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 10,
    fontSize: 16,
  },
  infoContainer: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: '#666',
  },
});

export default MessagesScreen;
