import AsyncStorage from '@react-native-async-storage/async-storage';

// Key for AsyncStorage
const VITALS_STORAGE_KEY = '@neurocare:vitals';

/**
 * Service for managing local storage of vitals data
 */
export const localVitalsService = {
  /**
   * Save a vital record locally
   * @param {Object} vitalData - The vital data to save
   * @returns {Promise<Object>} - The saved vital record with ID
   */
  saveVitalRecord: async (vitalData) => {
    try {
      // Get existing vitals
      const existingVitalsStr = await AsyncStorage.getItem(VITALS_STORAGE_KEY);
      const existingVitals = existingVitalsStr ? JSON.parse(existingVitalsStr) : [];

      // Create new record with ID and timestamp
      const newRecord = {
        id: Date.now().toString(),
        ...vitalData,
        timestamp: vitalData.timestamp || new Date().toISOString(),
      };

      // Add to existing records
      const updatedVitals = [newRecord, ...existingVitals];

      // Save back to storage
      await AsyncStorage.setItem(VITALS_STORAGE_KEY, JSON.stringify(updatedVitals));

      return newRecord;
    } catch (error) {
      console.error('Error saving vital record locally:', error);
      throw new Error('Failed to save vital record');
    }
  },

  /**
   * Get all vital records for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of vital records
   */
  getPatientVitals: async (patientId) => {
    try {
      // Get all vitals
      const vitalsStr = await AsyncStorage.getItem(VITALS_STORAGE_KEY);
      const allVitals = vitalsStr ? JSON.parse(vitalsStr) : [];

      // Filter by patient ID
      let filteredVitals = allVitals.filter(record => record.userId === patientId || record.patientId === patientId);

      // Sort by timestamp (newest first)
      filteredVitals.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      return filteredVitals;
    } catch (error) {
      console.error('Error getting patient vitals locally:', error);
      return [];
    }
  },

  /**
   * Get the latest vital of each type for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Object>} - Object with latest vitals by type
   */
  getLatestVitals: async (patientId) => {
    try {
      const allVitals = await localVitalsService.getPatientVitals(patientId);

      // Group by vital type
      const vitalTypes = ['heartRate', 'bloodPressure', 'bloodGlucose', 'weight'];
      const latestVitals = {};

      for (const type of vitalTypes) {
        const typeVitals = allVitals.filter(v => v.vitalType === type);
        if (typeVitals.length > 0) {
          latestVitals[type] = typeVitals[0]; // Already sorted newest first
        }
      }

      return latestVitals;
    } catch (error) {
      console.error('Error getting latest vitals:', error);
      return {};
    }
  },

  /**
   * Delete a vital record
   * @param {string} vitalId - The vital record ID to delete
   * @returns {Promise<boolean>} - Success status
   */
  deleteVitalRecord: async (vitalId) => {
    try {
      // Get all vitals
      const vitalsStr = await AsyncStorage.getItem(VITALS_STORAGE_KEY);
      const allVitals = vitalsStr ? JSON.parse(vitalsStr) : [];

      // Find the vital to delete
      const vitalIndex = allVitals.findIndex(v => v.id === vitalId);

      if (vitalIndex === -1) {
        return false; // Vital not found
      }

      // Remove the vital
      allVitals.splice(vitalIndex, 1);

      // Save back to storage
      await AsyncStorage.setItem(VITALS_STORAGE_KEY, JSON.stringify(allVitals));

      return true;
    } catch (error) {
      console.error('Error deleting vital record:', error);
      return false;
    }
  },


};

export default localVitalsService;
