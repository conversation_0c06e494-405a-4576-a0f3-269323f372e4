import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ROLE_COLORS } from '../../../config/theme';

const PatientDetailCard = ({ patient }) => {
  const [expandedSections, setExpandedSections] = useState({
    personalInfo: true,
    contactInfo: false,
    medicalInfo: false,
    emergencyContacts: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Format date string to a readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return dateString || 'Not specified';
    }
  };

  // Render a section header with toggle functionality
  const renderSectionHeader = (title, section, icon) => (
    <TouchableOpacity
      style={styles.sectionHeader}
      onPress={() => toggleSection(section)}
    >
      <View style={styles.sectionHeaderLeft}>
        <Ionicons name={icon} size={20} color={ROLE_COLORS.doctor.primary} />
        <Text style={styles.sectionTitle}>{title}</Text>
      </View>
      <Ionicons
        name={expandedSections[section] ? 'chevron-up' : 'chevron-down'}
        size={20}
        color={ROLE_COLORS.doctor.primary}
      />
    </TouchableOpacity>
  );

  // Render a field with label and value
  const renderField = (label, value, icon) => (
    <View style={styles.fieldContainer}>
      {icon && <Ionicons name={icon} size={16} color="#666" style={styles.fieldIcon} />}
      <Text style={styles.fieldLabel}>{label}:</Text>
      <Text style={styles.fieldValue}>{value || 'Not specified'}</Text>
    </View>
  );

  // Render emergency contacts
  const renderEmergencyContacts = () => {
    if (!patient.emergencyContacts || patient.emergencyContacts.length === 0) {
      return <Text style={styles.emptyText}>No emergency contacts specified</Text>;
    }

    return patient.emergencyContacts.map((contact, index) => (
      <View key={index} style={styles.contactCard}>
        <Text style={styles.contactName}>{contact.name}</Text>
        <Text style={styles.contactRelationship}>{contact.relationship}</Text>
        <Text style={styles.contactPhone}>{contact.phoneNumber}</Text>
      </View>
    ));
  };

  // Render medical conditions
  const renderMedicalConditions = () => {
    const conditions = patient.medicalInfo?.conditions || [];

    if (conditions.length === 0) {
      return <Text style={styles.emptyText}>No medical conditions specified</Text>;
    }

    return (
      <View style={styles.conditionsContainer}>
        {conditions.map((condition, index) => (
          <View key={index} style={styles.conditionTag}>
            <Text style={styles.conditionText}>{condition}</Text>
          </View>
        ))}
      </View>
    );
  };

  // Render allergies
  const renderAllergies = () => {
    const allergies = patient.medicalInfo?.allergies || patient.allergies || [];

    if (allergies.length === 0) {
      return <Text style={styles.emptyText}>No allergies specified</Text>;
    }

    return (
      <View style={styles.allergiesContainer}>
        {allergies.map((allergy, index) => (
          <View key={index} style={styles.allergyTag}>
            <Text style={styles.allergyText}>{allergy}</Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} nestedScrollEnabled={true}>
      {/* Personal Information Section */}
      {renderSectionHeader('Personal Information', 'personalInfo', 'person')}
      {expandedSections.personalInfo && (
        <View style={styles.sectionContent}>
          {renderField('Full Name', `${patient.firstName} ${patient.lastName}`, 'person-outline')}
          {renderField('Gender', patient.gender, 'male-female-outline')}
          {renderField('Date of Birth', formatDate(patient.dateOfBirth), 'calendar-outline')}
          {renderField('Age', patient.age, 'time-outline')}
          {renderField('Blood Type', patient.bloodType || patient.medicalInfo?.bloodType, 'water-outline')}
          {renderField('Height', patient.height || patient.medicalInfo?.height, 'resize-outline')}
          {renderField('Weight', patient.weight || patient.medicalInfo?.weight, 'scale-outline')}
        </View>
      )}

      {/* Contact Information Section */}
      {renderSectionHeader('Contact Information', 'contactInfo', 'call')}
      {expandedSections.contactInfo && (
        <View style={styles.sectionContent}>
          {renderField('Email', patient.email, 'mail-outline')}
          {renderField('Phone', patient.phone, 'call-outline')}
          {renderField('Address', patient.address, 'home-outline')}
          {renderField('City', patient.city, 'location-outline')}
          {renderField('Country', patient.country, 'globe-outline')}
          {renderField('Postal Code', patient.postalCode, 'map-outline')}
        </View>
      )}

      {/* Medical Information Section */}
      {renderSectionHeader('Medical Information', 'medicalInfo', 'medical')}
      {expandedSections.medicalInfo && (
        <View style={styles.sectionContent}>
          {renderField('Primary Diagnosis', patient.medicalInfo?.primaryDiagnosis, 'fitness-outline')}

          <Text style={styles.subSectionTitle}>Medical Conditions</Text>
          {renderMedicalConditions()}

          <Text style={styles.subSectionTitle}>Allergies</Text>
          {renderAllergies()}

          {renderField('Medical History', patient.medicalHistory, 'document-text-outline')}
          {renderField('Insurance Provider', patient.insuranceProvider, 'card-outline')}
          {renderField('Insurance Number', patient.insuranceNumber, 'key-outline')}
        </View>
      )}

      {/* Emergency Contacts Section */}
      {renderSectionHeader('Emergency Contacts', 'emergencyContacts', 'alert-circle')}
      {expandedSections.emergencyContacts && (
        <View style={styles.sectionContent}>
          {renderEmergencyContacts()}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  sectionContent: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  fieldContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  fieldIcon: {
    marginRight: 6,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    width: 120,
    marginRight: 8,
  },
  fieldValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    marginVertical: 8,
  },
  subSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#555',
    marginTop: 12,
    marginBottom: 8,
  },
  conditionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  conditionTag: {
    backgroundColor: '#E3F2FD',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 10,
    margin: 4,
  },
  conditionText: {
    fontSize: 12,
    color: '#1976D2',
  },
  allergiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  allergyTag: {
    backgroundColor: '#FFEBEE',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 10,
    margin: 4,
  },
  allergyText: {
    fontSize: 12,
    color: '#D32F2F',
  },
  contactCard: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  contactName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  contactRelationship: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  contactPhone: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
});

export default PatientDetailCard;
