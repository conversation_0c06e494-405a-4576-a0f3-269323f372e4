// API URLs
// Use a function to get the API URL dynamically for different environments
const getApiUrl = () => {
  // Default to localhost for development
  let baseUrl = 'http://localhost:3001';

  // Try to use the local IP if available for device testing
  if (typeof window !== 'undefined' && window.location && window.location.hostname) {
    // When running in a browser
    baseUrl = `http://${window.location.hostname}:3001`;
  }

  // Using console.log to debug
  console.log('Using API URL:', baseUrl);

  return baseUrl;
};

export const API_URL = getApiUrl();

// Firebase Cloud Messaging
export const FCM_SENDER_ID = '123456789012'; // Replace with your actual sender ID

// Constants for notification types
export const NOTIFICATION_TYPES = {
  APPOINTMENT_UPDATE: 'appointment_update',
  APPOINTMENT_REQUEST: 'appointment_request',
  APPOINTMENT_CANCELLED: 'appointment_cancelled',
  MEDICATION_REMINDER: 'medication_reminder',
  MESSAGE: 'message'
};

// Appointment statuses
export const APPOINTMENT_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  MISSED: 'missed'
};

// Appointment types
export const APPOINTMENT_TYPES = {
  IN_PERSON: 'in-person',
  VIDEO_CALL: 'video-call',
  PHONE_CALL: 'phone-call'
};

// User roles
export const USER_ROLES = {
  PATIENT: 'patient',
  DOCTOR: 'doctor',
  CAREGIVER: 'caregiver',
  ADMIN: 'admin'
};