import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Chip,
  Pagination,
  Portal,
  Modal,
  List,
  Divider,
} from 'react-native-paper';
import { format } from 'date-fns';
import axios from 'axios';
import { API_URL } from '../../../config/constants';

const ITEMS_PER_PAGE = 10;

const AppointmentList = ({ token, onAppointmentSelected }) => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  useEffect(() => {
    fetchAppointments();
  }, [page]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/appointments/patient`, {
        headers: { Authorization: `Bearer ${token}` },
        params: {
          page,
          limit: ITEMS_PER_PAGE,
        },
      });
      setAppointments(response.data.appointments);
      setTotalPages(Math.ceil(response.data.total / ITEMS_PER_PAGE));
    } catch (error) {
      console.error('Error fetching appointments:', error);
      Alert.alert('Error', 'Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchAppointments();
    setRefreshing(false);
  };

  const handleCancelAppointment = async (appointmentId) => {
    try {
      setLoading(true);
      await axios.post(
        `${API_URL}/api/appointments/${appointmentId}/cancel`,
        { reason: 'Canceled by patient' },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      await fetchAppointments();
      setShowDetailsModal(false);
      Alert.alert('Success', 'Appointment canceled successfully');
    } catch (error) {
      console.error('Error canceling appointment:', error);
      Alert.alert('Error', 'Failed to cancel appointment');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA500';
      case 'confirmed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const renderAppointmentCard = (appointment) => (
    <Card
      key={appointment.id}
      style={styles.card}
      onPress={() => {
        setSelectedAppointment(appointment);
        setShowDetailsModal(true);
        if (onAppointmentSelected) {
          onAppointmentSelected(appointment);
        }
      }}
    >
      <Card.Content>
        <View style={styles.cardHeader}>
          <Title>{format(new Date(appointment.date), 'MMMM dd, yyyy')}</Title>
          <Chip
            style={[styles.statusChip, { backgroundColor: getStatusColor(appointment.status) }]}
          >
            {appointment.status.toUpperCase()}
          </Chip>
        </View>
        <Paragraph>Time: {appointment.time}</Paragraph>
        <Paragraph>Type: {appointment.type}</Paragraph>
        <Paragraph>Doctor: {appointment.doctor.displayName}</Paragraph>
      </Card.Content>
    </Card>
  );

  const renderDetailsModal = () => (
    <Portal>
      <Modal
        visible={showDetailsModal}
        onDismiss={() => setShowDetailsModal(false)}
        contentContainerStyle={styles.modal}
      >
        {selectedAppointment && (
          <ScrollView>
            <Title>Appointment Details</Title>
            <Divider style={styles.divider} />

            <List.Item
              title="Date"
              description={format(new Date(selectedAppointment.date), 'MMMM dd, yyyy')}
            />
            <List.Item
              title="Time"
              description={selectedAppointment.time}
            />
            <List.Item
              title="Type"
              description={selectedAppointment.type}
            />
            <List.Item
              title="Doctor"
              description={selectedAppointment.doctor.displayName}
            />
            <List.Item
              title="Reason"
              description={selectedAppointment.reason || 'No reason provided'}
            />

            <Divider style={styles.divider} />

            {selectedAppointment.status === 'pending' || selectedAppointment.status === 'confirmed' ? (
              <Button
                mode="contained"
                onPress={() => handleCancelAppointment(selectedAppointment.id)}
                style={styles.cancelButton}
              >
                Cancel Appointment
              </Button>
            ) : null}
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {appointments.length === 0 ? (
          <Text style={styles.noAppointments}>No appointments found</Text>
        ) : (
          <>
            {appointments.map(renderAppointmentCard)}
            <Pagination
              page={page}
              numberOfPages={totalPages}
              onPageChange={setPage}
              style={styles.pagination}
            />
          </>
        )}
      </ScrollView>
      {renderDetailsModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusChip: {
    height: 24,
  },
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  divider: {
    marginVertical: 16,
  },
  cancelButton: {
    backgroundColor: '#F44336',
    marginVertical: 16,
  },
  noAppointments: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  },
  pagination: {
    marginVertical: 16,
  },
});

export default AppointmentList;