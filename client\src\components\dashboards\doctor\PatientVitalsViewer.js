import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { firebaseDoctorPatientsService } from '../../../services/firebaseDoctorPatientsService';
import { LineChart } from 'react-native-chart-kit';
import { ROLE_COLORS } from '../../../config/theme';

const PatientVitalsViewer = ({ patientId, patientName }) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [patientVitals, setPatientVitals] = useState([]);
  const [selectedVital, setSelectedVital] = useState('heartRate');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedVitalRecord, setSelectedVitalRecord] = useState(null);
  const [timeRange, setTimeRange] = useState('week');
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientVitals();
  }, [patientId, selectedVital]);

  const fetchPatientVitals = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch vitals for the selected patient from Firebase
      const vitalsData = await firebaseDoctorPatientsService.getPatientVitals(patientId);
      console.log(`Fetched ${vitalsData.length} vital records for patient ${patientId}`);
      setPatientVitals(vitalsData || []);
    } catch (error) {
      console.error('Error fetching patient vitals from Firebase:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVitalChange = (vitalType) => {
    setSelectedVital(vitalType);
  };

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  const openVitalDetails = (vital) => {
    setSelectedVitalRecord(vital);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get vital value based on type
  const getVitalValue = (vital) => {
    if (!vital) return 'N/A';

    if (vital.vitalType === 'bloodPressure') {
      return `${vital.values.systolic}/${vital.values.diastolic} mmHg`;
    } else if (vital.vitalType === 'heartRate') {
      return `${vital.values.value} bpm`;
    } else if (vital.vitalType === 'bloodGlucose') {
      return `${vital.values.value} mg/dL`;
    } else if (vital.vitalType === 'weight') {
      return `${vital.values.value} kg`;
    }
    return 'N/A';
  };

  // Get vital type name
  const getVitalTypeName = (vital) => {
    if (!vital) return 'Unknown';

    switch (vital.vitalType) {
      case 'bloodPressure': return 'Blood Pressure';
      case 'heartRate': return 'Heart Rate';
      case 'bloodGlucose': return 'Blood Glucose';
      case 'weight': return 'Weight';
      default: return 'Unknown';
    }
  };

  // Get icon for vital type
  const getVitalIcon = (vitalType) => {
    switch (vitalType) {
      case 'bloodPressure': return 'fitness';
      case 'heartRate': return 'heart';
      case 'bloodGlucose': return 'water';
      case 'weight': return 'body';
      default: return 'pulse';
    }
  };

  // Get color for vital type
  const getVitalColor = (vitalType) => {
    switch (vitalType) {
      case 'bloodPressure': return '#4285F4'; // Blue
      case 'heartRate': return '#EA4335';     // Red
      case 'bloodGlucose': return '#FBBC05';  // Yellow/Orange
      case 'weight': return '#34A853';        // Green
      default: return '#4285F4';
    }
  };

  // Get Y-axis suffix for charts
  const getYAxisSuffix = () => {
    switch (selectedVital) {
      case 'bloodPressure': return ' mmHg';
      case 'heartRate': return ' bpm';
      case 'bloodGlucose': return ' mg/dL';
      case 'weight': return ' kg';
      default: return '';
    }
  };

  // Filter vitals by selected type
  const getFilteredVitals = () => {
    return patientVitals.filter(item => item.vitalType === selectedVital)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  };

  // Get chart data
  const getChartData = () => {
    const filteredVitals = getFilteredVitals();

    // Sort by date (oldest to newest for charts)
    filteredVitals.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Limit to last 7 days for week view or 30 days for month view
    const daysToShow = timeRange === 'week' ? 7 : 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToShow);

    const recentVitals = filteredVitals.filter(v => new Date(v.timestamp) >= cutoffDate);

    // Prepare data for chart
    const labels = recentVitals.map(v => {
      const date = new Date(v.timestamp);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    });

    // Get the appropriate color based on vital type
    const vitalColor = getVitalColor(selectedVital);

    const datasets = [{
      data: recentVitals.map(v => {
        if (selectedVital === 'bloodPressure') {
          return parseInt(v.values.systolic);
        } else {
          return parseInt(v.values.value);
        }
      }),
      color: () => vitalColor,
      strokeWidth: 2
    }];

    // If blood pressure, add diastolic as second dataset
    if (selectedVital === 'bloodPressure') {
      datasets.push({
        data: recentVitals.map(v => parseInt(v.values.diastolic)),
        color: () => `${vitalColor}80`, // Add transparency to the color
        strokeWidth: 2
      });
    }

    return {
      labels,
      datasets
    };
  };

  // Render the vital records list
  const renderVitalsList = () => {
    const filteredVitals = getFilteredVitals();

    if (filteredVitals.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name={getVitalIcon(selectedVital)} size={48} color={getVitalColor(selectedVital)} />
          <Text style={styles.emptyText}>No {getVitalTypeName({vitalType: selectedVital})} records found</Text>
        </View>
      );
    }

    return (
      <View>
        {filteredVitals.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.vitalCard, { borderLeftColor: getVitalColor(item.vitalType) }]}
            onPress={() => openVitalDetails(item)}
          >
            <View style={[styles.vitalIconContainer, { backgroundColor: getVitalColor(item.vitalType) }]}>
              <Ionicons name={getVitalIcon(item.vitalType)} size={24} color="#fff" />
            </View>
            <View style={styles.vitalInfo}>
              <Text style={styles.vitalType}>{getVitalTypeName(item)}</Text>
              <Text style={styles.vitalValue}>{getVitalValue(item)}</Text>
              <Text style={styles.vitalDate}>{formatDate(item.timestamp)}</Text>
            </View>
            <Ionicons name="chevron-forward" size={24} color="#ccc" />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render the chart section
  const renderChartSection = () => {
    const chartData = getChartData();

    if (chartData.labels.length === 0) {
      return (
        <View style={styles.emptyChartContainer}>
          <Text style={styles.emptyChartText}>Not enough data to display chart</Text>
        </View>
      );
    }

    return (
      <View style={styles.chartContainer}>
        <LineChart
          data={chartData}
          width={340}
          height={220}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 16
            },
            propsForDots: {
              r: '6',
              strokeWidth: '2',
              stroke: '#ffa726'
            }
          }}
          bezier
          style={styles.chart}
          yAxisSuffix={getYAxisSuffix()}
          fromZero={selectedVital === 'weight'}
        />
        <View style={styles.legendContainer}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: getVitalColor(selectedVital) }]} />
            <Text style={styles.legendText}>
              {selectedVital === 'bloodPressure' ? 'Systolic' : getVitalTypeName({vitalType: selectedVital})}
            </Text>
          </View>
          {selectedVital === 'bloodPressure' && (
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: `${getVitalColor(selectedVital)}80` }]} />
              <Text style={styles.legendText}>Diastolic</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedVitalRecord) return null;

    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Vital Sign Details</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent} nestedScrollEnabled={true}>
              <View style={[styles.modalVitalCard, { borderLeftColor: getVitalColor(selectedVitalRecord.vitalType) }]}>
                <View style={[styles.vitalIconContainer, { backgroundColor: getVitalColor(selectedVitalRecord.vitalType) }]}>
                  <Ionicons name={getVitalIcon(selectedVitalRecord.vitalType)} size={24} color="#fff" />
                </View>
                <View style={styles.vitalInfo}>
                  <Text style={styles.vitalType}>{getVitalTypeName(selectedVitalRecord)}</Text>
                  <Text style={styles.vitalValue}>{getVitalValue(selectedVitalRecord)}</Text>
                  <Text style={styles.vitalDate}>{formatDate(selectedVitalRecord.timestamp)}</Text>
                </View>
              </View>

              {selectedVitalRecord.notes && (
                <View style={styles.notesContainer}>
                  <Text style={styles.notesLabel}>Notes:</Text>
                  <Text style={styles.notesText}>{selectedVitalRecord.notes}</Text>
                </View>
              )}

              <View style={styles.metadataContainer}>
                <Text style={styles.metadataLabel}>Record Method:</Text>
                <Text style={styles.metadataValue}>{selectedVitalRecord.recordMethod || 'Manual'}</Text>

                <Text style={styles.metadataLabel}>Record Type:</Text>
                <Text style={styles.metadataValue}>{selectedVitalRecord.recordType || 'Self'}</Text>

                <Text style={styles.metadataLabel}>Record ID:</Text>
                <Text style={styles.metadataValue}>{selectedVitalRecord.id}</Text>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {patientName ? `${patientName}'s Vitals` : 'Patient Vitals'}
        </Text>
        <Text style={styles.headerSubtitle}>
          Monitor your patient's health metrics
        </Text>
      </View>

      {/* Vital Type Selector */}
      <View style={styles.selectorContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} nestedScrollEnabled={true}>
          <TouchableOpacity
            style={[styles.selectorButton, selectedVital === 'heartRate' && [styles.selectedButton, { borderColor: '#EA4335', backgroundColor: '#FFEBEE' }]]}
            onPress={() => handleVitalChange('heartRate')}
          >
            <Ionicons
              name="heart"
              size={20}
              color={selectedVital === 'heartRate' ? '#EA4335' : '#888'}
            />
            <Text style={[styles.selectorText, selectedVital === 'heartRate' && [styles.selectedText, { color: '#EA4335' }]]}>
              Heart Rate
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.selectorButton, selectedVital === 'bloodPressure' && [styles.selectedButton, { borderColor: '#4285F4', backgroundColor: '#E3F2FD' }]]}
            onPress={() => handleVitalChange('bloodPressure')}
          >
            <Ionicons
              name="fitness"
              size={20}
              color={selectedVital === 'bloodPressure' ? '#4285F4' : '#888'}
            />
            <Text style={[styles.selectorText, selectedVital === 'bloodPressure' && [styles.selectedText, { color: '#4285F4' }]]}>
              Blood Pressure
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.selectorButton, selectedVital === 'bloodGlucose' && [styles.selectedButton, { borderColor: '#FBBC05', backgroundColor: '#FFF8E1' }]]}
            onPress={() => handleVitalChange('bloodGlucose')}
          >
            <Ionicons
              name="water"
              size={20}
              color={selectedVital === 'bloodGlucose' ? '#FBBC05' : '#888'}
            />
            <Text style={[styles.selectorText, selectedVital === 'bloodGlucose' && [styles.selectedText, { color: '#FBBC05' }]]}>
              Blood Glucose
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.selectorButton, selectedVital === 'weight' && [styles.selectedButton, { borderColor: '#34A853', backgroundColor: '#E8F5E9' }]]}
            onPress={() => handleVitalChange('weight')}
          >
            <Ionicons
              name="body"
              size={20}
              color={selectedVital === 'weight' ? '#34A853' : '#888'}
            />
            <Text style={[styles.selectorText, selectedVital === 'weight' && [styles.selectedText, { color: '#34A853' }]]}>
              Weight
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Time Range Selector for Chart */}
      <View style={styles.timeRangeContainer}>
        <Text style={styles.timeRangeLabel}>Time Range:</Text>
        <View style={styles.timeRangeButtons}>
          <TouchableOpacity
            style={[styles.timeRangeButton, timeRange === 'week' && styles.selectedTimeRange]}
            onPress={() => handleTimeRangeChange('week')}
          >
            <Text style={[styles.timeRangeText, timeRange === 'week' && styles.selectedTimeRangeText]}>
              Week
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.timeRangeButton, timeRange === 'month' && styles.selectedTimeRange]}
            onPress={() => handleTimeRangeChange('month')}
          >
            <Text style={[styles.timeRangeText, timeRange === 'month' && styles.selectedTimeRangeText]}>
              Month
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading patient vitals...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.content}
          nestedScrollEnabled={true}
        >
          {/* Chart Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {getVitalTypeName({vitalType: selectedVital})} Trends
            </Text>
            {renderChartSection()}
          </View>

          {/* Records Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {getVitalTypeName({vitalType: selectedVital})} Records
            </Text>
            {renderVitalsList()}
          </View>
        </ScrollView>
      )}

      {renderDetailModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  selectorContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    padding: 10,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 10,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  selectedButton: {
    backgroundColor: '#f0f7ff',
    borderColor: '#666',
  },
  selectorText: {
    marginLeft: 6,
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  selectedText: {
    fontWeight: '600',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
  },
  timeRangeLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  timeRangeButtons: {
    flexDirection: 'row',
  },
  timeRangeButton: {
    paddingHorizontal: 14,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  selectedTimeRange: {
    backgroundColor: '#f0f7ff',
    borderColor: ROLE_COLORS.doctor.primary,
  },
  timeRangeText: {
    fontSize: 13,
    color: '#666',
    fontWeight: '500',
  },
  selectedTimeRangeText: {
    color: ROLE_COLORS.doctor.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  chartContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  chart: {
    borderRadius: 8,
    marginVertical: 8,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },

  vitalIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  vitalCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 14,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eaeaea',
    borderLeftWidth: 4,
  },
  vitalInfo: {
    flex: 1,
  },
  vitalType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  vitalValue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  vitalDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  emptyChartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyChartText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 6,
  },
  modalContent: {
    padding: 16,
  },
  modalVitalCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
  },
  notesContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
  },
  metadataContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
  },
  metadataLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  metadataValue: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
});

export default PatientVitalsViewer;
