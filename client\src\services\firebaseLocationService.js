import { auth, db } from '../config/firebase';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  addDoc,
  updateDoc,
  deleteDoc,
  orderBy,
  limit,
  serverTimestamp
} from 'firebase/firestore';
import * as Location from 'expo-location';

/**
 * Service for managing location data in Firebase
 */
export const firebaseLocationService = {
  /**
   * Request location permissions from the user
   * @returns {Promise<boolean>} Whether permission was granted
   */
  requestLocationPermissions: async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  },

  /**
   * Get the current location of the device
   * @returns {Promise<Location.LocationObject|null>} The current location or null if unavailable
   */
  getCurrentLocation: async () => {
    try {
      const hasPermission = await firebaseLocationService.requestLocationPermissions();
      if (!hasPermission) {
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      return location;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  },

  /**
   * Save a patient's location to Firebase
   * @param {Object} locationData - The location data to save
   * @returns {Promise<Object>} The saved location data with ID
   */
  savePatientLocation: async (locationData) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const locationToSave = {
        ...locationData,
        userId: currentUser.uid,
        createdAt: serverTimestamp(),
      };

      const locationsCollection = collection(db, 'patientLocations');
      const docRef = await addDoc(locationsCollection, locationToSave);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: docRef.id,
        ...newDocData,
      };

      // Ensure timestamps are strings if they're not already
      if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
        result.createdAt = newDocData.createdAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error saving location to Firebase:', error);
      throw new Error('Failed to save location');
    }
  },

  /**
   * Start sharing location with a supervisor
   * @param {string} supervisorId - The supervisor's user ID
   * @returns {Promise<Object>} The location sharing session
   */
  startLocationSharing: async (supervisorId) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get current location
      const location = await firebaseLocationService.getCurrentLocation();
      if (!location) {
        throw new Error('Could not get current location');
      }

      const sharingData = {
        patientId: currentUser.uid,
        supervisorId,
        status: 'active',
        startedAt: serverTimestamp(),
        lastLocation: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          altitude: location.coords.altitude,
          heading: location.coords.heading,
          speed: location.coords.speed,
          timestamp: new Date(location.timestamp).toISOString(),
        }
      };

      const sharingCollection = collection(db, 'locationSharing');
      const docRef = await addDoc(sharingCollection, sharingData);

      // Get the newly created document
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      return {
        id: docRef.id,
        ...newDocData,
        startedAt: newDocData.startedAt?.toDate?.().toISOString() || new Date().toISOString()
      };
    } catch (error) {
      console.error('Error starting location sharing:', error);
      throw new Error('Failed to start location sharing');
    }
  },

  /**
   * Stop sharing location with a supervisor
   * @param {string} sharingId - The location sharing session ID
   * @returns {Promise<boolean>} Whether the operation was successful
   */
  stopLocationSharing: async (sharingId) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const sharingRef = doc(db, 'locationSharing', sharingId);
      const sharingDoc = await getDoc(sharingRef);

      if (!sharingDoc.exists()) {
        throw new Error('Location sharing session not found');
      }

      const sharingData = sharingDoc.data();
      if (sharingData.patientId !== currentUser.uid) {
        throw new Error('Not authorized to stop this location sharing session');
      }

      await updateDoc(sharingRef, {
        status: 'stopped',
        stoppedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error stopping location sharing:', error);
      return false;
    }
  },

  /**
   * Get active location sharing sessions for a supervisor
   * @returns {Promise<Array>} Array of active location sharing sessions
   */
  getSupervisorActiveSessions: async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const sharingCollection = collection(db, 'locationSharing');
      const q = query(
        sharingCollection,
        where('supervisorId', '==', currentUser.uid),
        where('status', '==', 'active')
      );

      const querySnapshot = await getDocs(q);
      const sessions = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        sessions.push({
          id: doc.id,
          ...data,
          startedAt: data.startedAt?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      return sessions;
    } catch (error) {
      console.error('Error getting supervisor active sessions:', error);
      return [];
    }
  },

  /**
   * Get a patient's recent locations
   * @param {string} patientId - The patient's user ID
   * @param {number} limit - Maximum number of locations to retrieve
   * @returns {Promise<Array>} Array of location records
   */
  getPatientRecentLocations: async (patientId, maxResults = 10) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      const locationsCollection = collection(db, 'patientLocations');
      const q = query(
        locationsCollection,
        where('userId', '==', patientId),
        orderBy('createdAt', 'desc'),
        limit(maxResults)
      );

      const querySnapshot = await getDocs(q);
      const locations = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        locations.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      return locations;
    } catch (error) {
      console.error('Error getting patient locations:', error);
      return [];
    }
  }
};
