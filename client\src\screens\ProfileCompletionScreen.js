import React, { useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { showMessage } from 'react-native-flash-message';
import SharedProfileForm from '../components/profile/SharedProfileForm';
import { COLORS } from '../config/theme';

const ProfileCompletionScreen = () => {
  const { user, updateUserProfile } = useAuth();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);

  // Initial form data from user object
  const initialData = {
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    // Include other fields as needed
  };

  const handleSubmit = async (formData) => {
    try {
      setLoading(true);
      
      // Update the user profile
      await updateUserProfile({
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString(),
      });

      // Show success message
      showMessage({
        message: 'Profile Completed',
        description: 'Your profile has been completed successfully',
        type: 'success',
        backgroundColor: COLORS.success,
      });

      // Wait for message to be visible before navigating
      // Using a single navigation call with timeout
      setTimeout(() => {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Dashboard' }],
        });
      }, 1000);
      
      // REMOVED: Second navigation call that was causing the double navigation
    } catch (error) {
      console.error('Error completing profile:', error);
      showMessage({
        message: 'Error',
        description: error.message || 'Failed to complete profile. Please try again.',
        type: 'danger',
        backgroundColor: COLORS.error,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Complete Your Profile</Text>
      <Text style={styles.subheader}>
        Please provide the following information to complete your profile.
      </Text>
      
      <SharedProfileForm
        initialData={initialData}
        onSubmit={handleSubmit}
        loading={loading}
        buttonText="Complete Profile"
        requiredFields={['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender']}
        nonEditableFields={['firstName', 'lastName', 'email']} // Add this prop
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.textDark,
    marginTop: 20,
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  subheader: {
    fontSize: 16,
    color: COLORS.textMedium,
    marginBottom: 20,
    paddingHorizontal: 20,
  },
});

export default ProfileCompletionScreen;