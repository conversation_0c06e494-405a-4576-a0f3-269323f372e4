import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView, TouchableOpacity, ActivityIndicator, Modal } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../../../contexts/AuthContext';
import { useVitals } from '../../../../contexts/VitalsContext';

const screenWidth = Dimensions.get('window').width;

const HealthRecords = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { fetchVitals, vitals: firebaseVitals, loading: vitalsLoading } = useVitals();
  const [vitalsData, setVitalsData] = useState([]);
  const [error, setError] = useState(null);
  const [selectedVital, setSelectedVital] = useState('bloodPressure');
  const [timeRange, setTimeRange] = useState('month');
  const [displayMode, setDisplayMode] = useState('chart'); // 'chart' or 'table'

  const [vitalTypeFilter, setVitalTypeFilter] = useState('all'); // Filter for recorded vitals
  const [modalVisible, setModalVisible] = useState(false); // For the modal view of all vitals

  // Effet pour le loading state supprimé

  // Function to fetch vitals from Firebase
  useEffect(() => {
    const fetchAllVitals = async () => {
      try {
        // Fetch ALL vitals from Firebase (sans filtre par type)
        if (user) {
          // Passer null pour récupérer tous les types de données vitales
          await fetchVitals(null);
        }
      } catch (error) {
        console.error('Error fetching vitals:', error);
      }
    };

    fetchAllVitals();
  }, [timeRange, user]); // Removed selectedVital dependency since we fetch all types

  // Process Firebase data
  useEffect(() => {
    if (!firebaseVitals) return;

    try {
      // Transform data to the appropriate format
      const transformedData = [];

      for (const record of firebaseVitals) {
        // Vérifier que l'enregistrement a un timestamp valide
        if (!record.timestamp) {
          continue;
        }

        const date = new Date(record.timestamp);
        let formattedDate;

        if (timeRange === 'year') {
          formattedDate = date.toLocaleString('default', { month: 'short' });
        } else {
          formattedDate = `${date.getMonth() + 1}/${date.getDate()}`;
        }

        // Create a record with the appropriate structure
        const result = {
          date: formattedDate,
          timestamp: record.timestamp,
          recordId: record.id
        };

        // Add the appropriate vital sign data based on type
        if (record.vitalType === 'bloodPressure' && record.values) {
          result.bloodPressure = {
            systolic: parseInt(record.values.systolic) || 0,
            diastolic: parseInt(record.values.diastolic) || 0
          };
        } else if (record.vitalType === 'heartRate' && record.values) {
          result.heartRate = parseInt(record.values.value) || 0;
        } else if (record.vitalType === 'bloodGlucose' && record.values) {
          result.bloodGlucose = parseInt(record.values.value) || 0;
        } else if (record.vitalType === 'weight' && record.values) {
          result.weight = parseInt(record.values.value) || 0;
        } else {
          continue; // Skip this record
        }

        transformedData.push(result);
      }

      // Set the processed data
      setVitalsData(transformedData);
    } catch (error) {
      console.error('Error processing vitals data:', error);
    }
  }, [firebaseVitals, timeRange]);

  // Mock data generation removed

  // Get vitals data for charts
  const getRecordedVitalsData = () => {
    // Use all vitals that match the selected vital type
    const filteredVitals = vitalsData.filter(item => {
      // Filter vitals by type
      if (selectedVital === 'bloodPressure' && item.bloodPressure) return true;
      if (selectedVital === 'heartRate' && item.heartRate) return true;
      if (selectedVital === 'bloodGlucose' && item.bloodGlucose) return true;
      if (selectedVital === 'weight' && item.weight) return true;
      return false;
    });

    // Sort by date (oldest to newest for charts)
    filteredVitals.sort((a, b) => {
      if (a.timestamp && b.timestamp) {
        return new Date(a.timestamp) - new Date(b.timestamp);
      }
      return 0;
    });

    return filteredVitals;
  };

  // Get data for charts (same as recorded vitals section)
  const getCombinedData = () => {
    // Use all vitals that match the selected vital type
    const filteredVitals = vitalsData.filter(item => {
      if (selectedVital === 'bloodPressure' && item.bloodPressure) return true;
      if (selectedVital === 'heartRate' && item.heartRate) return true;
      if (selectedVital === 'bloodGlucose' && item.bloodGlucose) return true;
      if (selectedVital === 'weight' && item.weight) return true;
      return false;
    });

    // Sort by date (oldest to newest for charts)
    filteredVitals.sort((a, b) => {
      if (a.timestamp && b.timestamp) {
        return new Date(a.timestamp) - new Date(b.timestamp);
      }
      return 0;
    });

    return filteredVitals;
  };

  // Prepare chart data based on selected vital
  const prepareChartData = () => {
    const recordedData = getRecordedVitalsData();
    if (!recordedData || recordedData.length === 0) return null;

    const labels = recordedData.map(item => item.date);

    if (selectedVital === 'bloodPressure') {
      return {
        labels,
        datasets: [
          {
            data: recordedData.map(item => item.bloodPressure?.systolic || 0),
            color: (opacity = 1) => `rgba(136, 132, 216, ${opacity})`,
            strokeWidth: 2,
            label: 'Systolic'
          },
          {
            data: recordedData.map(item => item.bloodPressure?.diastolic || 0),
            color: (opacity = 1) => `rgba(130, 202, 157, ${opacity})`,
            strokeWidth: 2,
            label: 'Diastolic'
          }
        ],
        legend: ['Systolic', 'Diastolic']
      };
    } else {
      return {
        labels,
        datasets: [{
          data: recordedData.map(item => {
            switch(selectedVital) {
              case 'heartRate': return item.heartRate || 0;
              case 'bloodGlucose': return item.bloodGlucose || 0;
              case 'weight': return item.weight || 0;
              default: return 0;
            }
          }),
          color: (opacity = 1) => {
            switch(selectedVital) {
              case 'heartRate': return `rgba(255, 115, 0, ${opacity})`;
              case 'bloodGlucose': return `rgba(0, 136, 254, ${opacity})`;
              case 'weight': return `rgba(0, 196, 159, ${opacity})`;
              default: return `rgba(0, 0, 0, ${opacity})`;
            }
          },
          strokeWidth: 2
        }],
        legend: [getVitalTitle()]
      };
    }
  };

  const getVitalTitle = () => {
    switch(selectedVital) {
      case 'bloodPressure': return 'Blood Pressure';
      case 'heartRate': return 'Heart Rate';
      case 'bloodGlucose': return 'Blood Glucose';
      case 'weight': return 'Weight';
      default: return '';
    }
  };

  const getYAxisSuffix = () => {
    switch(selectedVital) {
      case 'bloodPressure': return ' mmHg';
      case 'heartRate': return ' bpm';
      case 'bloodGlucose': return ' mg/dL';
      case 'weight': return ' kg';
      default: return '';
    }
  };



  // Navigate to record vitals screen
  const navigateToRecordVitals = () => {
    navigation.navigate('RecordVitals');
  };

  // Render data table view
  const renderDataTable = () => {
    const combinedData = getCombinedData();
    if (!combinedData || combinedData.length === 0) return null;

    // Determine columns based on vital sign type
    const getTableHeaders = () => {
      if (selectedVital === 'bloodPressure') {
        return ['Date', 'Systolic (mmHg)', 'Diastolic (mmHg)'];
      } else {
        return ['Date', `${getVitalTitle()} ${getYAxisSuffix().trim()}`];
      }
    };

    // Get table headers
    const headers = getTableHeaders();

    return (
      <View style={styles.tableContainer}>
        {/* Table Headers */}
        <View style={styles.tableRow}>
          {headers.map((header, index) => (
            <View key={index} style={[
              styles.tableHeaderCell,
              index === 0 ? styles.dateCell : styles.valueCell
            ]}>
              <Text style={styles.tableHeaderText}>{header}</Text>
            </View>
          ))}
        </View>

        {/* Table Data Rows - Fixed Scrolling */}
        <View style={styles.tableBodyContainer}>
          <ScrollView
            nestedScrollEnabled={true}
            contentContainerStyle={styles.tableBodyContent}
          >
            {getCombinedData().map((item, rowIndex) => (
              <View key={rowIndex} style={[
                styles.tableRow,
                rowIndex % 2 === 0 ? styles.evenRow : null,
                styles.recordedRow
              ]}>
                {/* Date Cell */}
                <View style={[styles.tableCell, styles.dateCell]}>
                  <Text style={[styles.tableCellText, styles.recordedText]}>
                    {item.date} 📱
                  </Text>
                </View>

                {/* Value Cells (based on vital sign type) */}
                {selectedVital === 'bloodPressure' ? (
                  <>
                    <View style={[styles.tableCell, styles.valueCell]}>
                      <Text style={[styles.tableCellText, styles.recordedText]}>
                        {item.bloodPressure?.systolic || '-'}
                      </Text>
                    </View>
                    <View style={[styles.tableCell, styles.valueCell]}>
                      <Text style={[styles.tableCellText, styles.recordedText]}>
                        {item.bloodPressure?.diastolic || '-'}
                      </Text>
                    </View>
                  </>
                ) : (
                  <View style={[styles.tableCell, styles.valueCell]}>
                    <Text style={[styles.tableCellText, styles.recordedText]}>
                      {selectedVital === 'heartRate' ? (item.heartRate || '-') :
                       selectedVital === 'bloodGlucose' ? (item.bloodGlucose || '-') :
                       selectedVital === 'weight' ? (item.weight || '-') : '-'}
                    </Text>
                  </View>
                )}
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    );
  };

  const chartData = prepareChartData();

  // Change vital selection
  const handleVitalChange = (vital) => {
    setSelectedVital(vital);
  };

  // Change time range
  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  // Change display mode
  const handleDisplayModeChange = (mode) => {
    setDisplayMode(mode);
  };

  // Render a section showing all recorded vitals
  const renderRecordedVitalsSection = () => {
    // Get all vitals
    const recordedVitals = vitalsData;

    if (recordedVitals.length === 0) {
      return (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Your Recorded Vitals</Text>
          <View style={styles.emptyRecordsContainer}>
            <Ionicons name="pulse" size={48} color="#4285F4" />
            <Text style={styles.emptyRecordsText}>No vital signs recorded yet</Text>
            <TouchableOpacity
              style={styles.recordVitalsButton}
              onPress={navigateToRecordVitals}
            >
              <Ionicons name="mic-outline" size={18} color="#fff" />
              <Text style={styles.recordVitalsButtonText}>Record Your First Vital</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // Use the vitalTypeFilter state from the component

    // Filter vitals by selected type
    const filteredVitals = vitalTypeFilter === 'all'
      ? recordedVitals
      : recordedVitals.filter(vital => {
          switch(vitalTypeFilter) {
            case 'bloodPressure': return !!vital.bloodPressure;
            case 'heartRate': return !!vital.heartRate;
            case 'bloodGlucose': return !!vital.bloodGlucose;
            case 'weight': return !!vital.weight;
            default: return true;
          }
        });

    // Sort vitals by timestamp (newest first)
    filteredVitals.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Format date for display
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString();
    };

    // Get vital value based on type
    const getVitalValue = (vital) => {
      if (vital.bloodPressure) {
        return `${vital.bloodPressure.systolic}/${vital.bloodPressure.diastolic} mmHg`;
      } else if (vital.heartRate) {
        return `${vital.heartRate} bpm`;
      } else if (vital.bloodGlucose) {
        return `${vital.bloodGlucose} mg/dL`;
      } else if (vital.weight) {
        return `${vital.weight} kg`;
      }
      return 'N/A';
    };

    // Get vital type name
    const getVitalTypeName = (vital) => {
      if (vital.bloodPressure) return 'Blood Pressure';
      if (vital.heartRate) return 'Heart Rate';
      if (vital.bloodGlucose) return 'Blood Glucose';
      if (vital.weight) return 'Weight';
      return 'Unknown';
    };

    // Get icon and color for vital type
    const getVitalTypeIcon = (vital) => {
      if (vital.bloodPressure) {
        return { name: 'fitness', color: '#4285F4' };
      } else if (vital.heartRate) {
        return { name: 'heart', color: '#E91E63' };
      } else if (vital.bloodGlucose) {
        return { name: 'water', color: '#FBBC05' };
      } else if (vital.weight) {
        return { name: 'body', color: '#34A853' };
      }
      return { name: 'medical', color: '#9E9E9E' };
    };

    return (
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Your Recorded Vitals</Text>
          <TouchableOpacity
            style={styles.recordButton}
            onPress={navigateToRecordVitals}
          >
            <Ionicons name="mic-outline" size={18} color="#fff" />
            <Text style={styles.recordButtonText}>Record New</Text>
          </TouchableOpacity>
        </View>

        {/* Filter buttons */}
        <View style={styles.filterContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.filterButton, vitalTypeFilter === 'all' && styles.filterButtonActive]}
              onPress={() => setVitalTypeFilter('all')}
            >
              <Text style={[styles.filterButtonText, vitalTypeFilter === 'all' && styles.filterButtonTextActive]}>All</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, vitalTypeFilter === 'bloodPressure' && styles.filterButtonActive]}
              onPress={() => setVitalTypeFilter('bloodPressure')}
            >
              <Ionicons name="fitness" size={16} color={vitalTypeFilter === 'bloodPressure' ? '#fff' : '#4285F4'} />
              <Text style={[styles.filterButtonText, vitalTypeFilter === 'bloodPressure' && styles.filterButtonTextActive]}>Blood Pressure</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, vitalTypeFilter === 'heartRate' && styles.filterButtonActive]}
              onPress={() => setVitalTypeFilter('heartRate')}
            >
              <Ionicons name="heart" size={16} color={vitalTypeFilter === 'heartRate' ? '#fff' : '#E91E63'} />
              <Text style={[styles.filterButtonText, vitalTypeFilter === 'heartRate' && styles.filterButtonTextActive]}>Heart Rate</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, vitalTypeFilter === 'bloodGlucose' && styles.filterButtonActive]}
              onPress={() => setVitalTypeFilter('bloodGlucose')}
            >
              <Ionicons name="water" size={16} color={vitalTypeFilter === 'bloodGlucose' ? '#fff' : '#FBBC05'} />
              <Text style={[styles.filterButtonText, vitalTypeFilter === 'bloodGlucose' && styles.filterButtonTextActive]}>Blood Glucose</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, vitalTypeFilter === 'weight' && styles.filterButtonActive]}
              onPress={() => setVitalTypeFilter('weight')}
            >
              <Ionicons name="body" size={16} color={vitalTypeFilter === 'weight' ? '#fff' : '#34A853'} />
              <Text style={[styles.filterButtonText, vitalTypeFilter === 'weight' && styles.filterButtonTextActive]}>Weight</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* List of first 4 vitals */}
        <View style={styles.allVitalsContainer}>
          {filteredVitals.length === 0 ? (
            <Text style={styles.emptyFilterText}>No records found for the selected filter</Text>
          ) : (
            <>
              {filteredVitals.slice(0, 4).map((vital, index) => {
                const { name: iconName, color: iconColor } = getVitalTypeIcon(vital);

                return (
                  <View key={index} style={styles.vitalCard}>
                    <View style={[styles.vitalIconContainer, { backgroundColor: iconColor }]}>
                      <Ionicons name={iconName} size={24} color="#fff" />
                    </View>
                    <View style={styles.vitalInfo}>
                      <Text style={styles.vitalType}>
                        {getVitalTypeName(vital)}
                      </Text>
                      <Text style={styles.vitalValue}>{getVitalValue(vital)}</Text>
                      <Text style={styles.vitalDate}>{formatDate(vital.timestamp)}</Text>
                    </View>
                  </View>
                );
              })}

              {filteredVitals.length > 4 && (
                <TouchableOpacity
                  style={styles.seeMoreButton}
                  onPress={() => setModalVisible(true)}
                >
                  <Text style={styles.seeMoreButtonText}>See More ({filteredVitals.length - 4} more)</Text>
                  <Ionicons name="chevron-forward" size={16} color="#4285F4" />
                </TouchableOpacity>
              )}
            </>
          )}
        </View>

        {/* Modal for showing all vitals */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Vitals History (Last 30 Days)</Text>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setModalVisible(false)}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              {/* Modal Filter Buttons */}
              <View style={styles.modalFilterContainer}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <TouchableOpacity
                    style={[styles.modalFilterButton, vitalTypeFilter === 'all' && styles.modalFilterButtonActive]}
                    onPress={() => setVitalTypeFilter('all')}
                  >
                    <Text style={[styles.modalFilterButtonText, vitalTypeFilter === 'all' && styles.modalFilterButtonTextActive]}>All</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalFilterButton, vitalTypeFilter === 'bloodPressure' && styles.modalFilterButtonActive]}
                    onPress={() => setVitalTypeFilter('bloodPressure')}
                  >
                    <Ionicons name="fitness" size={16} color={vitalTypeFilter === 'bloodPressure' ? '#fff' : '#4285F4'} />
                    <Text style={[styles.modalFilterButtonText, vitalTypeFilter === 'bloodPressure' && styles.modalFilterButtonTextActive]}>Blood Pressure</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalFilterButton, vitalTypeFilter === 'heartRate' && styles.modalFilterButtonActive]}
                    onPress={() => setVitalTypeFilter('heartRate')}
                  >
                    <Ionicons name="heart" size={16} color={vitalTypeFilter === 'heartRate' ? '#fff' : '#E91E63'} />
                    <Text style={[styles.modalFilterButtonText, vitalTypeFilter === 'heartRate' && styles.modalFilterButtonTextActive]}>Heart Rate</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalFilterButton, vitalTypeFilter === 'bloodGlucose' && styles.modalFilterButtonActive]}
                    onPress={() => setVitalTypeFilter('bloodGlucose')}
                  >
                    <Ionicons name="water" size={16} color={vitalTypeFilter === 'bloodGlucose' ? '#fff' : '#FBBC05'} />
                    <Text style={[styles.modalFilterButtonText, vitalTypeFilter === 'bloodGlucose' && styles.modalFilterButtonTextActive]}>Blood Glucose</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalFilterButton, vitalTypeFilter === 'weight' && styles.modalFilterButtonActive]}
                    onPress={() => setVitalTypeFilter('weight')}
                  >
                    <Ionicons name="body" size={16} color={vitalTypeFilter === 'weight' ? '#fff' : '#34A853'} />
                    <Text style={[styles.modalFilterButtonText, vitalTypeFilter === 'weight' && styles.modalFilterButtonTextActive]}>Weight</Text>
                  </TouchableOpacity>
                </ScrollView>
              </View>

              <ScrollView style={styles.modalScrollView}>
                {filteredVitals
                  .filter(vital => {
                    // Filter for last 30 days
                    const vitalDate = new Date(vital.timestamp);
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                    return vitalDate >= thirtyDaysAgo;
                  })
                  // Apply type filter
                  .filter(vital => {
                    if (vitalTypeFilter === 'all') return true;
                    if (vitalTypeFilter === 'bloodPressure' && vital.bloodPressure) return true;
                    if (vitalTypeFilter === 'heartRate' && vital.heartRate) return true;
                    if (vitalTypeFilter === 'bloodGlucose' && vital.bloodGlucose) return true;
                    if (vitalTypeFilter === 'weight' && vital.weight) return true;
                    return false;
                  })
                  // Sort by date (newest first)
                  .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                  // Limit to 30 records
                  .slice(0, 30)
                  .map((vital, index) => {
                    const { name: iconName, color: iconColor } = getVitalTypeIcon(vital);

                    return (
                      <View key={index} style={styles.modalVitalCard}>
                        <View style={[styles.vitalIconContainer, { backgroundColor: iconColor }]}>
                          <Ionicons name={iconName} size={24} color="#fff" />
                        </View>
                        <View style={styles.vitalInfo}>
                          <Text style={styles.vitalType}>
                            {getVitalTypeName(vital)}
                          </Text>
                          <Text style={styles.vitalValue}>{getVitalValue(vital)}</Text>
                          <Text style={styles.vitalDate}>{formatDate(vital.timestamp)}</Text>
                        </View>
                      </View>
                    );
                  })
                }
              </ScrollView>
            </View>
          </View>
        </Modal>
      </View>
    );
  };

  // Afficher un loader pendant le chargement des données
  if (vitalsLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#4285F4" />
        <Text style={styles.loaderText}>Loading health records...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.header}>Health Records</Text>
      </View>

      {/* Recorded Vitals Section */}
      {renderRecordedVitalsSection()}

      {/* Trends Section */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Vital Signs Trends</Text>

        <View style={styles.selectorsContainer}>
          <View style={styles.selectorWrapper}>
            <Text style={styles.label}>Vital Sign:</Text>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, selectedVital === 'bloodPressure' && styles.selectedButton]}
                onPress={() => handleVitalChange('bloodPressure')}>
                <Text style={[styles.buttonText, selectedVital === 'bloodPressure' && styles.selectedButtonText]}>Blood Pressure</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, selectedVital === 'heartRate' && styles.selectedButton]}
                onPress={() => handleVitalChange('heartRate')}>
                <Text style={[styles.buttonText, selectedVital === 'heartRate' && styles.selectedButtonText]}>Heart Rate</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, selectedVital === 'bloodGlucose' && styles.selectedButton]}
                onPress={() => handleVitalChange('bloodGlucose')}>
                <Text style={[styles.buttonText, selectedVital === 'bloodGlucose' && styles.selectedButtonText]}>Blood Glucose</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, selectedVital === 'weight' && styles.selectedButton]}
                onPress={() => handleVitalChange('weight')}>
                <Text style={[styles.buttonText, selectedVital === 'weight' && styles.selectedButtonText]}>Weight</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.selectorWrapper}>
            <Text style={styles.label}>Time Period:</Text>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, timeRange === 'week' && styles.selectedButton]}
                onPress={() => handleTimeRangeChange('week')}>
                <Text style={[styles.buttonText, timeRange === 'week' && styles.selectedButtonText]}>Last Week</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, timeRange === 'month' && styles.selectedButton]}
                onPress={() => handleTimeRangeChange('month')}>
                <Text style={[styles.buttonText, timeRange === 'month' && styles.selectedButtonText]}>Last Month</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, timeRange === 'year' && styles.selectedButton]}
                onPress={() => handleTimeRangeChange('year')}>
                <Text style={[styles.buttonText, timeRange === 'year' && styles.selectedButtonText]}>Last Year</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.selectorWrapper}>
            <Text style={styles.label}>Display Mode:</Text>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, displayMode === 'chart' && styles.selectedButton]}
                onPress={() => handleDisplayModeChange('chart')}>
                <Text style={[styles.buttonText, displayMode === 'chart' && styles.selectedButtonText]}>Chart</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, displayMode === 'table' && styles.selectedButton]}
                onPress={() => handleDisplayModeChange('table')}>
                <Text style={[styles.buttonText, displayMode === 'table' && styles.selectedButtonText]}>Table</Text>
              </TouchableOpacity>
            </View>
          </View>


        </View>

        {vitalsLoading ? (
          <Text style={styles.message}>Loading data...</Text>
        ) : error ? (
          <Text style={styles.errorMessage}>{error}</Text>
        ) : chartData ? (
          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>
              {getVitalTitle()} - {
                timeRange === 'week' ? 'Last Week' :
                timeRange === 'month' ? 'Last Month' :
                'Last Year'
              }
            </Text>

            {displayMode === 'chart' ? (
              <View>
                <LineChart
                  data={chartData}
                  width={screenWidth - 40}
                  height={220}
                  chartConfig={{
                    backgroundColor: '#ffffff',
                    backgroundGradientFrom: '#ffffff',
                    backgroundGradientTo: '#ffffff',
                    decimalPlaces: 0,
                    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                    style: {
                      borderRadius: 16
                    },
                    propsForDots: {
                      r: "4",
                      strokeWidth: "2",
                    }
                  }}
                  bezier
                  style={styles.chart}
                  yAxisSuffix={getYAxisSuffix()}
                  fromZero={selectedVital === 'weight'}
                  withInnerLines={true}
                  withOuterLines={true}
                  withVerticalLines={true}
                  withHorizontalLines={true}
                  withDots={true}
                  withShadow={false}
                  segments={5}
                />
                <View style={styles.legendContainer}>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendDot, { backgroundColor: '#4285F4' }]} />
                    <Text style={styles.legendText}>Recorded Data</Text>
                  </View>
                </View>
              </View>
            ) : (
              renderDataTable()
            )}
          </View>
        ) : (
          <Text style={styles.message}>No data available</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4285F4',
  },
  headerContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  card: {
    margin: 16,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  selectorsContainer: {
    marginBottom: 16,
  },
  selectorWrapper: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  buttonRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  button: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedButton: {
    backgroundColor: 'rgba(16, 107, 0, 1)',
  },
  buttonText: {
    color: '#333',
  },
  selectedButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  chartContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 8,
    marginVertical: 8,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 5,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    margin: 20,
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    margin: 20,
    color: 'red',
  },
  // Table styles with scrolling fixes
  tableContainer: {
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    backgroundColor: '#fff',
    width: screenWidth - 40,
    maxHeight: 400,
  },
  tableBodyContainer: {
    height: 300,
  },
  tableBodyContent: {
    flexGrow: 1,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  tableHeaderCell: {
    padding: 10,
    backgroundColor: 'rgba(16, 107, 0, 0.1)',
  },
  tableCell: {
    padding: 10,
  },
  dateCell: {
    flex: 2,
    justifyContent: 'flex-start',
  },
  valueCell: {
    flex: 1.5,
    justifyContent: 'center',
  },
  tableHeaderText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  tableCellText: {
    fontSize: 14,
  },
  evenRow: {
    backgroundColor: '#f9f9f9',
  },
  recordedRow: {
    borderLeftWidth: 3,
    borderLeftColor: '#4285F4',
  },
  recordedText: {
    fontWeight: '500',
    color: '#4285F4',
  },
  recordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4285F4',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  recordButtonText: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 14,
    fontWeight: '500',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  recentVitalsContainer: {
    marginTop: 10,
  },
  vitalCard: {
    flexDirection: 'row',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderLeftWidth: 3,
    borderLeftColor: '#4285F4',
  },
  vitalIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  vitalInfo: {
    flex: 1,
  },
  vitalType: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  vitalValue: {
    fontSize: 18,
    fontWeight: '500',
    color: '#4285F4',
    marginVertical: 2,
  },
  vitalDate: {
    fontSize: 12,
    color: '#757575',
  },
  emptyRecordsContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyRecordsText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 10,
    marginBottom: 20,
  },
  recordVitalsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4285F4',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  recordVitalsButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  filterContainer: {
    marginTop: 10,
    marginBottom: 15,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#4285F4',
  },
  filterButtonText: {
    fontSize: 14,
    marginLeft: 4,
  },
  filterButtonTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
  allVitalsContainer: {
    marginTop: 5,
  },
  emptyFilterText: {
    textAlign: 'center',
    color: '#757575',
    padding: 20,
    fontStyle: 'italic',
  },
  seeMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    marginTop: 5,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  seeMoreButtonText: {
    color: '#4285F4',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingBottom: 30,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  modalScrollView: {
    marginTop: 10,
    maxHeight: '90%',
  },
  modalVitalCard: {
    flexDirection: 'row',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderLeftWidth: 3,
    borderLeftColor: '#4285F4',
  },
  // Removed simulated styles
  modalFilterContainer: {
    paddingVertical: 10,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  modalFilterButtonActive: {
    backgroundColor: '#4285F4',
  },
  modalFilterButtonText: {
    fontSize: 14,
    marginLeft: 4,
  },
  modalFilterButtonTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
});

export default HealthRecords;