import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  ScrollView,
  ImageBackground,
  ActivityIndicator,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import DashboardChart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import { useAuth } from '../../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import AddPatientButton from './AddPatientButton';
import PatientVitalsCharts from './PatientVitalsCharts';
import { showMessage } from 'react-native-flash-message';
import { LinearGradient } from 'expo-linear-gradient';
import { ROLE_COLORS, COLORS } from '../../../config/theme';
import PatientProgressNotes from './PatientProgressNotes';
import { firebaseAppointmentsService } from '../../../services/firebaseAppointmentsService';
import { format } from 'date-fns';


const DoctorDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const doctorColors = ROLE_COLORS.doctor;

  // State
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    todayAppointments: '0',
    pendingRequests: '0',
    totalPatients: '0',
    completedToday: '0'
  });
  const [selectedPatient] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [requests, setRequests] = useState([]);
  const [expandedSections, setExpandedSections] = useState({
    overview: true,
    appointments: true,
    requests: true,
    vitals: true,
    notes: true,
    quickActions: true
  });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const headerAnim = useRef(new Animated.Value(0)).current;
  const cardsAnim = useRef(new Animated.Value(0)).current;
  const chartsAnim = useRef(new Animated.Value(0)).current;
  const listsAnim = useRef(new Animated.Value(0)).current;
  const actionsAnim = useRef(new Animated.Value(0)).current;

  // Empty weekly stats structure that will be populated with real data
  const [weeklyStats, setWeeklyStats] = useState({
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => `rgba(170, 86, 255, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Appointments']
  });

  // Empty diagnosis data structure that will be populated with real data
  const [diagnosisData] = useState({
    data: []
  });

  const menuItems = [
    { label: 'Dashboard', icon: 'home', screen: 'DoctorDashboard' },
    { label: 'Appointment', icon: 'calendar', screen: 'DoctorAppointments' },
    { label: 'Patients', icon: 'people', screen: 'Patients' },
    { label: 'Patient Health', icon: 'pulse', screen: 'PatientHealthMonitoring' },
    { label: 'Progress Notes', icon: 'document', screen: 'PatientProgressNotes' },
    { label: 'Messages', icon: 'chatbubble', screen: 'Messages' },
    { label: 'Prescriptions', icon: 'medical', screen: 'Prescriptions' },
    { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
    { label: 'Profile', icon: 'person', screen: 'Profile' },
    { label: 'Settings', icon: 'settings', screen: 'Settings' }
  ];

  // Fetch appointments and other dashboard data from Firebase
  const fetchAppointments = async () => {
    if (!refreshing) {
      setLoading(true); // Set loading to true when fetching data (unless refreshing)
    }

    try {
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      const formattedDate = format(today, 'yyyy-MM-dd');

      // Fetch all appointments first, then filter for today
      const options = {
        includePatientDetails: true,
        dateOrder: 'asc',  // Earliest appointments first
        timeOrder: 'asc'   // Earlier times first
      };

      // Get all appointments and filter for today in JavaScript
      const allAppointments = await firebaseAppointmentsService.getDoctorAppointments(options);
      const todayAppointments = allAppointments.filter(appointment =>
        appointment.date === formattedDate
      );

      // Get completed appointments for today
      const completedToday = allAppointments.filter(appointment =>
        appointment.date === formattedDate &&
        appointment.status?.toLowerCase() === 'completed'
      );

      console.log(`Fetched ${allAppointments.length} total appointments, ${todayAppointments.length} for today`);

      // Format appointments for display in UpcomingList
      const formattedAppointments = todayAppointments.map(appointment => {
        // Format the time for display
        const appointmentTime = appointment.time;
        const timeDisplay = appointmentTime ?
          `Today, ${appointmentTime}` :
          'Time not specified';

        // Get patient name - try different sources
        let patientName = 'Unknown Patient';
        if (appointment.patient && appointment.patient.name) {
          patientName = appointment.patient.name;
        } else if (appointment.patient && appointment.patient.displayName) {
          patientName = appointment.patient.displayName;
        } else if (appointment.patientName) {
          patientName = appointment.patientName;
        }

        return {
          id: appointment.id,
          title: patientName,
          description: appointment.reason || 'Medical Appointment',
          time: timeDisplay,
          type: 'appointment',
          status: appointment.status?.toLowerCase() || 'pending',
          appointmentDate: appointment.date,
          appointmentTime: appointment.time,
          patientId: appointment.patientId
        };
      });

      // Update state with formatted appointments
      setAppointments(formattedAppointments);

      // Get pending requests (appointments with status 'pending')
      const pendingRequests = allAppointments.filter(app =>
        app.status?.toLowerCase() === 'pending'
      );

      // Format pending requests for display
      const formattedRequests = pendingRequests.map(request => {
        // Get patient name
        let patientName = 'Unknown Patient';
        if (request.patient && request.patient.name) {
          patientName = request.patient.name;
        } else if (request.patient && request.patient.displayName) {
          patientName = request.patient.displayName;
        } else if (request.patientName) {
          patientName = request.patientName;
        }

        return {
          id: request.id,
          title: 'Appointment Request',
          description: `${patientName} - ${request.reason || 'Medical Appointment'}`,
          time: `Requested for ${request.date}`,
          type: 'appointment',
          status: 'pending'
        };
      });

      // Update requests state
      setRequests(formattedRequests);

      // Calculate weekly stats (appointments per day for the last 7 days)
      const last7Days = Array(7).fill(0).map((_, i) => {
        const d = new Date();
        d.setDate(d.getDate() - i);
        return format(d, 'yyyy-MM-dd');
      }).reverse();

      const weeklyAppointmentCounts = last7Days.map(date => {
        return allAppointments.filter(app => app.date === date).length;
      });

      // Update weekly stats
      setWeeklyStats({
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
          {
            data: weeklyAppointmentCounts,
            color: (opacity = 1) => `rgba(170, 86, 255, ${opacity})`,
            strokeWidth: 2
          }
        ],
        legend: ['Appointments']
      });

      // Get unique patients count
      const uniquePatientIds = new Set();
      allAppointments.forEach(app => {
        if (app.patientId) {
          uniquePatientIds.add(app.patientId);
        }
      });

      // Update all stats
      setStats({
        todayAppointments: todayAppointments.length.toString(),
        pendingRequests: pendingRequests.length.toString(),
        totalPatients: uniquePatientIds.size.toString(),
        completedToday: completedToday.length.toString()
      });

    } catch (error) {
      console.error('Error fetching appointments:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load appointments',
        type: 'danger'
      });
    } finally {
      setLoading(false); // Set loading to false when done, regardless of success or failure
    }
  };

  // Load appointments when component mounts
  useEffect(() => {
    fetchAppointments();
  }, []);

  // Animation sequence when data is loaded
  useEffect(() => {
    if (!loading) {
      // Start animations in sequence
      Animated.sequence([
        // Fade in the entire view
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        // Animate header
        Animated.timing(headerAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        // Animate cards with staggered effect
        Animated.stagger(100, [
          Animated.timing(cardsAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(chartsAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(listsAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(actionsAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
      ]).start();

    }
  }, [loading]);

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Reset animation values
    fadeAnim.setValue(0);
    headerAnim.setValue(0);
    cardsAnim.setValue(0);
    chartsAnim.setValue(0);
    listsAnim.setValue(0);
    actionsAnim.setValue(0);

    // Fetch real data
    await fetchAppointments();
    setRefreshing(false);
  };



  // Section header component with toggle functionality
  const SectionHeader = ({ title, section, icon }) => (
    <TouchableOpacity
      style={styles.sectionHeader}
      onPress={() => toggleSection(section)}
      activeOpacity={0.7}
    >
      <View style={styles.sectionHeaderContent}>
        <Ionicons name={icon} size={22} color={doctorColors.primary} style={styles.sectionIcon} />
        <Text style={styles.sectionTitle}>{title}</Text>
      </View>
      <Ionicons
        name={expandedSections[section] ? 'chevron-up' : 'chevron-down'}
        size={22}
        color={COLORS.textMedium}
      />
    </TouchableOpacity>
  );

  return (
    <DashboardLayout
      title="Doctor Dashboard"
      roleName="Doctor"
      menuItems={menuItems}
      userRole="doctor"
      notifications={notifications}
    >
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}></Text>
        </View>
      ) : (
        <Animated.ScrollView
          style={[styles.scrollView, { opacity: fadeAnim }]}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[doctorColors.primary]} />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Enhanced Header Section with Animation */}
          <Animated.View
            style={[
              styles.headerSection,
              {
                transform: [
                  {
                    translateY: headerAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-50, 0]
                    })
                  }
                ]
              }
            ]}
          >
            <LinearGradient
              colors={[doctorColors.primary, doctorColors.primaryLight, '#f5f7fa']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerGradient}
            >
              <View style={styles.greeting}>
                <View>
                  <Text style={styles.greetingText}>
                    Hello, Dr. {user?.lastName || 'Doctor'}
                  </Text>
                  <Text style={styles.dateText}>
                    {new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </Text>
                </View>
                <View style={styles.headerActions}>
                  <AddPatientButton />
                </View>
              </View>
            </LinearGradient>
          </Animated.View>

          <View style={styles.contentSection}>
            {/* Today's Overview Section */}
            <SectionHeader title="Today's Overview" section="overview" icon="stats-chart" />

            {expandedSections.overview && (
              <View style={styles.sectionContent}>
                <View style={styles.cardsContainer}>
                  <View style={styles.cardRow}>
                    <DashboardCard
                      title="Appointments"
                      value={stats.todayAppointments}
                      icon="calendar"
                      iconColor="#00897B"
                      width="48%"
                      gradientStart="#E8F5E9"
                      gradientEnd="#C8E6C9"
                      onPress={() => navigation.navigate('DoctorAppointments')}
                    />
                    <DashboardCard
                      title="Pending Requests"
                      value={stats.pendingRequests}
                      icon="alert-circle"
                      iconColor="#F57C00"
                      width="48%"
                      gradientStart="#FFF3E0"
                      gradientEnd="#FFE0B2"
                      onPress={() => navigation.navigate('Requests')}
                    />
                  </View>
                  <View style={styles.cardRow}>
                    <DashboardCard
                      title="Total Patients"
                      value={stats.totalPatients}
                      icon="people"
                      iconColor="#3949AB"
                      width="48%"
                      gradientStart="#E8EAF6"
                      gradientEnd="#C5CAE9"
                      onPress={() => navigation.navigate('Patients')}
                    />
                    <DashboardCard
                      title="Completed Today"
                      value={stats.completedToday}
                      icon="checkmark-circle"
                      iconColor="#43A047"
                      width="48%"
                      gradientStart="#E8F5E9"
                      gradientEnd="#C8E6C9"
                      onPress={() => navigation.navigate('CompletedAppointments')}
                    />
                  </View>
                  <View style={styles.cardRow}>
                    <DashboardCard
                      title="Chat Consultations"
                      value="Start Now"
                      icon="chatbubble"
                      iconColor="#9C27B0"
                      width="100%"
                      gradientStart="#E1BEE7"
                      gradientEnd="#CE93D8"
                      onPress={() => navigation.navigate('Messages')}
                      style={{
                        borderWidth: 2,
                        borderColor: doctorColors.primary,
                        elevation: 12,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 8 },
                        shadowOpacity: 0.35,
                        shadowRadius: 12,
                      }}
                    />
                  </View>
                </View>
              </View>
            )}

            {/* Charts Section */}
            <Animated.View
              style={[
                styles.sectionContainer,
                {
                  opacity: chartsAnim,
                  transform: [
                    {
                      translateY: chartsAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [20, 0]
                      })
                    }
                  ]
                }
              ]}
            >
              <DashboardChart
                title="Weekly Appointments"
                subtitle="Last 7 days"
                data={weeklyStats}
                type="bar"
                color={doctorColors.primary}
              />
            </Animated.View>

            {diagnosisData.data.length > 0 && (
              <Animated.View
                style={[
                  styles.sectionContainer,
                  {
                    opacity: chartsAnim,
                    transform: [
                      {
                        translateY: chartsAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ]
                  }
                ]}
              >
                <DashboardChart
                  title="Diagnosis Distribution"
                  subtitle="Last 30 days"
                  data={diagnosisData}
                  type="pie"
                  height={200}
                />
              </Animated.View>
            )}

            {/* Appointments Section */}
            <SectionHeader title="Today's Appointments" section="appointments" icon="calendar" />

            {expandedSections.appointments && (
              <Animated.View
                style={[
                  {
                    opacity: listsAnim,
                    transform: [
                      {
                        translateY: listsAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ]
                  }
                ]}
              >
                <UpcomingList
                  data={appointments}
                  onItemPress={(item) => navigation.navigate('AppointmentDetail', { id: item.id })}
                  onViewAll={() => navigation.navigate('DoctorAppointments')}
                  emptyText="No appointments scheduled for today"
                  backgroundColor="#ffffff"
                />
              </Animated.View>
            )}

            {/* Requests Section */}
            <SectionHeader title="Pending Requests" section="requests" icon="alert-circle" />

            {expandedSections.requests && (
              <Animated.View
                style={[
                  {
                    opacity: listsAnim,
                    transform: [
                      {
                        translateY: listsAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ]
                  }
                ]}
              >
                <UpcomingList
                  data={requests}
                  onItemPress={(item) => navigation.navigate('RequestDetail', { id: item.id })}
                  onViewAll={() => navigation.navigate('Requests')}
                  emptyText="No pending requests"
                  backgroundColor="#ffffff"
                />
              </Animated.View>
            )}

            {/* Patient Vitals Section */}
            <SectionHeader title="Patient Vitals Tracking" section="vitals" icon="pulse" />

            {expandedSections.vitals && (
              <Animated.View
                style={[
                  styles.sectionContainer,
                  {
                    opacity: listsAnim,
                    transform: [
                      {
                        translateY: listsAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ],
                    padding: 0
                  }
                ]}
              >
                <View style={styles.vitalsChartsContainer}>
                  <PatientVitalsCharts />
                </View>
              </Animated.View>
            )}

            {/* Progress Notes Section */}
            <SectionHeader title="Progress Notes" section="notes" icon="document-text" />

            {expandedSections.notes && (
              <Animated.View
                style={[
                  styles.sectionContainer,
                  {
                    opacity: listsAnim,
                    transform: [
                      {
                        translateY: listsAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ]
                  }
                ]}
              >
                <PatientProgressNotes
                  patientId={selectedPatient?.id}
                  token={user?.token}
                />
              </Animated.View>
            )}

            {/* Quick Actions Section */}
            <SectionHeader title="Quick Actions" section="quickActions" icon="flash" />

            {expandedSections.quickActions && (
              <Animated.View
                style={[
                  styles.quickActions,
                  {
                    opacity: actionsAnim,
                    transform: [
                      {
                        translateY: actionsAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ]
                  }
                ]}
              >
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => navigation.navigate('NewAppointment')}
                    activeOpacity={0.8}
                  >
                    <View style={[styles.actionIcon, { backgroundColor: '#E8F5E9' }]}>
                      <Ionicons name="calendar" size={24} color="#43A047" />
                    </View>
                    <Text style={styles.actionText}>Create Appointment</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => navigation.navigate('Messages')}
                    activeOpacity={0.8}
                  >
                    <View style={[styles.actionIcon, { backgroundColor: '#E3F2FD' }]}>
                      <Ionicons name="chatbubble" size={24} color="#2196F3" />
                    </View>
                    <Text style={styles.actionText}>Send Message</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => navigation.navigate('NewPrescription')}
                    activeOpacity={0.8}
                  >
                    <View style={[styles.actionIcon, { backgroundColor: '#E1F5FE' }]}>
                      <Ionicons name="document-text" size={24} color="#0288D1" />
                    </View>
                    <Text style={styles.actionText}>Write Prescription</Text>
                  </TouchableOpacity>
                </View>
              </Animated.View>
            )}
          </View>

          {/* Floating Action Button */}
          <TouchableOpacity
            style={[styles.fab, { backgroundColor: doctorColors.primary }]}
            onPress={() => navigation.navigate('NewAppointment')}
            activeOpacity={0.8}
          >
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </Animated.ScrollView>
      )}
    </DashboardLayout>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f7fa',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#424242',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  headerSection: {
    width: '100%',
    marginBottom: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  greeting: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingText: {
    fontSize: 26,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  dateText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  contentSection: {
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: 80, // Extra padding for FAB
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 16,
    marginTop: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  sectionContent: {
    marginBottom: 30,
    paddingTop: 5,
  },
  cardsContainer: {
    marginBottom: 30,
    paddingHorizontal: 0,
    paddingTop: 10,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    height: 170, // Increased height for all card rows
  },
  sectionContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 18,
    marginBottom: 30,
    marginTop: 5,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActions: {
    marginBottom: 30,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  actionButton: {
    width: '31%',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  actionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#424242',
    textAlign: 'center',
    fontWeight: '500',
  },
  vitalsChartsContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    height: 500,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  section: {
    marginTop: 20,
    marginBottom: 20,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.27,
    shadowRadius: 4.65,
    zIndex: 999,
  },
});

export default DoctorDashboard;