{"name": "neuro_care", "version": "1.0.0", "description": "A healthcare application focused on neurological care.", "main": "index.js", "scripts": {"start": "concurrently \"npm  start --prefix client\" \"npm  start --prefix server\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@stream-io/video-react-native-sdk": "^1.11.3", "concurrently": "^9.1.2", "expo": "^52.0.46", "react-native-splash-screen": "^3.3.0"}}