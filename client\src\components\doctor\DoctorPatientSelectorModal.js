import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ROLE_COLORS } from '../../config/theme';
import { firebaseDoctorPatientsService } from '../../services/firebaseDoctorPatientsService';

const DoctorPatientSelectorModal = ({ visible, onClose, onSuccess, scannerTitle = 'Select Patient' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const doctorColors = ROLE_COLORS.doctor;

  // Load patients when modal becomes visible
  useEffect(() => {
    if (visible) {
      loadPatients();
    }
  }, [visible]);

  // Load patients linked to the current doctor
  const loadPatients = async () => {
    setLoading(true);
    try {
      const doctorPatients = await firebaseDoctorPatientsService.getDoctorPatients();
      setPatients(doctorPatients);
    } catch (error) {
      console.error('Error loading doctor patients:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const handlePatientSelect = (patient) => {
    // Format patient data for the prescription form
    const formattedPatient = {
      id: patient.id,
      name: `${patient.firstName} ${patient.lastName}`
    };
    onSuccess(formattedPatient);
    onClose();
  };

  const renderPatientItem = ({ item }) => (
    <TouchableOpacity
      style={styles.patientItem}
      onPress={() => handlePatientSelect(item)}
    >
      <View style={styles.patientIcon}>
        <Text style={styles.patientInitials}>
          {item.firstName.charAt(0)}{item.lastName.charAt(0)}
        </Text>
      </View>
      <View style={styles.patientInfo}>
        <Text style={styles.patientName}>{item.firstName} {item.lastName}</Text>
        <Text style={styles.patientDetails}>
          {item.gender ? `${item.gender} • ` : ''}
          {item.age ? `${item.age} years` : ''}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#9E9E9E" />
    </TouchableOpacity>
  );

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{scannerTitle}</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search patients..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color="#757575" />
              </TouchableOpacity>
            ) : null}
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={doctorColors.primary} />
              <Text style={styles.loadingText}>Loading your patients...</Text>
            </View>
          ) : (
            <FlatList
              data={filteredPatients}
              renderItem={renderPatientItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.patientList}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  {searchQuery ? (
                    <>
                      <Ionicons name="search" size={48} color="#BDBDBD" />
                      <Text style={styles.emptyText}>No patients found matching "{searchQuery}"</Text>
                    </>
                  ) : (
                    <>
                      <Ionicons name="people" size={48} color="#BDBDBD" />
                      <Text style={styles.emptyText}>You don't have any linked patients yet</Text>
                    </>
                  )}
                </View>
              }
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  loadingText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
  },
  patientList: {
    paddingBottom: 16,
  },
  patientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingVertical: 12,
  },
  patientIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  patientInitials: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  patientDetails: {
    fontSize: 14,
    color: '#757575',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default DoctorPatientSelectorModal;
