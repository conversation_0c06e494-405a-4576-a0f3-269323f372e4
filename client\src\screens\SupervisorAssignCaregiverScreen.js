import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Avatar, Button, Searchbar, Chip, Divider } from 'react-native-paper';
import { ROLE_COLORS } from '../config/theme';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';
import { showMessage } from 'react-native-flash-message';
import CaregiverScannerModal from '../components/scanner/CaregiverScannerModal';

const SupervisorAssignCaregiverScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { patient } = route.params || {};
  const supervisorColors = ROLE_COLORS.supervisor;

  const [loading, setLoading] = useState(true);
  const [caregivers, setCaregivers] = useState([]);
  const [assignedCaregivers, setAssignedCaregivers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCaregiverId, setSelectedCaregiverId] = useState(null);
  const [assigning, setAssigning] = useState(false);
  const [removing, setRemoving] = useState(false);
  const [showScanner, setShowScanner] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load available caregivers
      const availableCaregivers = await firebaseCaregiverService.getAvailableCaregivers();
      setCaregivers(availableCaregivers);

      // Load assigned caregivers for this patient
      if (patient?.uid) {
        const patientCaregivers = await firebaseCaregiverService.getPatientCaregivers(patient.uid);
        setAssignedCaregivers(patientCaregivers);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load caregivers. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleSelectCaregiver = (caregiverId) => {
    setSelectedCaregiverId(caregiverId === selectedCaregiverId ? null : caregiverId);
  };

  const handleAssignCaregiver = async () => {
    if (!selectedCaregiverId) {
      showMessage({
        message: 'Error',
        description: 'Please select a caregiver to assign',
        type: 'warning',
      });
      return;
    }

    try {
      setAssigning(true);
      await firebaseCaregiverService.assignCaregiverToPatient(patient.uid, selectedCaregiverId);

      // Refresh the assigned caregivers list
      const patientCaregivers = await firebaseCaregiverService.getPatientCaregivers(patient.uid);
      setAssignedCaregivers(patientCaregivers);

      // Reset selection
      setSelectedCaregiverId(null);

      showMessage({
        message: 'Success',
        description: 'Caregiver assigned successfully',
        type: 'success',
        backgroundColor: supervisorColors.primary,
      });
    } catch (error) {
      console.error('Error assigning caregiver:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to assign caregiver. Please try again.',
        type: 'danger',
      });
    } finally {
      setAssigning(false);
    }
  };

  const handleRemoveCaregiver = async (caregiverId) => {
    Alert.alert(
      'Remove Caregiver',
      'Are you sure you want to remove this caregiver from the patient?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              setRemoving(true);
              await firebaseCaregiverService.removeCaregiverAssignment(patient.uid, caregiverId);

              // Refresh the assigned caregivers list
              const patientCaregivers = await firebaseCaregiverService.getPatientCaregivers(patient.uid);
              setAssignedCaregivers(patientCaregivers);

              showMessage({
                message: 'Success',
                description: 'Caregiver removed successfully',
                type: 'success',
                backgroundColor: supervisorColors.primary,
              });
            } catch (error) {
              console.error('Error removing caregiver:', error);
              showMessage({
                message: 'Error',
                description: 'Failed to remove caregiver. Please try again.',
                type: 'danger',
              });
            } finally {
              setRemoving(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Filter caregivers based on search query
  const filteredCaregivers = caregivers.filter(caregiver => {
    const fullName = `${caregiver.firstName} ${caregiver.lastName}`.toLowerCase();
    const email = caregiver.email.toLowerCase();
    const query = searchQuery.toLowerCase();

    return fullName.includes(query) || email.includes(query);
  });

  // Filter out caregivers that are already assigned
  const availableCaregivers = filteredCaregivers.filter(caregiver =>
    !assignedCaregivers.some(assigned => assigned.uid === caregiver.uid)
  );

  const renderCaregiverItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.caregiverCard,
        selectedCaregiverId === item.uid && styles.selectedCaregiverCard
      ]}
      onPress={() => handleSelectCaregiver(item.uid)}
    >
      <View style={styles.caregiverCardContent}>
        <Avatar.Text
          size={50}
          label={`${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`}
          backgroundColor={supervisorColors.primary}
          color="#fff"
        />
        <View style={styles.caregiverInfo}>
          <Text style={styles.caregiverName}>{`${item.firstName || ''} ${item.lastName || ''}`}</Text>
          <Text style={styles.caregiverEmail}>{item.email || ''}</Text>
          {item.speciality && (
            <Chip style={styles.specialityChip} textStyle={styles.specialityText}>
              {item.speciality}
            </Chip>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderAssignedCaregiverItem = ({ item }) => (
    <Card style={styles.assignedCard}>
      <Card.Content style={styles.assignedCardContent}>
        <Avatar.Text
          size={40}
          label={`${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`}
          backgroundColor={supervisorColors.primary}
          color="#fff"
        />
        <View style={styles.assignedInfo}>
          <Text style={styles.assignedName}>{`${item.firstName || ''} ${item.lastName || ''}`}</Text>
          <Text style={styles.assignedEmail}>{item.email || ''}</Text>
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveCaregiver(item.uid)}
          disabled={removing}
        >
          <Ionicons name="close-circle" size={24} color="#FF5252" />
        </TouchableOpacity>
      </Card.Content>
    </Card>
  );

  if (!patient) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No patient selected. Please go back and select a patient.</Text>
        <Button
          mode="contained"
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          Go Back
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={supervisorColors.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Assign Caregivers</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={loadData}
        >
          <Ionicons name="refresh" size={24} color={supervisorColors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.patientInfoCard}>
        <Text style={styles.patientInfoTitle}>Patient:</Text>
        <Text style={styles.patientInfoName}>{`${patient.firstName || ''} ${patient.lastName || ''}`}</Text>
      </View>

      <View style={styles.assignedSection}>
        <Text style={styles.sectionTitle}>Assigned Caregivers</Text>
        {loading ? (
          <ActivityIndicator size="large" color={supervisorColors.primary} />
        ) : assignedCaregivers.length === 0 ? (
          <Text style={styles.noDataText}>No caregivers assigned yet</Text>
        ) : (
          <FlatList
            data={assignedCaregivers}
            renderItem={renderAssignedCaregiverItem}
            keyExtractor={(item) => item.uid}
            style={styles.assignedList}
          />
        )}
      </View>

      <Divider style={styles.divider} />

      <View style={styles.availableSection}>
        <View style={styles.sectionHeaderRow}>
          <Text style={styles.sectionTitle}>Available Caregivers</Text>
          <TouchableOpacity
            style={styles.scanButton}
            onPress={() => setShowScanner(true)}
          >
            <Ionicons name="qr-code" size={20} color="#fff" />
            <Text style={styles.scanButtonText}>Scan Code</Text>
          </TouchableOpacity>
        </View>

        <Searchbar
          placeholder="Search caregivers..."
          onChangeText={handleSearchChange}
          value={searchQuery}
          style={styles.searchBar}
        />

        {loading ? (
          <ActivityIndicator size="large" color={supervisorColors.primary} />
        ) : availableCaregivers.length === 0 ? (
          <Text style={styles.noDataText}>No available caregivers found</Text>
        ) : (
          <FlatList
            data={availableCaregivers}
            renderItem={renderCaregiverItem}
            keyExtractor={(item) => item.uid}
            style={styles.caregiverList}
          />
        )}
      </View>

      <View style={styles.actionButtonContainer}>
        <Button
          mode="contained"
          onPress={handleAssignCaregiver}
          style={[styles.assignButton, { backgroundColor: supervisorColors.primary }]}
          disabled={!selectedCaregiverId || assigning}
          loading={assigning}
        >
          Assign Selected Caregiver
        </Button>
      </View>

      <CaregiverScannerModal
        visible={showScanner}
        onClose={() => setShowScanner(false)}
        patientId={patient?.uid}
        scannerTitle="Scan Caregiver Code"
        onSuccess={() => {
          setShowScanner(false);
          // Refresh the assigned caregivers list after successful assignment
          setTimeout(() => loadData(), 1500);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 8,
  },
  refreshButton: {
    padding: 8,
  },
  patientInfoCard: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  patientInfoTitle: {
    fontSize: 14,
    color: '#666',
  },
  patientInfoName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  assignedSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  assignedList: {
    maxHeight: 150,
  },
  assignedCard: {
    marginBottom: 8,
    elevation: 1,
  },
  assignedCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  assignedInfo: {
    flex: 1,
    marginLeft: 12,
  },
  assignedName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  assignedEmail: {
    fontSize: 12,
    color: '#666',
  },
  removeButton: {
    padding: 8,
  },
  divider: {
    height: 1,
    backgroundColor: '#ddd',
    marginHorizontal: 16,
  },
  availableSection: {
    padding: 16,
    flex: 1,
  },
  searchBar: {
    marginBottom: 16,
    elevation: 0,
    backgroundColor: '#fff',
  },
  caregiverList: {
    flex: 1,
  },
  caregiverCard: {
    backgroundColor: '#fff',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    elevation: 1,
  },
  selectedCaregiverCard: {
    backgroundColor: '#E8F5E9',
    borderWidth: 1,
    borderColor: ROLE_COLORS.supervisor.primary,
  },
  caregiverCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  caregiverInfo: {
    marginLeft: 12,
    flex: 1,
  },
  caregiverName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  caregiverEmail: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  specialityChip: {
    alignSelf: 'flex-start',
    backgroundColor: '#E1F5FE',
    height: 24,
  },
  specialityText: {
    fontSize: 10,
  },
  actionButtonContainer: {
    padding: 16,
    backgroundColor: '#fff',
    elevation: 4,
  },
  assignButton: {
    borderRadius: 8,
  },
  noDataText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 16,
  },
  errorText: {
    textAlign: 'center',
    color: '#F44336',
    margin: 16,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ROLE_COLORS.supervisor.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  scanButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default SupervisorAssignCaregiverScreen;
