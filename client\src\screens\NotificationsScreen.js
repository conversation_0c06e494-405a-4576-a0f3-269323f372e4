import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useAppointments } from '../contexts/AppointmentContext';
import { useNavigation } from '@react-navigation/native';
import { COLORS } from '../config/theme';
import NotificationsList from '../components/notifications/NotificationsList';
import AsyncStorage from '@react-native-async-storage/async-storage';

const NotificationsScreen = ({ route }) => {
  const { user } = useAuth();
  const { getUpcomingAppointments, fetchAppointments } = useAppointments();
  const navigation = useNavigation();
  const [notifications, setNotifications] = useState([]);

  // Load notifications when the screen is focused
  useEffect(() => {
    const loadNotifications = async () => {
      try {
        // Start with notifications from route params if available
        let allNotifications = route.params?.notifications || [];

        // If user is a patient, add appointment notifications
        if (user?.role === 'patient') {
          // Fetch appointments from Firebase
          await fetchAppointments(true);

          // Get upcoming appointments
          const upcomingAppointments = getUpcomingAppointments();

          // Generate notifications for upcoming appointments
          const appointmentNotifications = upcomingAppointments.map(appointment => {
            // Parse the appointment date
            let appointmentDate;
            try {
              appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
              if (isNaN(appointmentDate.getTime())) {
                appointmentDate = new Date(`${appointment.date} ${appointment.time}`);
              }
            } catch (error) {
              appointmentDate = new Date();
            }

            // Format the time display
            const now = new Date();
            const dayDiff = Math.round((appointmentDate - now) / (24 * 60 * 60 * 1000));
            let timeDisplay;

            if (dayDiff === 0) {
              timeDisplay = `Today at ${appointment.time}`;
            } else if (dayDiff === 1) {
              timeDisplay = `Tomorrow at ${appointment.time}`;
            } else if (dayDiff > 1 && dayDiff < 7) {
              timeDisplay = `In ${dayDiff} days`;
            } else {
              timeDisplay = appointmentDate.toLocaleDateString();
            }

            return {
              id: `appointment-${appointment.id}`,
              type: 'appointment',
              title: `Appointment with ${appointment.doctor}`,
              message: `${timeDisplay} - ${appointment.specialty || 'Medical Appointment'}`,
              color: COLORS.primary,
              time: timeDisplay,
              actionType: 'navigate',
              actionTarget: 'Appointments',
              actionParams: { id: appointment.id }
            };
          });

          // Add appointment notifications to the list
          if (appointmentNotifications.length > 0) {
            // Filter out any duplicates that might be in both sources
            const existingIds = allNotifications.map(n => n.id);
            const newAppointmentNotifications = appointmentNotifications.filter(
              n => !existingIds.includes(n.id)
            );

            allNotifications = [...allNotifications, ...newAppointmentNotifications];
          }
        }

        setNotifications(allNotifications);
      } catch (error) {
        console.error('Error loading notifications:', error);
      }
    };

    loadNotifications();

    // Also refresh when the screen is focused
    const unsubscribe = navigation.addListener('focus', loadNotifications);
    return unsubscribe;
  }, [navigation, route.params, user]);

  const handleNotificationPress = (notification) => {
    console.log('Notification pressed:', notification);

    // Handle navigation based on actionType and actionTarget
    if (notification.actionType === 'navigate' && notification.actionTarget) {
      navigation.navigate(notification.actionTarget);
    }

    // Optional: Dismiss the notification after handling it
    handleDismiss(notification.id);
  };

  const handleDismiss = async (id) => {
    console.log('Dismissing notification with id:', id);

    // If it's an appointment notification, we need to update the storage
    if (id.startsWith('appointment-')) {
      try {
        // Get current notifications from AsyncStorage
        const notificationsStr = await AsyncStorage.getItem('@neurocare:appointment_notifications');
        const currentNotifications = notificationsStr ? JSON.parse(notificationsStr) : [];

        // Filter out the dismissed notification
        const updatedNotifications = currentNotifications.filter(n => n.id !== id);

        // Save back to storage
        await AsyncStorage.setItem(
          '@neurocare:appointment_notifications',
          JSON.stringify(updatedNotifications)
        );
      } catch (error) {
        console.error('Error updating appointment notifications:', error);
      }
    }

    // Update state
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const handleDismissAll = async () => {
    console.log('Dismissing all notifications');

    // Clear appointment notifications from storage
    if (user?.role === 'patient') {
      try {
        await AsyncStorage.setItem('@neurocare:appointment_notifications', JSON.stringify([]));
      } catch (error) {
        console.error('Error clearing appointment notifications:', error);
      }
    }

    // Update state
    setNotifications([]);
  };

  return (
    <View style={styles.container}>
      {notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No notifications</Text>
        </View>
      ) : (
        <NotificationsList
          notifications={notifications}
          onPress={handleNotificationPress}
          onDismiss={handleDismiss}
          onDismissAll={handleDismissAll}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
    padding: 0, // Remove any padding
    margin: 0, // Remove any margin
    width: '100%', // Ensure full width
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.textMedium,
    textAlign: 'center',
  }
});

export default NotificationsScreen;
