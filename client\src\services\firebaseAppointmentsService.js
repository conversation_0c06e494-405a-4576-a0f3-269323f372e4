import {
  db,
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy
} from '../config/firebase';
import { auth } from '../config/firebase';

/**
 * Service for managing appointments in Firebase
 */
export const firebaseAppointmentsService = {
  /**
   * Save a new appointment to Firebase
   * @param {Object} appointmentData - The appointment data to save
   * @returns {Promise<Object>} - The saved appointment record with ID
   */
  saveAppointment: async (appointmentData) => {
    try {
      // Get current user
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create appointment data with patient ID
      const appointmentToSave = {
        ...appointmentData,
        patientId: currentUser.uid,
        createdAt: new Date().toISOString(),
        status: appointmentData.status || 'Pending'
      };

      // Add to Firestore
      const appointmentsCollection = collection(db, 'appointments');
      const docRef = await addDoc(appointmentsCollection, appointmentToSave);

      // Return the saved appointment with ID
      return {
        id: docRef.id,
        ...appointmentToSave
      };
    } catch (error) {
      console.error('Error saving appointment to Firebase:', error);
      throw new Error('Failed to save appointment');
    }
  },

  /**
   * Get all appointments for the current user
   * @returns {Promise<Array>} - Array of appointment records
   */
  getAllAppointments: async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const appointmentsCollection = collection(db, 'appointments');
      const q = query(
        appointmentsCollection,
        where('patientId', '==', currentUser.uid),
        orderBy('date', 'asc'),
        orderBy('time', 'asc')
      );

      const querySnapshot = await getDocs(q);
      const appointments = [];

      querySnapshot.forEach((doc) => {
        appointments.push({
          id: doc.id,
          ...doc.data()
        });
      });

      return appointments;
    } catch (error) {
      console.error('Error getting appointments from Firebase:', error);
      return [];
    }
  },

  /**
   * Get upcoming appointments (appointments in the future)
   * @returns {Promise<Array>} - Array of upcoming appointment records
   */
  getUpcomingAppointments: async () => {
    try {
      const appointments = await firebaseAppointmentsService.getAllAppointments();
      const now = new Date();

      // Filter for upcoming appointments
      return appointments.filter(appointment => {
        const appointmentDate = new Date(`${appointment.date} ${appointment.time}`);
        return appointmentDate > now;
      });
    } catch (error) {
      console.error('Error getting upcoming appointments:', error);
      return [];
    }
  },

  /**
   * Update an appointment
   * @param {string} appointmentId - The appointment ID to update
   * @param {Object} updatedData - The updated appointment data
   * @returns {Promise<Object>} - The updated appointment
   */
  updateAppointment: async (appointmentId, updatedData) => {
    try {
      const appointmentRef = doc(db, 'appointments', appointmentId);
      const appointmentSnap = await getDoc(appointmentRef);

      if (!appointmentSnap.exists()) {
        throw new Error('Appointment not found');
      }

      const currentData = appointmentSnap.data();

      // Update the document
      const dataToUpdate = {
        ...updatedData,
        updatedAt: new Date().toISOString()
      };

      await updateDoc(appointmentRef, dataToUpdate);

      // Return the updated appointment
      return {
        id: appointmentId,
        ...currentData,
        ...dataToUpdate
      };
    } catch (error) {
      console.error('Error updating appointment in Firebase:', error);
      throw new Error('Failed to update appointment');
    }
  },

  /**
   * Delete an appointment
   * @param {string} appointmentId - The appointment ID to delete
   * @returns {Promise<boolean>} - Success status
   */
  deleteAppointment: async (appointmentId) => {
    try {
      const appointmentRef = doc(db, 'appointments', appointmentId);
      await deleteDoc(appointmentRef);
      return true;
    } catch (error) {
      console.error('Error deleting appointment from Firebase:', error);
      return false;
    }
  },

  /**
   * Get appointment by ID
   * @param {string} appointmentId - The appointment ID
   * @returns {Promise<Object|null>} - The appointment or null if not found
   */
  getAppointmentById: async (appointmentId) => {
    try {
      const appointmentRef = doc(db, 'appointments', appointmentId);
      const appointmentSnap = await getDoc(appointmentRef);

      if (!appointmentSnap.exists()) {
        return null;
      }

      return {
        id: appointmentId,
        ...appointmentSnap.data()
      };
    } catch (error) {
      console.error('Error getting appointment by ID from Firebase:', error);
      return null;
    }
  },

  /**
   * Get all appointments for the current doctor
   * @param {Object} options - Query options (status, date, etc.)
   * @returns {Promise<Array>} - Array of appointment records
   */
  getDoctorAppointments: async (options = {}) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const appointmentsCollection = collection(db, 'appointments');

      // Start with base query for doctor's appointments - only filter by doctorId
      // to avoid complex index requirements
      let queryConstraints = [
        where('doctorId', '==', currentUser.uid)
      ];

      // Create the query with minimal constraints
      const q = query(appointmentsCollection, ...queryConstraints);
      const querySnapshot = await getDocs(q);

      let appointments = [];

      // Process each appointment document
      for (const docSnapshot of querySnapshot.docs) {
        const appointmentData = docSnapshot.data();

        // Apply filters in JavaScript instead of in the query
        // Filter by status if provided
        if (options.status && appointmentData.status !== options.status) {
          continue;
        }

        // Filter by date if provided
        if (options.date && appointmentData.date !== options.date) {
          continue;
        }

        // Get patient details if needed
        let patientDetails = null;
        if (appointmentData.patientId && options.includePatientDetails) {
          try {
            const patientRef = doc(db, 'users', appointmentData.patientId);
            const patientSnap = await getDoc(patientRef);
            if (patientSnap.exists()) {
              patientDetails = patientSnap.data();
            }
          } catch (patientError) {
            console.error('Error fetching patient details:', patientError);
          }
        }

        appointments.push({
          id: docSnapshot.id,
          ...appointmentData,
          patient: patientDetails || { id: appointmentData.patientId, name: 'Unknown Patient' }
        });
      }

      // Sort the appointments in JavaScript
      appointments.sort((a, b) => {
        // First sort by date
        const dateComparison = options.dateOrder === 'asc'
          ? a.date.localeCompare(b.date)
          : b.date.localeCompare(a.date);

        // If dates are equal, sort by time
        if (dateComparison === 0) {
          return options.timeOrder === 'asc'
            ? a.time.localeCompare(b.time)
            : b.time.localeCompare(a.time);
        }

        return dateComparison;
      });

      return appointments;
    } catch (error) {
      console.error('Error getting doctor appointments from Firebase:', error);
      return [];
    }
  },

  /**
   * Get appointments for patients linked to the current caregiver
   * @param {Object} options - Query options (status, date, etc.)
   * @returns {Promise<Array>} - Array of appointment records
   */
  getCaregiverPatientAppointments: async (options = {}) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        return [];
      }

      const appointmentsCollection = collection(db, 'appointments');
      let appointments = [];

      // Get appointments for each linked patient
      for (const patientId of linkedPatientIds) {
        // Create query for this patient's appointments - simplified to avoid requiring composite index
        const q = query(
          appointmentsCollection,
          where('patientId', '==', patientId)
        );

        const querySnapshot = await getDocs(q);

        // Process each appointment
        for (const docSnapshot of querySnapshot.docs) {
          const appointmentData = docSnapshot.data();

          // Apply filters in JavaScript
          // Filter by status if provided
          if (options.status && appointmentData.status !== options.status) {
            continue;
          }

          // Filter by date if provided
          if (options.date && appointmentData.date !== options.date) {
            continue;
          }

          // Get patient details
          let patientDetails = null;
          try {
            const patientRef = doc(db, 'users', patientId);
            const patientSnap = await getDoc(patientRef);
            if (patientSnap.exists()) {
              patientDetails = patientSnap.data();
            }
          } catch (patientError) {
            console.error('Error fetching patient details:', patientError);
          }

          // Get doctor details if available
          let doctorDetails = null;
          if (appointmentData.doctorId) {
            try {
              const doctorRef = doc(db, 'users', appointmentData.doctorId);
              const doctorSnap = await getDoc(doctorRef);
              if (doctorSnap.exists()) {
                doctorDetails = doctorSnap.data();
              }
            } catch (doctorError) {
              console.error('Error fetching doctor details:', doctorError);
            }
          }

          appointments.push({
            id: docSnapshot.id,
            ...appointmentData,
            patient: patientDetails || { id: patientId, name: 'Unknown Patient' },
            doctor: doctorDetails || { id: appointmentData.doctorId, name: appointmentData.doctor || 'Unknown Doctor' }
          });
        }
      }

      // Sort appointments
      appointments.sort((a, b) => {
        // First sort by date
        const dateComparison = options.dateOrder === 'desc'
          ? b.date.localeCompare(a.date)
          : a.date.localeCompare(b.date);

        // If dates are equal, sort by time
        if (dateComparison === 0) {
          return options.timeOrder === 'desc'
            ? b.time.localeCompare(a.time)
            : a.time.localeCompare(b.time);
        }

        return dateComparison;
      });

      return appointments;
    } catch (error) {
      console.error('Error getting caregiver patient appointments from Firebase:', error);
      return [];
    }
  },

  /**
   * Get appointments for patients linked to the current supervisor
   * @param {Object} options - Query options (status, date, etc.)
   * @returns {Promise<Array>} - Array of appointment records
   */
  getSupervisorPatientAppointments: async (options = {}) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        return [];
      }

      const appointmentsCollection = collection(db, 'appointments');
      let appointments = [];

      // Get appointments for each linked patient
      for (const patientId of linkedPatientIds) {
        // Create query for this patient's appointments - simplified to avoid requiring composite index
        const q = query(
          appointmentsCollection,
          where('patientId', '==', patientId)
        );

        const querySnapshot = await getDocs(q);

        // Process each appointment
        for (const docSnapshot of querySnapshot.docs) {
          const appointmentData = docSnapshot.data();

          // Apply filters in JavaScript
          // Filter by status if provided
          if (options.status && appointmentData.status !== options.status) {
            continue;
          }

          // Filter by date if provided
          if (options.date && appointmentData.date !== options.date) {
            continue;
          }

          // Get patient details
          let patientDetails = null;
          try {
            const patientRef = doc(db, 'users', patientId);
            const patientSnap = await getDoc(patientRef);
            if (patientSnap.exists()) {
              patientDetails = patientSnap.data();
            }
          } catch (patientError) {
            console.error('Error fetching patient details:', patientError);
          }

          // Get doctor details if available
          let doctorDetails = null;
          if (appointmentData.doctorId) {
            try {
              const doctorRef = doc(db, 'users', appointmentData.doctorId);
              const doctorSnap = await getDoc(doctorRef);
              if (doctorSnap.exists()) {
                doctorDetails = doctorSnap.data();
              }
            } catch (doctorError) {
              console.error('Error fetching doctor details:', doctorError);
            }
          }

          appointments.push({
            id: docSnapshot.id,
            ...appointmentData,
            patient: patientDetails || { id: patientId, name: 'Unknown Patient' },
            doctor: doctorDetails || { id: appointmentData.doctorId, name: appointmentData.doctor || 'Unknown Doctor' }
          });
        }
      }

      // Sort appointments
      appointments.sort((a, b) => {
        // First sort by date
        const dateComparison = options.dateOrder === 'desc'
          ? b.date.localeCompare(a.date)
          : a.date.localeCompare(b.date);

        // If dates are equal, sort by time
        if (dateComparison === 0) {
          return options.timeOrder === 'desc'
            ? b.time.localeCompare(a.time)
            : a.time.localeCompare(b.time);
        }

        return dateComparison;
      });

      return appointments;
    } catch (error) {
      console.error('Error getting supervisor patient appointments from Firebase:', error);
      return [];
    }
  }
};
