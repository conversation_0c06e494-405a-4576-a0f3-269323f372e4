import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ROLE_COLORS } from '../../../config/theme';
import { firebaseDoctorPatientsService } from '../../../services/firebaseDoctorPatientsService';

const PatientMedicationsViewer = ({ patientId, patientName }) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [medications, setMedications] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState(null);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientMedications();
  }, [patientId]);

  const fetchPatientMedications = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch medications for the selected patient from Firebase
      const medicationsData = await firebaseDoctorPatientsService.getPatientMedications(patientId);
      console.log(`Fetched ${medicationsData.length} medications for patient ${patientId}`);
      setMedications(medicationsData || []);
    } catch (error) {
      console.error('Error fetching patient medications from Firebase:', error);
    } finally {
      setLoading(false);
    }
  };

  const openMedicationDetails = (medication) => {
    setSelectedMedication(medication);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get frequency text
  const getFrequencyText = (frequency) => {
    if (!frequency) return 'As needed';

    if (frequency.type === 'daily') {
      return `${frequency.times} time(s) daily`;
    } else if (frequency.type === 'weekly') {
      const days = frequency.days || [];
      if (days.length === 7) return 'Every day of the week';
      if (days.length === 0) return 'Weekly';
      return `${days.join(', ')}`;
    } else if (frequency.type === 'monthly') {
      return `${frequency.dayOfMonth || 1} day of each month`;
    }

    return 'Custom schedule';
  };

  // Render the medications list
  const renderMedicationsList = () => {
    if (medications.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="medkit-outline" size={48} color={doctorColors.primary} />
          <Text style={styles.emptyText}>No medications found for this patient</Text>
        </View>
      );
    }

    return (
      <View>
        {medications.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.medicationCard}
            onPress={() => openMedicationDetails(item)}
          >
            <View style={styles.medicationIconContainer}>
              <Ionicons name="medkit" size={24} color="#fff" />
            </View>
            <View style={styles.medicationInfo}>
              <Text style={styles.medicationName}>{item.name}</Text>
              <Text style={styles.medicationDosage}>{item.dosage} {item.unit}</Text>
              <Text style={styles.medicationFrequency}>
                {getFrequencyText(item.frequency)}
              </Text>
              {item.startDate && (
                <Text style={styles.medicationDate}>
                  Started: {formatDate(item.startDate)}
                </Text>
              )}
            </View>
            <Ionicons name="chevron-forward" size={24} color="#ccc" />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedMedication) return null;

    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Medication Details</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent} nestedScrollEnabled={true}>
              <View style={styles.modalMedicationHeader}>
                <View style={styles.modalMedicationIconContainer}>
                  <Ionicons name="medkit" size={32} color="#fff" />
                </View>
                <View>
                  <Text style={styles.modalMedicationName}>{selectedMedication.name}</Text>
                  <Text style={styles.modalMedicationDosage}>
                    {selectedMedication.dosage} {selectedMedication.unit}
                  </Text>
                </View>
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Schedule</Text>
                <Text style={styles.modalSectionText}>
                  {getFrequencyText(selectedMedication.frequency)}
                </Text>
                {selectedMedication.frequency && selectedMedication.frequency.specificTime && (
                  <Text style={styles.modalSectionText}>
                    Time: {selectedMedication.frequency.specificTime}
                  </Text>
                )}
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Duration</Text>
                <Text style={styles.modalSectionText}>
                  Start Date: {formatDate(selectedMedication.startDate)}
                </Text>
                {selectedMedication.endDate && (
                  <Text style={styles.modalSectionText}>
                    End Date: {formatDate(selectedMedication.endDate)}
                  </Text>
                )}
              </View>

              {selectedMedication.instructions && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Instructions</Text>
                  <Text style={styles.modalSectionText}>{selectedMedication.instructions}</Text>
                </View>
              )}

              {selectedMedication.reason && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Reason</Text>
                  <Text style={styles.modalSectionText}>{selectedMedication.reason}</Text>
                </View>
              )}

              {selectedMedication.sideEffects && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Possible Side Effects</Text>
                  <Text style={styles.modalSectionText}>{selectedMedication.sideEffects}</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {patientName ? `${patientName}'s Medications` : 'Patient Medications'}
        </Text>
        <Text style={styles.headerSubtitle}>
          View your patient's medication regimen
        </Text>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading patient medications...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current Medications</Text>
            {renderMedicationsList()}
          </View>
        </View>
      )}

      {renderDetailModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  medicationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 14,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  medicationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF9800',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  medicationInfo: {
    flex: 1,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  medicationDosage: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  medicationFrequency: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  medicationDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 6,
  },
  modalContent: {
    padding: 16,
  },
  modalMedicationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalMedicationIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FF9800',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  modalMedicationName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalMedicationDosage: {
    fontSize: 16,
    color: '#666',
    marginTop: 2,
  },
  modalSection: {
    marginBottom: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 14,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  modalSectionText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
});

export default PatientMedicationsViewer;
