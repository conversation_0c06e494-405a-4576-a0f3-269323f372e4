import React, { useState, useEffect } from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useAppointments } from '../contexts/AppointmentContext';
import { showMessage } from 'react-native-flash-message';
import { useNavigation } from '@react-navigation/native';
import { COLORS } from '../config/theme';

// Import components
import {
  PatientDashboard,
  DoctorDashboard,
  AdminDashboard,
  CaregiverDashboard,
  SupervisorDashboard
} from '../components/dashboards';
import ProfileNotification from '../components/notifications/ProfileNotification';

const Dashboard = () => {
  const { user, loading } = useAuth();
  const { getUpcomingAppointments, fetchAppointments } = useAppointments();
  const navigation = useNavigation();
  const [notifications, setNotifications] = useState([]);

  // Generate notifications based on user state and upcoming appointments
  useEffect(() => {
    if (user && !loading) {
      const loadNotifications = async () => {
        try {
          const newNotifications = [];

          // Add profile completion notification if needed
          if (!user.profileCompleted) {
            newNotifications.push({
              id: 'profile-completion',
              type: 'profile',
              title: 'Complete Your Profile',
              message: 'Please complete your profile to unlock all features.',
              color: COLORS.primary,
              time: 'Just now',
              actionType: 'navigate',
              actionTarget: 'Profile'
            });
          }

          // Generate appointment notifications
          if (user.role === 'patient') {
            // Fetch appointments from Firebase
            await fetchAppointments(true);

            // Get upcoming appointments
            const upcomingAppointments = getUpcomingAppointments();

            // Generate notifications for upcoming appointments
            const appointmentNotifications = upcomingAppointments.map(appointment => {
              // Parse the appointment date
              let appointmentDate;
              try {
                appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
                if (isNaN(appointmentDate.getTime())) {
                  appointmentDate = new Date(`${appointment.date} ${appointment.time}`);
                }
              } catch (error) {
                appointmentDate = new Date();
              }

              // Format the time display
              const now = new Date();
              const dayDiff = Math.round((appointmentDate - now) / (24 * 60 * 60 * 1000));
              let timeDisplay;

              if (dayDiff === 0) {
                timeDisplay = `Today at ${appointment.time}`;
              } else if (dayDiff === 1) {
                timeDisplay = `Tomorrow at ${appointment.time}`;
              } else if (dayDiff > 1 && dayDiff < 7) {
                timeDisplay = `In ${dayDiff} days`;
              } else {
                timeDisplay = appointmentDate.toLocaleDateString();
              }

              return {
                id: `appointment-${appointment.id}`,
                type: 'appointment',
                title: `Appointment with ${appointment.doctor}`,
                message: `${timeDisplay} - ${appointment.specialty || 'Medical Appointment'}`,
                color: COLORS.primary,
                time: timeDisplay,
                actionType: 'navigate',
                actionTarget: 'AppointmentDetail',
                actionParams: { id: appointment.id }
              };
            });

            // Add appointment notifications to the list
            if (appointmentNotifications.length > 0) {
              newNotifications.push(...appointmentNotifications);
            }
          }

          setNotifications(newNotifications);
        } catch (error) {
          console.error('Error loading notifications:', error);
        }
      };

      loadNotifications();
    }
  }, [user, loading]);

  // Update menuItems to include Notifications for all dashboard components
  const getMenuItemsWithNotifications = (baseMenuItems) => {
    // Add Notifications menu item if it doesn't already exist
    const hasNotifications = baseMenuItems.some(item => item.screen === 'Notifications');
    if (!hasNotifications) {
      return [
        ...baseMenuItems,
        { label: 'Notifications', icon: 'notifications', screen: 'Notifications' }
      ];
    }
    return baseMenuItems;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="rgba(16, 107, 0, 1)" />
      </View>
    );
  }

  if (!user) {
    return null;
  }

  // Determine the dashboard component to render based on user role
  const renderDashboard = () => {
    let DashboardComponent;

    switch (user.role?.toLowerCase()) {
      case 'patient':
        DashboardComponent = PatientDashboard;
        break;
      case 'doctor':
        DashboardComponent = DoctorDashboard;
        break;
      case 'admin':
        DashboardComponent = AdminDashboard;
        break;
      case 'caregiver':
        DashboardComponent = CaregiverDashboard;
        break;
      case 'supervisor':
        DashboardComponent = SupervisorDashboard;
        break;
      default:
        showMessage({
          message: 'Role Not Assigned',
          description: 'Your user account does not have a role assigned. Please contact support.',
          type: 'warning',
          duration: 4000,
        });
        DashboardComponent = PatientDashboard;
    }

    // Pass notifications to the dashboard component
    // For components that use default parameters instead of defaultProps,
    // we need to handle the case where defaultProps doesn't exist
    let baseMenuItems = [];

    // Try to get menu items from defaultProps if available
    if (DashboardComponent.defaultProps?.menuItems) {
      baseMenuItems = DashboardComponent.defaultProps.menuItems;
    }

    return <DashboardComponent
      notifications={notifications}
      menuItems={getMenuItemsWithNotifications(baseMenuItems)}
    />;
  };

  // Check if profile is incomplete for inline notification
  const isProfileIncomplete = !user.profileCompleted;

  return (
    <View style={styles.container}>
      {isProfileIncomplete && (
        <ProfileNotification userName={user.firstName || user.displayName?.split(' ')[0]} />
      )}
      {renderDashboard()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

export default Dashboard;