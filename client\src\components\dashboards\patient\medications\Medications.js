import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Alert,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMedications } from '../../../../contexts/MedicationContext';
import MedicationReminders from './MedicationReminders';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Audio } from 'expo-av';
import { speak, stop } from 'expo-speech';
import { clearAllMedicationsForUser } from '../../../../services/firebaseMedicationsService';
import { localMedicationsService } from '../../../../services/localStorageService';

const Medications = () => {
  const { user } = useAuth();
  const {
    medications,
    reminders,
    loading,
    error,
    useLocalStorage,
    addMedication,
    addReminder,
    createRemindersForMedication,
    migrateLocalDataToFirebase,
    fetchMedications
  } = useMedications();
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [reminderModalVisible, setReminderModalVisible] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState(null);
  const [localLoading, setLocalLoading] = useState(false);

  // Form states for adding new medication
  const [medicationName, setMedicationName] = useState('');
  const [dosage, setDosage] = useState('');
  const [frequency, setFrequency] = useState('daily');
  const [selectedDays, setSelectedDays] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: true,
    sunday: true
  });
  const [selectedTimes, setSelectedTimes] = useState([{ hour: 9, minute: 0 }]);
  const [showTimePickerIndex, setShowTimePickerIndex] = useState(-1);
  const [instructions, setInstructions] = useState('');

  // Voice recognition states
  const [voiceMode, setVoiceMode] = useState(false);
  const [recording, setRecording] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [processingVoice, setProcessingVoice] = useState(false);
  const [recognizedText, setRecognizedText] = useState('');

  // Form states for adding reminder
  const [reminderDate, setReminderDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [reminderInstructions, setReminderInstructions] = useState('');

  useEffect(() => {
    // Check if we need to migrate local data to Firebase
    const checkMigration = async () => {
      try {
        // This is a one-time migration for existing users
        // In a real app, you might want to track this in user preferences
        await migrateLocalDataToFirebase();
      } catch (err) {
        console.error('Error during migration check:', err);
      }
    };

    if (user) {
      checkMigration();
    }
  }, [user]);

  // Handle adding a time slot
  const handleAddTimeSlot = () => {
    setSelectedTimes([...selectedTimes, { hour: 9, minute: 0 }]);
  };

  // Voice recognition functions
  const startRecording = async () => {
    try {
      // Request audio recording permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Permission to access microphone is required!');
        return;
      }

      // Configure audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
      });

      // Create and prepare the recording
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);
      setIsRecording(true);

      // Using a speech confirmation to indicate listening started
      await speak("Listening for medication details", {
        rate: 1.0,
        onDone: () => {
          console.log("Voice prompt completed");
        },
        onError: (error) => {
          console.error("Voice prompt error:", error);
        }
      });
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    try {
      if (!recording) {
        setIsRecording(false);
        return;
      }

      setIsRecording(false);
      setProcessingVoice(true);

      // Stop recording
      await recording.stopAndUnloadAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });

      const uri = recording.getURI();
      console.log('Recording stopped and stored at', uri);
      setRecording(null);

      // In a real app, you would send this audio file to a speech-to-text service
      // For this demo, we'll simulate the process with a timeout
      setTimeout(() => {
        // Instead of using a predefined text, we'll just show a message
        const userMessage = "I heard you speaking. In a real app, this would use speech recognition.";
        setRecognizedText(userMessage);

        // Don't automatically parse any medication - let the user enter it manually
        setProcessingVoice(false);

        // Show an alert to inform the user
        Alert.alert(
          "Voice Recognition",
          "This is a demo. In a real app, your voice would be processed. Please enter medication details manually.",
          [{ text: "OK" }]
        );
      }, 1000);
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setIsRecording(false);
      setProcessingVoice(false);
      setRecording(null);
      Alert.alert('Error', 'Failed to process recording. Please try again.');
    }
  };

  // Cette fonction est désactivée pour éviter d'ajouter des données de test
  const parseMedicationVoiceCommand = (text) => {
    // Dans une application réelle, cette fonction analyserait le texte
    // pour extraire les informations sur le médicament
    console.log("Voice command parsing disabled to prevent test data");

    // Ne rien faire - laisser l'utilisateur entrer les données manuellement
  };

  const toggleVoiceMode = () => {
    setVoiceMode(!voiceMode);
    setRecognizedText('');
  };

  // Handle removing a time slot
  const handleRemoveTimeSlot = (index) => {
    const newTimes = [...selectedTimes];
    newTimes.splice(index, 1);
    setSelectedTimes(newTimes);
  };

  // Handle time change
  const handleTimeChange = (event, selectedTime, index) => {
    setShowTimePickerIndex(-1);
    if (selectedTime) {
      const newTimes = [...selectedTimes];
      newTimes[index] = {
        hour: selectedTime.getHours(),
        minute: selectedTime.getMinutes()
      };
      setSelectedTimes(newTimes);
    }
  };

  // Toggle day selection
  const toggleDay = (day) => {
    setSelectedDays(prev => ({
      ...prev,
      [day]: !prev[day]
    }));
  };

  // Format time for display
  const formatTime = (hour, minute) => {
    const date = new Date();
    date.setHours(hour, minute, 0);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleAddMedication = async () => {
    if (!medicationName.trim()) {
      Alert.alert('Error', 'Please enter a medication name');
      return;
    }

    setLocalLoading(true);
    try {
      // Create frequency string based on selection
      let frequencyStr = frequency;
      if (frequency === 'specific-days') {
        const days = Object.entries(selectedDays)
          .filter(([_, selected]) => selected)
          .map(([day]) => day.charAt(0).toUpperCase() + day.slice(1))
          .join(', ');
        frequencyStr = `On ${days}`;
      }

      // Format times
      const times = selectedTimes.map(time =>
        formatTime(time.hour, time.minute)
      ).join(', ');

      const newMedication = {
        patientId: user?.uid,
        name: medicationName,
        dosage: dosage,
        frequency: frequencyStr,
        frequencyType: frequency,
        frequencyDays: selectedDays,
        frequencyTimes: selectedTimes,
        instructions: instructions,
      };

      // Use the context method to save to Firebase
      const savedMedication = await addMedication(newMedication);

      // Create reminders for the medication based on frequency
      await createRemindersForMedication(savedMedication);

      // Reset form
      setMedicationName('');
      setDosage('');
      setFrequency('daily');
      setSelectedDays({
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true
      });
      setSelectedTimes([{ hour: 9, minute: 0 }]);
      setInstructions('');

      // Close modal automatically after saving
      setModalVisible(false);

      // Reset voice mode if active
      if (voiceMode) {
        setVoiceMode(false);
        setRecognizedText('');
      }
    } catch (error) {
      console.error('Error adding medication:', error);
      Alert.alert('Error', 'Failed to add medication');
    } finally {
      setLocalLoading(false);
    }
  };

  // This function is now handled by the MedicationContext

  const handleAddReminder = async () => {
    if (!selectedMedication) {
      Alert.alert('Error', 'Please select a medication');
      return;
    }

    setLocalLoading(true);
    try {
      const newReminder = {
        patientId: user?.uid,
        medicationId: selectedMedication.id,
        medicationName: selectedMedication.name,
        dosage: selectedMedication.dosage,
        instructions: reminderInstructions || selectedMedication.instructions,
        scheduledTime: reminderDate.toISOString(),
        status: 'scheduled',
      };

      // Use the context method to save to Firebase
      await addReminder(newReminder);

      // Reset form and close modal
      setReminderInstructions('');
      setReminderDate(new Date());
      setReminderModalVisible(false);
      setSelectedMedication(null);
    } catch (error) {
      console.error('Error adding reminder:', error);
      Alert.alert('Error', 'Failed to add reminder');
    } finally {
      setLocalLoading(false);
    }
  };

  const handleMedicationPress = (medication) => {
    setSelectedMedication(medication);
    setReminderInstructions(medication.instructions);
    setReminderModalVisible(true);
  };

  // Function to clear all medications
  const handleClearAllMedications = () => {
    Alert.alert(
      "Clear All Medications",
      "Are you sure you want to delete all your medications and reminders? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete Firebase Data",
          style: "destructive",
          onPress: async () => {
            setLocalLoading(true);
            try {
              await clearAllMedicationsForUser();
              // Refresh the medications list
              await fetchMedications();
              Alert.alert("Success", "All medications and reminders have been deleted from Firebase.");
            } catch (error) {
              console.error('Error clearing medications:', error);
              Alert.alert("Error", "Failed to clear medications. Please try again.");
            } finally {
              setLocalLoading(false);
            }
          }
        },
        {
          text: "Delete All Data",
          style: "destructive",
          onPress: async () => {
            setLocalLoading(true);
            try {
              // Clear Firebase data
              await clearAllMedicationsForUser();

              // Clear local storage data
              await localMedicationsService.clearAllMedicationData();

              // Refresh the medications list
              await fetchMedications();

              Alert.alert("Success", "All medications and reminders have been deleted from both Firebase and local storage.");
            } catch (error) {
              console.error('Error clearing all medications data:', error);
              Alert.alert("Error", "Failed to clear all medications data. Please try again.");
            } finally {
              setLocalLoading(false);
            }
          }
        }
      ]
    );
  };

  const onDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || reminderDate;
    setShowDatePicker(false);

    // Keep the time from the current reminderDate
    const newDate = new Date(currentDate);
    newDate.setHours(reminderDate.getHours());
    newDate.setMinutes(reminderDate.getMinutes());

    setReminderDate(newDate);
  };

  const onTimeChange = (event, selectedTime) => {
    const currentTime = selectedTime || reminderDate;
    setShowTimePicker(false);
    setReminderDate(currentTime);
  };

  const renderMedicationItem = ({ item }) => (
    <TouchableOpacity
      style={styles.medicationItem}
      onPress={() => handleMedicationPress(item)}
    >
      <View style={styles.medicationHeader}>
        <Ionicons name="medkit-outline" size={24} color="#4285F4" />
        <View style={styles.medicationInfo}>
          <Text style={styles.medicationName}>{item.name}</Text>
          <Text style={styles.medicationDosage}>{item.dosage}</Text>
        </View>
        <Ionicons name="add-circle" size={24} color="#4CAF50" />
      </View>

      <View style={styles.medicationDetails}>
        <Text style={styles.medicationFrequency}>
          <Text style={styles.detailLabel}>Frequency: </Text>
          {item.frequency}
        </Text>

        {item.instructions && (
          <Text style={styles.medicationInstructions}>
            <Text style={styles.detailLabel}>Instructions: </Text>
            {item.instructions}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Medications</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setModalVisible(true)}
        >
          <Ionicons name="add" size={24} color="#4285F4" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Medications</Text>
            {medications.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleClearAllMedications}
              >
                <Ionicons name="trash-outline" size={18} color="#F44336" />
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
            )}
          </View>

          {loading || localLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#4285F4" />
              <Text style={styles.loadingText}>Loading medications...</Text>
            </View>
          ) : medications.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="medkit-outline" size={48} color="#BDBDBD" />
              <Text style={styles.emptyText}>No medications added yet</Text>
              <TouchableOpacity
                style={styles.emptyButton}
                onPress={() => setModalVisible(true)}
              >
                <Text style={styles.emptyButtonText}>Add Medication</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={medications}
              renderItem={renderMedicationItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
            />
          )}

          {error && (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle" size={24} color="#F44336" />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {useLocalStorage && (
            <View style={styles.infoContainer}>
              <Ionicons name="information" size={24} color="#2196F3" />
              <Text style={styles.infoText}>
                Your medications are currently stored on this device only.
                They will not be available on other devices.
              </Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Medication Reminders</Text>
          <MedicationReminders />
        </View>
      </ScrollView>

      {/* Add Medication Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add New Medication</Text>
              <View style={styles.modalHeaderActions}>
                <TouchableOpacity
                  style={styles.voiceModeButton}
                  onPress={toggleVoiceMode}
                >
                  <Ionicons name="mic-outline" size={24} color="#4285F4" />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>
            </View>

            {voiceMode ? (
              <View style={styles.voiceContainer}>
                {!recognizedText ? (
                  <>
                    <Text style={styles.voiceInstructions}>
                      {isRecording
                        ? "I'm listening... Speak clearly and include medication name, dosage, frequency, and instructions."
                        : "Tap the microphone and say something like:"}
                    </Text>

                    {!isRecording && (
                      <Text style={styles.voiceExample}>
                        "Add Amoxicillin 500mg three times daily with food"
                      </Text>
                    )}

                    <TouchableOpacity
                      style={[styles.recordButton, isRecording && styles.recordingButton]}
                      onPress={isRecording ? stopRecording : startRecording}
                    >
                      <Ionicons
                        name={isRecording ? "stop" : "mic"}
                        size={32}
                        color={isRecording ? "#fff" : "#4285F4"}
                      />
                    </TouchableOpacity>

                    {isRecording && (
                      <Text style={styles.recordingText}>Recording... Tap to stop</Text>
                    )}
                  </>
                ) : processingVoice ? (
                  <View style={styles.processingContainer}>
                    <ActivityIndicator size="large" color="#4285F4" />
                    <Text style={styles.processingText}>Processing your request...</Text>
                  </View>
                ) : (
                  <View style={styles.resultContainer}>
                    <Text style={styles.recognizedTextTitle}>I heard:</Text>
                    <Text style={styles.recognizedText}>{recognizedText}</Text>

                    <View style={styles.medicationSummary}>
                      <Text style={styles.summaryTitle}>Medication Details:</Text>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>Name:</Text>
                        <Text style={styles.summaryValue}>{medicationName || 'Not detected'}</Text>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>Dosage:</Text>
                        <Text style={styles.summaryValue}>{dosage || 'Not detected'}</Text>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>Frequency:</Text>
                        <Text style={styles.summaryValue}>
                          {frequency === 'daily' ? 'Daily' : 'Specific days'}
                        </Text>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>Times:</Text>
                        <Text style={styles.summaryValue}>
                          {selectedTimes.map(time => formatTime(time.hour, time.minute)).join(', ')}
                        </Text>
                      </View>
                      <View style={styles.summaryItem}>
                        <Text style={styles.summaryLabel}>Instructions:</Text>
                        <Text style={styles.summaryValue}>{instructions || 'None'}</Text>
                      </View>
                    </View>

                    <View style={styles.voiceActionButtons}>
                      <TouchableOpacity
                        style={[styles.voiceActionButton, styles.secondaryVoiceButton]}
                        onPress={() => {
                          setRecognizedText('');
                          setVoiceMode(false);
                        }}
                      >
                        <Text style={styles.secondaryVoiceButtonText}>Edit Manually</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.voiceActionButton, styles.primaryVoiceButton]}
                        onPress={handleAddMedication}
                        disabled={localLoading}
                      >
                        {localLoading ? (
                          <View style={styles.loadingButtonContent}>
                            <ActivityIndicator size="small" color="#FFFFFF" />
                            <Text style={styles.primaryVoiceButtonText}>Saving...</Text>
                          </View>
                        ) : (
                          <Text style={styles.primaryVoiceButtonText}>Save Medication</Text>
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>
            ) : (
              <ScrollView style={styles.modalBody}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Medication Name *</Text>
                <TextInput
                  style={styles.formInput}
                  value={medicationName}
                  onChangeText={setMedicationName}
                  placeholder="Enter medication name"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Dosage</Text>
                <TextInput
                  style={styles.formInput}
                  value={dosage}
                  onChangeText={setDosage}
                  placeholder="e.g., 500mg, 2 tablets"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Frequency</Text>
                <View style={styles.frequencyOptions}>
                  <TouchableOpacity
                    style={[styles.frequencyOption, frequency === 'daily' && styles.frequencyOptionSelected]}
                    onPress={() => setFrequency('daily')}
                  >
                    <Text style={[styles.frequencyOptionText, frequency === 'daily' && styles.frequencyOptionTextSelected]}>Daily</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.frequencyOption, frequency === 'specific-days' && styles.frequencyOptionSelected]}
                    onPress={() => setFrequency('specific-days')}
                  >
                    <Text style={[styles.frequencyOptionText, frequency === 'specific-days' && styles.frequencyOptionTextSelected]}>Specific Days</Text>
                  </TouchableOpacity>
                </View>
              </View>

              {frequency === 'specific-days' && (
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Select Days</Text>
                  <View style={styles.daysContainer}>
                    {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(day => (
                      <TouchableOpacity
                        key={day}
                        style={[styles.dayButton, selectedDays[day] && styles.dayButtonSelected]}
                        onPress={() => toggleDay(day)}
                      >
                        <Text style={[styles.dayButtonText, selectedDays[day] && styles.dayButtonTextSelected]}>
                          {day.charAt(0).toUpperCase()}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

              <View style={styles.formGroup}>
                <View style={styles.timeHeaderContainer}>
                  <Text style={styles.formLabel}>Time(s) to Take</Text>
                  <TouchableOpacity
                    style={styles.addTimeButton}
                    onPress={handleAddTimeSlot}
                  >
                    <Ionicons name="add-circle" size={24} color="#4285F4" />
                    <Text style={styles.addTimeText}>Add Time</Text>
                  </TouchableOpacity>
                </View>

                {selectedTimes.map((time, index) => (
                  <View key={index} style={styles.timeSlotContainer}>
                    <TouchableOpacity
                      style={styles.timePickerButton}
                      onPress={() => setShowTimePickerIndex(index)}
                    >
                      <Ionicons name="time-outline" size={20} color="#4285F4" />
                      <Text style={styles.timePickerText}>
                        {formatTime(time.hour, time.minute)}
                      </Text>
                    </TouchableOpacity>

                    {selectedTimes.length > 1 && (
                      <TouchableOpacity
                        style={styles.removeTimeButton}
                        onPress={() => handleRemoveTimeSlot(index)}
                      >
                        <Ionicons name="close-circle" size={24} color="#F44336" />
                      </TouchableOpacity>
                    )}

                    {showTimePickerIndex === index && (
                      <DateTimePicker
                        value={(() => {
                          const date = new Date();
                          date.setHours(time.hour, time.minute, 0);
                          return date;
                        })()}
                        mode="time"
                        display="default"
                        onChange={(event, selectedTime) => handleTimeChange(event, selectedTime, index)}
                      />
                    )}
                  </View>
                ))}
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Instructions</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={instructions}
                  onChangeText={setInstructions}
                  placeholder="e.g., Take with food, Take before bed"
                  multiline
                  numberOfLines={3}
                />
              </View>
              </ScrollView>
            )}

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleAddMedication}
                disabled={localLoading}
              >
                {localLoading ? (
                  <View style={styles.loadingButtonContent}>
                    <ActivityIndicator size="small" color="#FFFFFF" />
                    <Text style={styles.saveButtonText}>Saving...</Text>
                  </View>
                ) : (
                  <Text style={styles.saveButtonText}>Save Medication</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Reminder Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={reminderModalVisible}
        onRequestClose={() => setReminderModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add Medication Reminder</Text>
              <TouchableOpacity onPress={() => setReminderModalVisible(false)}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {selectedMedication && (
              <ScrollView style={styles.modalBody}>
                <View style={styles.selectedMedication}>
                  <Text style={styles.selectedMedicationTitle}>{selectedMedication.name}</Text>
                  <Text style={styles.selectedMedicationDosage}>{selectedMedication.dosage}</Text>
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Date</Text>
                  <TouchableOpacity
                    style={styles.datePickerButton}
                    onPress={() => setShowDatePicker(true)}
                  >
                    <Text style={styles.datePickerText}>
                      {reminderDate.toLocaleDateString()}
                    </Text>
                    <Ionicons name="calendar" size={20} color="#4285F4" />
                  </TouchableOpacity>
                  {showDatePicker && (
                    <DateTimePicker
                      value={reminderDate}
                      mode="date"
                      display="default"
                      onChange={onDateChange}
                    />
                  )}
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Time</Text>
                  <TouchableOpacity
                    style={styles.datePickerButton}
                    onPress={() => setShowTimePicker(true)}
                  >
                    <Text style={styles.datePickerText}>
                      {reminderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                    <Ionicons name="time" size={20} color="#4285F4" />
                  </TouchableOpacity>
                  {showTimePicker && (
                    <DateTimePicker
                      value={reminderDate}
                      mode="time"
                      display="default"
                      onChange={onTimeChange}
                    />
                  )}
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Special Instructions</Text>
                  <TextInput
                    style={[styles.formInput, styles.textArea]}
                    value={reminderInstructions}
                    onChangeText={setReminderInstructions}
                    placeholder="e.g., Take with food, Take before bed"
                    multiline
                    numberOfLines={3}
                  />
                </View>
              </ScrollView>
            )}

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setReminderModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleAddReminder}
              >
                <Text style={styles.saveButtonText}>Set Reminder</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 4,
  },
  clearButtonText: {
    color: '#F44336',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  loadingText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 12,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
    marginTop: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#D32F2F',
    marginLeft: 8,
    flex: 1,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    marginTop: 16,
  },
  infoText: {
    fontSize: 14,
    color: '#0D47A1',
    marginLeft: 8,
    flex: 1,
  },
  // Voice recognition styles
  voiceContainer: {
    padding: 20,
    alignItems: 'center',
  },
  voiceInstructions: {
    fontSize: 16,
    color: '#424242',
    textAlign: 'center',
    marginBottom: 16,
  },
  voiceExample: {
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 24,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recordingButton: {
    backgroundColor: '#EA4335',
  },
  recordingText: {
    fontSize: 14,
    color: '#EA4335',
    marginTop: 8,
  },
  processingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  processingText: {
    fontSize: 16,
    color: '#424242',
    marginTop: 16,
  },
  resultContainer: {
    width: '100%',
    padding: 16,
  },
  recognizedTextTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#424242',
    marginBottom: 8,
  },
  recognizedText: {
    fontSize: 14,
    color: '#757575',
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  medicationSummary: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4285F4',
    marginBottom: 12,
  },
  summaryItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#424242',
    width: 100,
  },
  summaryValue: {
    fontSize: 14,
    color: '#424242',
    flex: 1,
  },
  voiceActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  voiceActionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  primaryVoiceButton: {
    backgroundColor: '#4285F4',
  },
  primaryVoiceButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  secondaryVoiceButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#BDBDBD',
  },
  secondaryVoiceButtonText: {
    color: '#757575',
    fontWeight: 'bold',
    fontSize: 14,
  },
  modalHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  voiceModeButton: {
    marginRight: 16,
  },
  frequencyOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  frequencyOption: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  frequencyOptionSelected: {
    backgroundColor: '#E3F2FD',
    borderColor: '#4285F4',
  },
  frequencyOptionText: {
    color: '#757575',
    fontWeight: '500',
  },
  frequencyOptionTextSelected: {
    color: '#4285F4',
    fontWeight: 'bold',
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  dayButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dayButtonSelected: {
    backgroundColor: '#4285F4',
    borderColor: '#4285F4',
  },
  dayButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#757575',
  },
  dayButtonTextSelected: {
    color: '#FFFFFF',
  },
  timeHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addTimeText: {
    color: '#4285F4',
    marginLeft: 4,
    fontWeight: '500',
  },
  timeSlotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timePickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    padding: 12,
  },
  timePickerText: {
    fontSize: 16,
    color: '#212121',
    marginLeft: 8,
  },
  removeTimeButton: {
    marginLeft: 8,
    padding: 4,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212121',
  },
  backButton: {
    padding: 8,
  },
  addButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
    marginBottom: 16,
  },
  emptyButton: {
    backgroundColor: '#4285F4',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  medicationItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  medicationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  medicationInfo: {
    flex: 1,
    marginLeft: 12,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  medicationDosage: {
    fontSize: 14,
    color: '#616161',
  },
  medicationDetails: {
    marginTop: 8,
  },
  medicationFrequency: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 4,
  },
  medicationInstructions: {
    fontSize: 14,
    color: '#616161',
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#424242',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  modalBody: {
    padding: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#424242',
    marginBottom: 8,
  },
  formInput: {
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
    color: '#212121',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  modalButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 4,
    marginLeft: 8,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
  },
  cancelButtonText: {
    color: '#757575',
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#4285F4',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  selectedMedication: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 4,
    marginBottom: 16,
  },
  selectedMedicationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  selectedMedicationDosage: {
    fontSize: 14,
    color: '#1976d2',
    marginTop: 4,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    padding: 12,
  },
  datePickerText: {
    fontSize: 16,
    color: '#212121',
  },
});

export default Medications;
