# OSX
.DS_Store

# Expo
.expo/
dist/
web-build/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*

# Node
node_modules/
npm-debug.*
yarn-debug.*
yarn-error.*

# Environment variables


# IDE
.idea/
.vscode/
*.swp
*.swo

# TypeScript
*.tsbuildinfo

# Testing
coverage/

# Temporary files
*.log
*.tmp
*.temp

# Build
build/
*.bundle
*.bundle.map

# Dependencies
.pnp/
.pnp.js

# Debug
.debug/

# Expo
.expo-shared/

# Metro
.metro-health-check*

# Local Netlify folder
.netlify
