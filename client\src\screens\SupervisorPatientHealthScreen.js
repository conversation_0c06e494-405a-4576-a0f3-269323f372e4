import { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Avatar, Searchbar } from 'react-native-paper';
import { ROLE_COLORS } from '../config/theme';
import { useAuth } from '../contexts/AuthContext';
import { collection, doc, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { showMessage } from 'react-native-flash-message';
import PatientVitalsViewer from '../components/dashboards/doctor/PatientVitalsViewer';
import PatientSymptomsViewer from '../components/dashboards/doctor/PatientSymptomsViewer';
import PatientMedicationsViewer from '../components/dashboards/doctor/PatientMedicationsViewer';

const SupervisorPatientHealthScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const supervisorColors = ROLE_COLORS.supervisor;

  // State variables
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [activeTab, setActiveTab] = useState('vitals');
  const [searchQuery, setSearchQuery] = useState('');

  // Load patients when component mounts
  useEffect(() => {
    loadLinkedPatients();
  }, []);

  // Function to load linked patients
  const loadLinkedPatients = async () => {
    try {
      setLoading(true);

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', user.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        setPatients([]);
        setLoading(false);
        return;
      }

      // Get user documents for all linked patients
      const usersCollection = collection(db, 'users');
      const patientDocs = [];

      for (const patientId of linkedPatientIds) {
        const patientDocRef = doc(usersCollection, patientId);
        const patientDocSnap = await getDoc(patientDocRef);

        if (patientDocSnap.exists()) {
          patientDocs.push({
            uid: patientDocSnap.id,
            ...patientDocSnap.data()
          });
        }
      }

      setPatients(patientDocs);

      // Select the first patient by default if available
      if (patientDocs.length > 0 && !selectedPatient) {
        setSelectedPatient(patientDocs[0]);
      }
    } catch (error) {
      console.error('Error loading linked patients:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load patients. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Function to handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    loadLinkedPatients();
  };

  // Function to handle patient selection
  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    setActiveTab('vitals'); // Reset to vitals tab when selecting a new patient
  };

  // Function to handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Function to handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName || ''} ${patient.lastName || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Render a patient card
  const renderPatientItem = ({ item }) => (
    <TouchableOpacity
      key={item.uid}
      style={[
        styles.patientCard,
        selectedPatient?.uid === item.uid && styles.selectedPatientCard
      ]}
      onPress={() => handlePatientSelect(item)}
      activeOpacity={0.7}
    >
      <View style={[
        styles.patientAvatarContainer,
        selectedPatient?.uid === item.uid && styles.selectedAvatarContainer
      ]}>
        <Avatar.Text
          size={64}
          label={`${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`}
          backgroundColor={selectedPatient?.uid === item.uid ? '#4CAF50' : '#f0f0f0'}
          color={selectedPatient?.uid === item.uid ? '#fff' : '#555'}
          labelStyle={{ fontSize: 24, fontWeight: 'bold' }}
          style={styles.patientAvatar}
        />
      </View>
      <Text style={[
        styles.patientName,
        selectedPatient?.uid === item.uid && styles.selectedPatientName
      ]}>
        {`${item.firstName || ''} ${item.lastName || ''}`}
      </Text>
      <Text style={styles.patientYear}>
        {new Date().getFullYear()}
      </Text>
    </TouchableOpacity>
  );

  // Render empty patients list
  const renderEmptyPatientsList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No patients found</Text>
      <Text style={styles.emptySubtext}>Add patients to your account first</Text>
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: supervisorColors.primary }]}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.addButtonText}>Go Back</Text>
      </TouchableOpacity>
    </View>
  );

  // Render health data content based on active tab
  const renderHealthContent = () => {
    if (!selectedPatient) {
      return (
        <View style={styles.noPatientSelectedContainer}>
          <Ionicons name="person" size={60} color="#ccc" />
          <Text style={styles.noPatientSelectedText}>Select a patient to view health data</Text>
        </View>
      );
    }

    switch (activeTab) {
      case 'vitals':
        return (
          <PatientVitalsViewer
            patientId={selectedPatient.uid}
            patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
          />
        );
      case 'symptoms':
        return (
          <PatientSymptomsViewer
            patientId={selectedPatient.uid}
            patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
          />
        );
      case 'medications':
        return (
          <PatientMedicationsViewer
            patientId={selectedPatient.uid}
            patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Patient Health Monitoring</Text>
        <Searchbar
          placeholder="Search patients..."
          onChangeText={handleSearch}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={supervisorColors.primary}
          theme={{ colors: { primary: supervisorColors.primary } }}
          elevation={1}
        />
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={supervisorColors.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.mainScrollView}
          contentContainerStyle={styles.scrollViewContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[supervisorColors.primary]}
            />
          }
        >
          <View style={styles.content}>
            <View style={styles.patientsSection}>
              <Text style={styles.sectionTitle}>Your Patients</Text>
              {filteredPatients.length > 0 ? (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.patientList}
                >
                  {filteredPatients.map((item) => renderPatientItem({item}))}
                </ScrollView>
              ) : (
                renderEmptyPatientsList()
              )}
            </View>

            {selectedPatient && (
              <View style={styles.tabsContainer}>
                <TouchableOpacity
                  style={[
                    styles.tabButton,
                    activeTab === 'vitals' && styles.activeTabButton
                  ]}
                  onPress={() => handleTabChange('vitals')}
                >
                  <Ionicons
                    name="pulse"
                    size={20}
                    color={activeTab === 'vitals' ? '#4CAF50' : '#666'}
                  />
                  <Text
                    style={[
                      styles.tabButtonText,
                      activeTab === 'vitals' && styles.activeTabButtonText
                    ]}
                  >
                    Vital Signs
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.tabButton,
                    activeTab === 'symptoms' && styles.activeTabButton
                  ]}
                  onPress={() => handleTabChange('symptoms')}
                >
                  <Ionicons
                    name="document-text"
                    size={20}
                    color={activeTab === 'symptoms' ? '#4CAF50' : '#666'}
                  />
                  <Text
                    style={[
                      styles.tabButtonText,
                      activeTab === 'symptoms' && styles.activeTabButtonText
                    ]}
                  >
                    Symptoms
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.tabButton,
                    activeTab === 'medications' && styles.activeTabButton
                  ]}
                  onPress={() => handleTabChange('medications')}
                >
                  <Ionicons
                    name="medkit"
                    size={20}
                    color={activeTab === 'medications' ? '#4CAF50' : '#666'}
                  />
                  <Text
                    style={[
                      styles.tabButtonText,
                      activeTab === 'medications' && styles.activeTabButtonText
                    ]}
                  >
                    Medications
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.healthContentContainer}>
              {selectedPatient ? (
                <>
                  <View style={styles.healthContentHeader}>
                    <Text style={styles.healthContentTitle}>
                      {`${selectedPatient.firstName}'s ${activeTab === 'vitals' ? 'Vitals' : activeTab === 'symptoms' ? 'Symptoms' : 'Medications'}`}
                    </Text>
                    <Text style={styles.healthContentSubtitle}>
                      Monitor your patient's health metrics
                    </Text>
                  </View>
                  {renderHealthContent()}
                </>
              ) : (
                renderHealthContent()
              )}
            </View>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mainScrollView: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  searchBar: {
    elevation: 0,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    height: 46,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchInput: {
    fontSize: 15,
  },
  content: {
    padding: 16,
  },
  patientsSection: {
    marginBottom: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  patientList: {
    paddingVertical: 10,
    paddingHorizontal: 4,
  },
  patientCard: {
    alignItems: 'center',
    marginRight: 16,
    padding: 12,
    borderRadius: 16,
    backgroundColor: '#fff',
    borderWidth: 0,
    width: 110,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 5,
    elevation: 4,
  },
  selectedPatientCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 0,
  },
  patientAvatarContainer: {
    marginBottom: 8,
    padding: 2,
  },
  selectedAvatarContainer: {
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
  },
  patientAvatar: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    borderWidth: 0,
  },
  patientName: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    color: '#333',
  },
  selectedPatientName: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  patientYear: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 2,
  },
  activeTabButton: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  tabButtonText: {
    marginLeft: 6,
    fontSize: 14,
    color: '#666',
  },
  activeTabButtonText: {
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  healthContentContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    minHeight: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 20,
  },
  healthContentHeader: {
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 12,
  },
  healthContentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 4,
  },
  healthContentSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  addButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  noPatientSelectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  noPatientSelectedText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default SupervisorPatientHealthScreen;
