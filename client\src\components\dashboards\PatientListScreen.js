import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '../../contexts/AuthContext';
import { usersAPI } from '../../config/api';
import DoctorAddPatientButton from './doctor/AddPatientButton';
import SupervisorAddPatientButton from './supervisor/AddPatientButton';

const PatientListScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { action } = route.params || {};
  const { user } = useAuth();
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchPatients();
  }, []);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const response = await usersAPI.getLinkedUsers('patients');
      setPatients(response || []);
    } catch (error) {
      console.error('Error fetching patients:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load patients. Please try again.',
        type: 'danger',
        backgroundColor: 'rgba(16, 107, 0, 1)',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchPatients();
  };

  const handlePatientPress = (patient) => {
    // If action is assignCaregiver, navigate to the assign caregiver screen
    if (action === 'assignCaregiver' && user.role === 'supervisor') {
      navigation.navigate('SupervisorAssignCaregiverScreen', { patient });
      return;
    }

    // Default behavior - just show a message
    showMessage({
      message: 'Patient Selected',
      description: `You selected ${patient.displayName}`,
      type: 'info',
      backgroundColor: 'rgba(16, 107, 0, 1)',
    });
  };

  const renderPatientItem = ({ item }) => (
    <TouchableOpacity
      style={styles.patientItem}
      onPress={() => handlePatientPress(item)}
    >
      <View style={styles.avatarContainer}>
        <Text style={styles.avatarText}>
          {item.displayName.charAt(0).toUpperCase()}
        </Text>
      </View>
      <View style={styles.patientInfo}>
        <Text style={styles.patientName}>{item.displayName}</Text>
        <Text style={styles.patientRole}>{item.role.charAt(0).toUpperCase() + item.role.slice(1)}</Text>
      </View>
      <Ionicons name="chevron-forward" size={24} color="#ccc" />
    </TouchableOpacity>
  );

  // Render the appropriate add patient button based on user role
  const renderAddPatientButton = (onPatientAdded) => {
    if (user.role === 'supervisor') {
      return <SupervisorAddPatientButton onPatientAdded={onPatientAdded} />;
    } else {
      return <DoctorAddPatientButton onPatientAdded={onPatientAdded} />;
    }
  };

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" style={styles.emptyIcon} />
      <Text style={styles.emptyText}>No patients connected yet</Text>
      <Text style={styles.emptySubtext}>
        Add patients by scanning their QR code or entering their unique code
      </Text>
      <View style={styles.addButtonContainer}>
        {renderAddPatientButton(() => fetchPatients())}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={patients}
        keyExtractor={(item) => item.uid}
        renderItem={renderPatientItem}
        ListEmptyComponent={!loading && renderEmptyList}
        contentContainerStyle={patients.length === 0 ? { flex: 1 } : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={['rgba(16, 107, 0, 1)']} />
        }
      />

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="rgba(16, 107, 0, 1)" />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      )}

      {patients.length > 0 && (
        <View style={styles.floatingButtonContainer}>
          {renderAddPatientButton((patient) => {
            setPatients(prev => [...prev, patient]);
            showMessage({
              message: 'Success',
              description: `${patient.displayName} has been added to your patients`,
              type: 'success',
              backgroundColor: 'rgba(16, 107, 0, 1)',
            });
          })}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  loadingText: {
    marginTop: 10,
    color: '#555',
    fontSize: 16,
  },
  patientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 1,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(16, 107, 0, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'rgba(16, 107, 0, 1)',
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  patientRole: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyIcon: {
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#555',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#777',
    textAlign: 'center',
    marginBottom: 30,
  },
  addButtonContainer: {
    marginTop: 20,
  },
  floatingButtonContainer: {
    position: 'absolute',
    right: 20,
    bottom: 20,
  },
});

export default PatientListScreen;