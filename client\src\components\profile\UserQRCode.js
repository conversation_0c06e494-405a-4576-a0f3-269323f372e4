import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Share,
  Dimensions,
  ScrollView,
  Alert
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { Card, Button, Avatar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import QRCode from 'react-native-qrcode-svg';
import { COLORS } from '../../config/theme';

const { width } = Dimensions.get('window');
const CODE_SIZE = width * 0.8;
const QR_SIZE = width * 0.6;

const UserQRCode = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [qrError, setQrError] = useState(false);

  useEffect(() => {
    // Validate if user code exists and has correct format
    if (!user?.userCode || !/^[A-Z0-9]{8}$/.test(user.userCode)) {
      setQrError(true);
    } else {
      setQrError(false);
    }
  }, [user]);

  if (qrError) {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <Avatar.Icon 
              size={60} 
              icon="qrcode" 
              style={styles.errorIcon} 
              color="#fff" 
            />
            <Text style={styles.errorText}>Code Not Available</Text>
            <Text style={styles.errorSubtext}>
              Your unique code hasn't been generated yet or is invalid. Please contact support.
            </Text>
            <Button 
              mode="contained" 
              style={{ backgroundColor: COLORS.primary, marginTop: 20 }}
              onPress={() => Alert.alert(
                'Contact Support', 
                'Please contact our support team for assistance with your user code.',
                [{ text: 'OK' }]
              )}
            >
              Contact Support
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  const handleShareCode = async () => {
    try {
      setLoading(true);
      await Share.share({
        message: `My NeuroCare code: ${user.userCode}`,
      });
    } catch (error) {
      console.error('Error sharing code:', error);
      Alert.alert('Error', 'Could not share your code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.contentContainer}>
        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <View style={styles.qrContainer}>
              {user?.userCode ? (
                <QRCode
                  value={user.userCode}
                  size={QR_SIZE}
                  color="rgba(16, 107, 0, 1)"
                  backgroundColor="white"
                  ecl="M"
                  onError={(e) => {
                    console.error('QR Code Error:', e);
                    setQrError(true);
                  }}
                />
              ) : (
                <View style={styles.placeholderQR}>
                  <Ionicons name="qr-code-outline" size={QR_SIZE * 0.6} color="#ccc" />
                </View>
              )}
            </View>
            
            <View style={styles.codeContainer}>
              <Text style={styles.codeLabel}>Your Unique Code</Text>
              <Text style={styles.codeText}>{user.userCode}</Text>
              <Text style={styles.codeDescription}>
                Share this code with healthcare providers to connect with them
              </Text>
            </View>
            
            <TouchableOpacity 
              style={styles.shareButton} 
              onPress={handleShareCode}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <>
                  <Ionicons name="share-outline" size={20} color="#fff" />
                  <Text style={styles.shareButtonText}>Share Code</Text>
                </>
              )}
            </TouchableOpacity>
          </Card.Content>
        </Card>
        
        <Card style={[styles.card, styles.infoCard]}>
          <Card.Content>
            <Text style={styles.infoTitle}>How to use your code</Text>
            <View style={styles.infoItem}>
              <Ionicons name="medical-outline" size={24} color="rgba(16, 107, 0, 1)" style={styles.infoIcon} />
              <Text style={styles.infoText}>Healthcare providers can use this code to connect with you</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="scan-outline" size={24} color="rgba(16, 107, 0, 1)" style={styles.infoIcon} />
              <Text style={styles.infoText}>They can scan this QR code or enter the code manually</Text>
            </View>
            <View style={styles.infoItem}>
              <Ionicons name="lock-closed-outline" size={24} color="rgba(16, 107, 0, 1)" style={styles.infoIcon} />
              <Text style={styles.infoText}>Only share with healthcare providers you trust</Text>
            </View>
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    padding: 16,
    alignItems: 'center',
  },
  card: {
    width: '100%',
    marginBottom: 16,
    elevation: 4,
    borderRadius: 12,
  },
  cardContent: {
    alignItems: 'center',
    padding: 16,
  },
  qrContainer: {
    padding: 24,
    backgroundColor: 'white',
    borderRadius: 12,
    elevation: 2,
    marginVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  codeContainer: {
    alignItems: 'center',
    marginBottom: 24,
    width: '100%',
  },
  codeLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  codeText: {
    fontSize: 28,
    fontWeight: 'bold',
    letterSpacing: 2,
    color: 'rgba(16, 107, 0, 1)',
    marginBottom: 8,
    textAlign: 'center',
  },
  codeDescription: {
    fontSize: 14,
    color: '#777',
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    elevation: 2,
  },
  shareButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  infoCard: {
    backgroundColor: '#fff',
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: 'rgba(16, 107, 0, 0.8)',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIcon: {
    marginRight: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  errorIcon: {
    backgroundColor: COLORS.primary,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#555',
  },
  errorSubtext: {
    fontSize: 14,
    color: '#777',
    textAlign: 'center',
  },
  placeholderQR: {
    padding: 24,
    backgroundColor: 'white',
    borderRadius: 12,
    elevation: 2,
    marginVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default UserQRCode;