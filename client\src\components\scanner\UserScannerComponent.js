import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');
const SCANNER_SIZE = width * 0.7;

// Simplified version that doesn't rely on barcode scanner
const UserScannerComponent = ({ onCodeScanned, scannerTitle = "Enter User Code" }) => {
  const [manualCode, setManualCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    if (!manualCode) return;
    
    setLoading(true);
    
    // Pass the entered code to parent component
    if (onCodeScanned) {
      onCodeScanned(manualCode);
    }
    
    // Reset after a delay
    setTimeout(() => {
      setLoading(false);
      setManualCode('');
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{scannerTitle}</Text>
      
      <View style={styles.manualContainer}>
        <Ionicons name="person-add" size={60} color="rgba(16, 107, 0, 1)" style={styles.icon} />
        <Text style={styles.text}>
          Enter the 8-character code provided by the user
        </Text>
        
        <TextInput
          style={styles.codeInput}
          value={manualCode}
          onChangeText={setManualCode}
          placeholder="Enter code (e.g. ABC12345)"
          placeholderTextColor="#999"
          autoCapitalize="characters"
          maxLength={8}
        />
        
        <TouchableOpacity 
          style={[styles.button, !manualCode && styles.buttonDisabled]}
          onPress={handleSubmit}
          disabled={!manualCode || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Connect User</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 40,
    color: 'rgba(16, 107, 0, 1)',
    textAlign: 'center',
  },
  manualContainer: {
    width: '100%',
    maxWidth: 350,
    alignItems: 'center',
    padding: 20,
    borderRadius: 12,
    backgroundColor: '#f9f9f9',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  text: {
    fontSize: 16,
    color: '#555',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  codeInput: {
    width: '100%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    fontSize: 18,
    textAlign: 'center',
    letterSpacing: 2,
    marginBottom: 30,
  },
  icon: {
    marginBottom: 10,
  },
  button: {
    backgroundColor: 'rgba(16, 107, 0, 1)',
    width: '100%',
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default UserScannerComponent; 