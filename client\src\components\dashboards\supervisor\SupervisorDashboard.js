import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ScrollView, RefreshControl } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { getThemeForRole, ROLE_COLORS } from '../../../config/theme';
import { useNavigation } from '@react-navigation/native';
import { Card, Avatar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../contexts/AuthContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import DashboardChart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import AddPatientButton from './AddPatientButton';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../../config/firebase';
import { showMessage } from 'react-native-flash-message';

// Define default menu items for the supervisor dashboard


const SupervisorDashboard = ({
  notifications = []
}) => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const theme = getThemeForRole('supervisor');
  const supervisorColors = ROLE_COLORS.supervisor; // Using supervisor-specific colors
  const [selectedSection, setSelectedSection] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // State for dashboard data
  const [statsData, setStatsData] = useState({
    staff: 0,
    patients: 0,
    doctors: 0,
    caregivers: 0,
    activities: [0, 0, 0, 0, 0, 0, 0]
  });

  const [activityData, setActivityData] = useState([]);

  const menuItems = [
  { label: 'Dashboard', icon: 'home', screen: 'Dashboard' },
  { label: 'Staff', icon: 'people', screen: 'Patients' },
  { label: 'Patients', icon: 'medkit', screen: 'SupervisorPatientHealth' },
  { label: 'Caregivers', icon: 'people-circle', screen: 'SupervisorCaregiverManagement' },
  { label: 'Appointments', icon: 'calendar', screen: 'SupervisorAppointments' },
  { label: 'Analytics', icon: 'stats-chart', screen: 'HealthRecords' },
  { label: 'Reports', icon: 'document', screen: 'HealthRecords' },
  { label: 'Messages', icon: 'chatbubbles', screen: 'Chatroom' },
  { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
  { label: 'Profile', icon: 'person', screen: 'Profile' },
  { label: 'Settings', icon: 'settings', screen: 'Settings' },
];

  // Function to fetch dashboard data from Firebase
  const fetchDashboardData = async () => {
    try {
      // Fetch users data to count staff, patients, doctors, and caregivers
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      // Count users by role
      let staffCount = 0;
      let patientCount = 0;
      let doctorCount = 0;
      let caregiverCount = 0;

      usersSnapshot.docs.forEach(doc => {
        const userData = doc.data();
        const role = userData.role?.toLowerCase();

        if (role === 'patient') {
          patientCount++;
        } else if (role === 'doctor') {
          doctorCount++;
          staffCount++;
        } else if (role === 'caregiver' || role === 'nurse') {
          caregiverCount++;
          staffCount++;
        } else if (role && role !== 'patient') {
          // Count any other non-patient role as staff
          staffCount++;
        }
      });

      // Generate random activity data for now (this would be replaced with real data)
      const activityValues = Array(7).fill(0).map(() => Math.floor(Math.random() * 30) + 10);

      // Update stats data
      setStatsData({
        staff: staffCount,
        patients: patientCount,
        doctors: doctorCount,
        caregivers: caregiverCount,
        activities: activityValues
      });

      // Try to fetch activities from Firebase if available
      try {
        const activitiesCollection = collection(db, 'activities');
        const activitiesSnapshot = await getDocs(activitiesCollection);

        if (!activitiesSnapshot.empty) {
          const activities = activitiesSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              name: data.name || 'Activity',
              time: data.time || 'No time specified',
              status: data.status || 'upcoming'
            };
          });

          setActivityData(activities);
        } else {
          // If no activities found, set empty array
          setActivityData([]);
        }
      } catch (error) {
        console.log('No activities collection available:', error);
        setActivityData([]);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Function to handle patient added
  const handlePatientAdded = (patient) => {
    showMessage({
      message: 'Success',
      description: `Patient ${patient.displayName} added successfully`,
      type: 'success',
      backgroundColor: supervisorColors.primary,
    });

    // Refresh dashboard data
    fetchDashboardData();
  };

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  return (
    <DashboardLayout
      title="Supervisor Dashboard"
      menuItems={menuItems}
      userRole="supervisor"
      notifications={notifications}
    >
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[supervisorColors.primary]} />
        }
      >
        <View style={styles.headerSection}>
          <LinearGradient
            colors={[supervisorColors.primaryLight, '#f5f5f5']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <View style={styles.welcomeContainer}>
                <Text style={styles.welcomeText}>Welcome, {user?.firstName || 'Supervisor'}</Text>
                <Text style={styles.dateText}>{new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</Text>
              </View>
              <View style={styles.headerButtonContainer}>
                <AddPatientButton onPatientAdded={handlePatientAdded} headerStyle={true} />
              </View>
            </View>
          </LinearGradient>
        </View>

        <View style={styles.contentSection}>

        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Facility Overview</Text>
        </View>

        <View style={styles.statsContainer}>
          <DashboardCard
            title="Total Staff"
            value={statsData.staff.toString()}
            icon="people"
            color="#4A148C"
            gradientStart="#E1BEE7"
            gradientEnd="#ffffff"
            width="48%"
          />
          <DashboardCard
            title="Total Patients"
            value={statsData.patients.toString()}
            icon="people-outline"
            color="#4A148C"
            gradientStart="#E1BEE7"
            gradientEnd="#ffffff"
            width="48%"
          />
          <DashboardCard
            title="Doctors"
            value={statsData.doctors.toString()}
            icon="medical"
            color="#4A148C"
            gradientStart="#E1BEE7"
            gradientEnd="#ffffff"
            width="48%"
          />
          <DashboardCard
            title="Caregivers"
            value={statsData.caregivers.toString()}
            icon="heart"
            color="#4A148C"
            gradientStart="#E1BEE7"
            gradientEnd="#ffffff"
            width="48%"
          />
        </View>

        <Card style={styles.chartCard}>
          <Card.Title
            title="Facility Activity"
            subtitle="Last 7 Days"
            titleStyle={styles.chartTitle}
            subtitleStyle={styles.chartSubtitle}
          />
          <Card.Content style={styles.chartContent}>
            <DashboardChart
              data={statsData.activities}
              labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
              color="#4A148C"
              type="line"
              height={240}
              backgroundColor="#ffffff"
            />
          </Card.Content>
        </Card>

        <View style={styles.reportContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Profile')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="document-text" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>Generate Reports</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Profile')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="calendar" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>Schedule Meeting</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Notifications')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="notifications" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>Send Announcements</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('SupervisorPatientHealth')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="pulse" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>Patient Health</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('SupervisorPatientTracking')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="navigate" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>Track & Guide Patients</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('UserQRCode')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="qr-code" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>My QR Code</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('SupervisorCaregiverManagement')}
              activeOpacity={0.7}
            >
              <View style={[styles.actionIconContainer, { backgroundColor: "#E1BEE7" }]}>
                <Ionicons name="people-circle" size={24} color="#4A148C" />
              </View>
              <Text style={styles.actionText}>Manage Caregivers</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.upcomingSection}>
          <Text style={styles.sectionTitle}>Today's Schedule</Text>
          <UpcomingList
            data={activityData}
            type="activities"
            emptyText="No activities scheduled for today"
            backgroundColor="#ffffff"
            maxItems={3}
            style={styles.scheduleList}
          />
        </View>

        <View style={styles.spacer} />
        </View>
      </ScrollView>
    </DashboardLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  headerSection: {
    width: '100%',
    marginBottom: 20,
  },
  headerGradient: {
    paddingHorizontal: 15,
    paddingVertical: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  headerButtonContainer: {
    marginLeft: 5,
  },
  contentSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  welcomeContainer: {
    flex: 1,
    marginRight: 5,
  },
  welcomeText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  dateText: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#343a40',
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
    marginBottom: 16,
  },
  chartCard: {
    marginBottom: 24,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  chartSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  chartContent: {
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  reportContainer: {
    padding: 16,
    marginBottom: 16,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
  },
  actionIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    marginTop: 8,
    color: '#333',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'center',
  },
  upcomingSection: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  scheduleList: {
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
  },
  spacer: {
    height: 30,
  },
});

export default SupervisorDashboard;