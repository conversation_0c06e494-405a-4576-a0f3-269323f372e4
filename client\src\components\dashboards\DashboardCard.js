import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const DashboardCard = ({
  title,
  value,
  icon,
  iconColor = '#4285F4',
  backgroundColor = '#fff',
  onPress,
  width = '100%',
  height,
  subtitle,
  trend,
  trendValue,
  unit,
  status,
  gradientStart = '#f5f5f5',
  gradientEnd = '#e0e0e0',
}) => {
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const renderTrend = () => {
    if (!trend) return null;

    const isPositive = trend === 'up';
    const trendIcon = isPositive ? 'arrow-up' : 'arrow-down';
    const trendColor = isPositive ? '#4CAF50' : '#F44336';

    return (
      <View style={styles.trendContainer}>
        <Ionicons name={trendIcon} size={16} color={trendColor} />
        <Text style={[styles.trendValue, { color: trendColor }]}>
          {trendValue || ''}
        </Text>
      </View>
    );
  };

  const getStatusInfo = () => {
    if (!status) return null;

    let statusColor, statusIcon, statusText;

    switch(status) {
      case 'high':
        statusColor = '#F44336';
        statusIcon = 'arrow-up';
        statusText = 'High';
        break;
      case 'low':
        statusColor = '#FFC107';
        statusIcon = 'arrow-down';
        statusText = 'Low';
        break;
      case 'normal':
      default:
        statusColor = '#4CAF50';
        statusIcon = 'checkmark-circle';
        statusText = 'Normal';
        break;
    }

    return { statusColor, statusIcon, statusText };
  };

  // Handle press animation
  const handlePressIn = () => {
    if (onPress) {
      Animated.spring(scaleAnim, {
        toValue: 0.97,
        friction: 5,
        tension: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (onPress) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  return (
    <Animated.View
      style={[
        styles.cardWrapper,
        {
          width: width,
          transform: [{ scale: scaleAnim }],
        }
      ]}
    >
      <TouchableOpacity
        style={[
          styles.card,
          {
            width: '100%',
            backgroundColor: '#ffffff',
            borderLeftColor: iconColor,
          }
        ]}
        onPress={onPress}
        disabled={!onPress}
        activeOpacity={0.9}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <LinearGradient
          colors={[gradientStart + '40', '#ffffff']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.cardGradient}
        >
          <View style={styles.cardContent}>
            <View
              style={[
                styles.iconContainer,
                {
                  backgroundColor: gradientStart
                }
              ]}
            >
              <Ionicons name={icon} size={28} color={iconColor} />
            </View>
            <View style={styles.cardTextContainer}>
              <Text style={styles.cardTitle}>{title}</Text>
              <View style={styles.valueContainer}>
                <Text style={styles.cardValue}>{value}</Text>
                {unit && <Text style={styles.unitText}>{unit}</Text>}
              </View>
              {status && (
                <View style={[styles.statusContainer, { backgroundColor: getStatusInfo().statusColor + '20' }]}>
                  <Ionicons name={getStatusInfo().statusIcon} size={12} color={getStatusInfo().statusColor} />
                  <Text style={[styles.statusText, { color: getStatusInfo().statusColor }]}>
                    {getStatusInfo().statusText}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  cardWrapper: {
    marginBottom: 16,
  },
  card: {
    borderRadius: 16,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderLeftWidth: 4,
    overflow: 'hidden',
    backgroundColor: '#ffffff',
    // Add box shadow
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  cardGradient: {
    width: '100%',
    padding: 16,
  },
  cardContent: {
    flexDirection: 'column',
    alignItems: 'center',
    height: 150,
    justifyContent: 'center',
    paddingVertical: 10,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    // Add subtle inner border
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  cardTextContainer: {
    width: '100%',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 15,
    color: '#616161',
    marginBottom: 8,
    textAlign: 'center',
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  cardValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#212121',
    flexShrink: 1,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  trendValue: {
    fontSize: 12,
    marginLeft: 2,
    fontWeight: 'bold',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  unitText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'center',
    // Add subtle border
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  statusText: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: 'bold',
    letterSpacing: 0.2,
  },
});

export default DashboardCard;