import { DefaultTheme } from 'react-native-paper';

// Define role-specific colors
export const ROLE_COLORS = {
  patient: {
    primary: 'rgba(255, 149, 43, 1)', // Orange for patients
    primaryLight: 'rgba(255, 149, 43, 0.8)',
    primaryLighter: 'rgba(255, 149, 43, 0.1)'
  },
  doctor: {
    primary: 'rgba(170, 86, 255, 1)', // Purple for doctors
    primaryLight: 'rgba(170, 86, 255, 0.8)',
    primaryLighter: 'rgba(170, 86, 255, 0.1)'
  },
  supervisor: {
    primary: 'rgb(255, 0, 242)', // Bright magenta/pink for supervisors
    primaryLight: 'rgba(255, 0, 242, 0.8)',
    primaryLighter: 'rgba(255, 0, 242, 0.1)'
  },
  caregiver: {
    primary: 'rgba(0, 169, 255, 1)', // Blue for caregivers
    primaryLight: 'rgba(0, 169, 255, 0.8)',
    primaryLighter: 'rgba(0, 169, 255, 0.1)'
  },
  admin: {
    primary: 'rgba(16, 107, 0, 1)', // Green for admin
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)'
  },
  default: {
    primary: 'rgba(16, 107, 0, 1)', // Default green
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)'
  }
};

// Export patient colors for direct access
export const PATIENT_COLORS = ROLE_COLORS.patient;

export const COLORS = {
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFA726',
  info: '#29B6F6',
  textDark: '#212121',
  textMedium: '#757575',
  textLight: '#BDBDBD',
  background: '#f5f7fa',
  surface: '#FFFFFF',
  border: '#E0E0E0',
};

export const getThemeForRole = (role = 'default') => {
  // Get role-specific colors or default if role doesn't exist
  const roleColors = ROLE_COLORS[role] || ROLE_COLORS.default;
  const primaryColor = roleColors.primary;

  // Base theme applied to the entire application
  const baseTheme = {
    ...DefaultTheme,
    roundness: 8,
    colors: {
      ...DefaultTheme.colors,
      primary: roleColors.primary,
      primaryLight: roleColors.primaryLight,
      primaryLighter: roleColors.primaryLighter,
      accent: COLORS.success,
      background: COLORS.background,
      surface: COLORS.surface,
      error: COLORS.error,
      text: COLORS.textDark,
      placeholder: COLORS.textLight,
      backdrop: 'rgba(0, 0, 0, 0.5)',
      notification: COLORS.info,
    },
  };

  return baseTheme;
};

export default getThemeForRole;