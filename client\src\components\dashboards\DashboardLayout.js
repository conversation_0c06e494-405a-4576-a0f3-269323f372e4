import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Dimensions,
  Animated,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../contexts/AuthContext';
import { StatusBar } from 'expo-status-bar';
import { COLORS } from '../../config/theme';
import { getThemeForRole } from '../../config/theme';
import NotificationBadge from '../notifications/NotificationBadge';
import VoiceCommandModal from '../voice/VoiceCommandModal';

const { width } = Dimensions.get('window');
const SIDEBAR_WIDTH = width * 0.75;

const DashboardLayout = ({
  children,
  title,
  roleName,
  menuItems = [],
  userRole = 'user',
  notifications = [],
  enableVoiceCommands = userRole === 'patient' // Only enable for patient by default
}) => {
  const navigation = useNavigation();
  const { logout, user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [animatedValue] = useState(new Animated.Value(0));
  const [voiceModalVisible, setVoiceModalVisible] = useState(false);

  // Get theme based on user role
  const theme = getThemeForRole(userRole);
  const primaryColor = theme.colors.primary;

  const toggleSidebar = () => {
    const toValue = sidebarOpen ? 0 : 1;
    Animated.timing(animatedValue, {
      toValue,
      duration: 300,
      useNativeDriver: true,
    }).start();
    setSidebarOpen(!sidebarOpen);
  };

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-SIDEBAR_WIDTH, 0],
  });

  const overlayOpacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5],
  });

  const handleNavigation = (screen) => {
    toggleSidebar();
    if (screen === 'logout') {
      logout();
    } else {
      navigation.navigate(screen);
    }
  };

  const getCurrentRouteName = () => {
    const state = navigation.getState();
    const route = state.routes[state.index];
    return route?.name || '';
  };

  const currentRoute = getCurrentRouteName();

  const handleNotificationPress = () => {
    console.log('Notification badge pressed, navigating with notifications:', notifications);
    if (sidebarOpen) {
      toggleSidebar();
    }
    navigation.navigate('Notifications', {
      notifications: notifications || []
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      {/* Header with specefic role color */}
      <View style={[styles.header, { backgroundColor: primaryColor }]}>
        <TouchableOpacity onPress={toggleSidebar} style={styles.menuButton}>
          <Ionicons name="menu" size={28} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{title}</Text>
        <View style={styles.headerRight}>
          {enableVoiceCommands && (
            <TouchableOpacity
              style={styles.voiceButton}
              onPress={() => setVoiceModalVisible(true)}
            >
              <Ionicons name="mic-outline" size={24} color="white" />
            </TouchableOpacity>
          )}
          <NotificationBadge
            count={notifications.length}
            onPress={handleNotificationPress}
          />
        </View>
      </View>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <Animated.View
          style={[
            styles.overlay,
            { opacity: overlayOpacity }
          ]}
          pointerEvents={sidebarOpen ? 'auto' : 'none'}
          onTouchStart={toggleSidebar}
        />
      )}

      {/* Sidebar */}
      <Animated.View
        style={[
          styles.sidebar,
          { transform: [{ translateX }] }
        ]}
      >
        <View style={styles.sidebarHeader}>
          <View style={styles.profileSection}>
            {/* Avatar with role-specific color */}
            <View style={[styles.profileIcon, { backgroundColor: primaryColor }]}>
              <Text style={styles.profileIconText}>
                {user?.firstName?.charAt(0) || ''}{user?.lastName?.charAt(0) || ''}
              </Text>
              {!user?.profileCompleted && (
                <View style={styles.incompleteProfileBadge}>
                  <Ionicons name="alert" size={12} color="#fff" />
                </View>
              )}
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>
                {user?.firstName} {user?.lastName}
              </Text>
              <Text style={styles.roleName}>
                {roleName}
                {!user?.profileCompleted && <Text style={styles.incompleteText}> • Incomplete Profile</Text>}
              </Text>
            </View>
          </View>
        </View>

        <ScrollView style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.menuItem,
                currentRoute === item.screen && [styles.activeMenuItem]
              ]}
              onPress={() => handleNavigation(item.screen)}
            >
              <Ionicons
                name={item.icon}
                size={24}
                color={COLORS.textMedium}
              />
              <Text
                style={[
                  styles.menuItemText,
                  currentRoute === item.screen && styles.activeMenuItemText
                ]}
              >
                {item.label}
              </Text>
            </TouchableOpacity>
          ))}

          <View style={styles.divider} />

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => handleNavigation('logout')}
          >
            <Ionicons name="log-out" size={24} color={COLORS.error} />
            <Text style={[styles.menuItemText, { color: COLORS.error }]}>Logout</Text>
          </TouchableOpacity>
        </ScrollView>
      </Animated.View>

      {/* Main Content */}
      <View style={styles.content}>
        {children}
      </View>

      {/* Voice Command Modal */}
      {enableVoiceCommands && (
        <VoiceCommandModal
          visible={voiceModalVisible}
          onClose={() => setVoiceModalVisible(false)}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    zIndex: 10,
    paddingTop: 40
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 8,
    zIndex: 20,
  },
  iconButton: {
    padding: 8,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  voiceButton: {
    padding: 8,
    marginRight: 8,
  },
  content: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'black',
    zIndex: 15,
  },
  sidebar: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: SIDEBAR_WIDTH,
    backgroundColor: COLORS.surface,
    zIndex: 20,
    elevation: 15,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  sidebarHeader: {
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    ...Platform.select({
      ios: {
        paddingTop: 60,
      },
      android: {
        paddingTop: 40,
      },
    }),
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileIconText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textDark,
  },
  roleName: {
    fontSize: 14,
    color: COLORS.textMedium,
    marginTop: 2,
  },
  menuContainer: {
    paddingTop: 8,
    paddingBottom: 24,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  menuItemText: {
    fontSize: 16,
    marginLeft: 16,
    color: COLORS.textMedium,
  },
  activeMenuItem: {
    // Background color is set dynamically based on the user role
  },
  activeMenuItemText: {
    fontWeight: 'bold',
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 8,
    marginHorizontal: 16,
  },
  incompleteProfileBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.error,
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fff',
  },
  incompleteText: {
    color: COLORS.error,
    fontSize: 12,
  },
});

export default DashboardLayout;