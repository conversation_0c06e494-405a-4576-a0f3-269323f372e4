import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import PatientVideoCall from '../components/video/PatientVideoCall';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const PatientVideoCallScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [callData, setCallData] = useState(null);

  // Get call information from route params
  const { callId, roomName } = route.params || {};
  const doctorInfo = route.params?.doctorInfo || {};

  // Fetch call details if only callId is provided
  useEffect(() => {
    const fetchCallDetails = async () => {
      if (!callId) {
        navigation.goBack();
        return;
      }

      try {
        setLoading(true);
        const response = await axios.get(`/api/video/${callId}`);
        
        if (response.data) {
          setCallData(response.data);
        } else {
          Alert.alert('Error', 'Call information not found');
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error fetching call details:', error);
        Alert.alert('Error', 'Failed to get call information');
        navigation.goBack();
      } finally {
        setLoading(false);
      }
    };

    if (callId && !roomName) {
      fetchCallDetails();
    } else if (roomName) {
      setCallData({ roomName });
      setLoading(false);
    } else {
      Alert.alert('Error', 'Invalid call information');
      navigation.goBack();
    }
  }, [callId, roomName]);

  // Handle call end
  const handleCallEnd = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      {!loading && callData && (
        <PatientVideoCall
          doctorInfo={doctorInfo}
          onCallEnd={handleCallEnd}
          callId={callId}
          roomName={callData.roomName || roomName}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});

export default PatientVideoCallScreen;
