import React, { useState, useMemo, useEffect } from 'react';
import { View, StyleSheet, Text, ActivityIndicator, TextInput, TouchableOpacity, Alert, Keyboard } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { showMessage } from 'react-native-flash-message';
import { Ionicons } from '@expo/vector-icons';
import { usersAPI } from '../config/api';
import { useAuth } from '../contexts/AuthContext';
import { getThemeForRole } from '../config/theme';

const AddPatientScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [userCode, setUserCode] = useState('');
  const [codeError, setCodeError] = useState('');

  // Get theme colors based on user role
  const theme = useMemo(() => {
    return getThemeForRole(user?.role || 'default');
  }, [user]);

  const primaryColor = theme.colors.primary;

  // Auto-capitalize the input
  useEffect(() => {
    if (userCode) {
      setUserCode(userCode.toUpperCase());
    }
  }, [userCode]);

  // Validate code format whenever it changes
  useEffect(() => {
    if (userCode && userCode.length > 0) {
      if (!/^[A-Z0-9]*$/.test(userCode)) {
        setCodeError('Code can only contain letters and numbers');
      } else if (userCode.length < 8 && userCode.length > 0) {
        setCodeError('Code must be 8 characters');
      } else {
        setCodeError('');
      }
    } else {
      setCodeError('');
    }
  }, [userCode]);

  // Determine relationship type based on user role
  const relationshipType = (() => {
    if (user?.role === 'doctor') return 'doctor-patient';
    if (user?.role === 'nurse') return 'nurse-patient';
    if (user?.role === 'pharmacist') return 'pharmacist-patient';
    return '';
  })();

  const handleSubmit = async () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    
    // Validate code format
    if (!userCode || userCode.length !== 8) {
      showMessage({
        message: 'Invalid Format',
        description: 'Please enter a valid 8-character code',
        type: 'danger',
        backgroundColor: primaryColor,
      });
      return;
    }

    if (!/^[A-Z0-9]{8}$/.test(userCode)) {
      showMessage({
        message: 'Invalid Format',
        description: 'Code must be 8 characters (letters and numbers only)',
        type: 'danger',
        backgroundColor: primaryColor,
      });
      return;
    }

    setLoading(true);
    try {
      console.log('Submitting code:', userCode);
      
      // First verify the user exists
      const userResponse = await usersAPI.getUserByCode(userCode);
      console.log('User found:', userResponse);

      if (!userResponse || userResponse?.role !== 'patient') {
        showMessage({
          message: 'Invalid User',
          description: 'This code does not belong to a patient',
          type: 'danger',
          backgroundColor: primaryColor,
        });
        setLoading(false);
        return;
      }

      // Link the user
      console.log('Linking user with type:', relationshipType);
      const linkResponse = await usersAPI.linkUser(userCode, relationshipType);
      console.log('Link response:', linkResponse);

      showMessage({
        message: 'Success',
        description: `Patient ${userResponse.displayName} added successfully`,
        type: 'success',
        backgroundColor: primaryColor,
      });

      // Go back to the previous screen
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    } catch (error) {
      console.error('Raw error:', error);
      let errorMessage = 'Failed to add patient. Please try again.';

      if (error?.responseData?.error === 'User not found with this code') {
        errorMessage = 'Invalid code. No patient found with this code.';
      } else if (error?.message?.includes('Network Error')) {
        errorMessage = 'Network error. Please check your connection.';
      }

      showMessage({
        message: 'Error',
        description: errorMessage,
        type: 'danger',
        backgroundColor: primaryColor,
      });
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={styles.loadingText}>Adding patient...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.formContainer}>
        <Ionicons name="people" size={60} color={primaryColor} style={styles.icon} />

        <Text style={styles.title}>Add Patient</Text>
        <Text style={styles.subtitle}>
          Enter the 8-character code from the patient's profile
        </Text>

        <TextInput
          style={[styles.input, codeError && styles.inputError]}
          value={userCode}
          onChangeText={setUserCode}
          placeholder="Enter code (e.g. ABC12345)"
          placeholderTextColor="#999"
          autoCapitalize="characters"
          maxLength={8}
          keyboardType="default"
        />
        
        {codeError ? (
          <Text style={styles.errorText}>{codeError}</Text>
        ) : (
          <Text style={styles.helperText}>
            {userCode.length}/8 characters
          </Text>
        )}

        <TouchableOpacity
          style={[
            styles.button, 
            { backgroundColor: primaryColor }, 
            (!userCode || userCode.length !== 8 || codeError) && styles.buttonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!userCode || userCode.length !== 8 || !!codeError || loading}
        >
          <Text style={styles.buttonText}>Connect Patient</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    padding: 30,
    borderRadius: 12,
    backgroundColor: '#f9f9f9',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  input: {
    width: '100%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    fontSize: 18,
    textAlign: 'center',
    letterSpacing: 2,
    marginBottom: 8,
  },
  inputError: {
    borderColor: '#ff6b6b',
    borderWidth: 2,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginBottom: 24,
    textAlign: 'center',
  },
  helperText: {
    color: '#666',
    fontSize: 14,
    marginBottom: 24,
    textAlign: 'center',
  },
  button: {
    width: '100%',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#555',
  },
});

export default AddPatientScreen;