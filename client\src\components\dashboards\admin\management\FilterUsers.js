import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const FilterUsers = ({ options, filters, setFilters }) => {
  const toggleFilter = (type, value) => {
    setFilters(prevFilters => {
      // Check if the value is already in the filter array
      if (prevFilters[type].includes(value)) {
        // Remove the value
        return {
          ...prevFilters,
          [type]: prevFilters[type].filter(item => item !== value)
        };
      } else {
        // Add the value
        return {
          ...prevFilters,
          [type]: [...prevFilters[type], value]
        };
      }
    });
  };

  const clearFilters = () => {
    setFilters({
      role: [],
      country: [],
      city: [],
      speciality: [],
      status: []
    });
  };

  // Get the count of active filters for each category
  const getRoleFilterCount = () => filters.role.length;
  const getSpecialityFilterCount = () => filters.speciality.length;
  const getLocationFilterCount = () => filters.country.length + filters.city.length;
  const getStatusFilterCount = () => filters.status.length;

  // Get status chip color
  const getStatusColor = (status) => {
    switch(status) {
      case 'active': return '#4CAF50';
      case 'banned': return '#F44336';
      case 'inactive': return '#9E9E9E';
      default: return '#95A5A6';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.filterHeader}>
        <Text style={styles.filterTitle}>Filter Users</Text>
        <TouchableOpacity
          style={styles.clearButton}
          onPress={clearFilters}
        >
          <Ionicons name="close-circle-outline" size={20} color="#ff5722" style={{ marginRight: 4 }} />
          <Text style={styles.clearButtonText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.filterPanel}>
        {/* Status Filters */}
        <View style={styles.filterSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Status</Text>
            {getStatusFilterCount() > 0 && (
              <View style={styles.filterCountBadge}>
                <Text style={styles.filterCountText}>{getStatusFilterCount()}</Text>
              </View>
            )}
          </View>
          <View style={styles.filterOptions}>
            {options.status.map(status => (
              <TouchableOpacity
                key={`status-${status}`}
                style={[
                  styles.filterChip,
                  filters.status.includes(status) && styles.activeChip,
                  filters.status.includes(status) && { backgroundColor: getStatusColor(status) }
                ]}
                onPress={() => toggleFilter('status', status)}
              >
                <View style={styles.chipContent}>
                  <View
                    style={[
                      styles.statusDot,
                      { backgroundColor: getStatusColor(status) }
                    ]}
                  />
                  <Text style={[
                    styles.filterChipText,
                    filters.status.includes(status) && styles.activeChipText
                  ]}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Role Filters */}
        <View style={styles.filterSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Role</Text>
            {getRoleFilterCount() > 0 && (
              <View style={styles.filterCountBadge}>
                <Text style={styles.filterCountText}>{getRoleFilterCount()}</Text>
              </View>
            )}
          </View>
          <View style={styles.filterOptions}>
            {options.role.map(role => (
              <TouchableOpacity
                key={`role-${role}`}
                style={[
                  styles.filterChip,
                  filters.role.includes(role) && styles.activeChip
                ]}
                onPress={() => toggleFilter('role', role)}
              >
                <Text style={[
                  styles.filterChipText,
                  filters.role.includes(role) && styles.activeChipText
                ]}>
                  {role.charAt(0).toUpperCase() + role.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Speciality Filters */}
        {options.speciality.length > 0 && (
          <View style={styles.filterSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Speciality</Text>
              {getSpecialityFilterCount() > 0 && (
                <View style={styles.filterCountBadge}>
                  <Text style={styles.filterCountText}>{getSpecialityFilterCount()}</Text>
                </View>
              )}
            </View>
            <View style={styles.filterOptions}>
              {options.speciality.map(speciality => (
                <TouchableOpacity
                  key={`speciality-${speciality}`}
                  style={[
                    styles.filterChip,
                    filters.speciality.includes(speciality) && styles.activeChip
                  ]}
                  onPress={() => toggleFilter('speciality', speciality)}
                >
                  <Text style={[
                    styles.filterChipText,
                    filters.speciality.includes(speciality) && styles.activeChipText
                  ]}>
                    {speciality}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Location Filters */}
        {(options.country.length > 0 || options.city.length > 0) && (
          <View style={styles.filterSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Location</Text>
              {getLocationFilterCount() > 0 && (
                <View style={styles.filterCountBadge}>
                  <Text style={styles.filterCountText}>{getLocationFilterCount()}</Text>
                </View>
              )}
            </View>

            {/* Country Filters */}
            {options.country.length > 0 && (
              <View style={styles.subSection}>
                <Text style={styles.subSectionTitle}>Country</Text>
                <View style={styles.filterOptions}>
                  {options.country.map(country => (
                    <TouchableOpacity
                      key={`country-${country}`}
                      style={[
                        styles.filterChip,
                        filters.country.includes(country) && styles.activeChip
                      ]}
                      onPress={() => toggleFilter('country', country)}
                    >
                      <Text style={[
                        styles.filterChipText,
                        filters.country.includes(country) && styles.activeChipText
                      ]}>
                        {country}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {/* City Filters */}
            {options.city.length > 0 && (
              <View style={styles.subSection}>
                <Text style={styles.subSectionTitle}>City</Text>
                <View style={styles.filterOptions}>
                  {options.city.map(city => (
                    <TouchableOpacity
                      key={`city-${city}`}
                      style={[
                        styles.filterChip,
                        filters.city.includes(city) && styles.activeChip
                      ]}
                      onPress={() => toggleFilter('city', city)}
                    >
                      <Text style={[
                        styles.filterChipText,
                        filters.city.includes(city) && styles.activeChipText
                      ]}>
                        {city}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f5f7fa',
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e6ed',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e6ed'
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50'
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8
  },
  clearButtonText: {
    color: '#ff5722',
    fontWeight: '600'
  },
  filterPanel: {
    maxHeight: 300
  },
  filterSection: {
    marginBottom: 20
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10
  },
  sectionTitle: {
    fontWeight: '600',
    fontSize: 16,
    color: '#2c3e50'
  },
  filterCountBadge: {
    backgroundColor: '#3498db',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8
  },
  filterCountText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold'
  },
  subSection: {
    marginTop: 12,
    marginBottom: 12
  },
  subSectionTitle: {
    fontWeight: '500',
    fontSize: 14,
    marginBottom: 8,
    color: '#7f8c8d',
    paddingLeft: 4
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  filterChip: {
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderWidth: 1,
    borderColor: '#e0e6ed',
    marginRight: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  chipContent: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6
  },
  activeChip: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50'
  },
  filterChipText: {
    color: '#2c3e50',
    fontWeight: '500'
  },
  activeChipText: {
    color: '#fff',
    fontWeight: '600'
  }
});

export default FilterUsers;