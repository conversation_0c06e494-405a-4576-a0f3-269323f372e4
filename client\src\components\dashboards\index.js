// Base components
import DashboardLayout from './DashboardLayout';
import DashboardCard from './DashboardCard';
import Dashboard<PERSON>hart from './DashboardChart';
import UpcomingList from './UpcomingList';

// Role-specific dashboards
import PatientDashboard from './patient/PatientDashboard';
import DoctorDashboard from './doctor/DoctorDashboard';
import AdminDashboard from './admin/AdminDashboard';
import CaregiverDashboard from './caregiver/CaregiverDashboard';
import SupervisorDashboard from './supervisor/SupervisorDashboard';

// Define default menu items for each dashboard
// This ensures they exist even if the dashboard component doesn't define them
// Removed defaultProps for PatientDashboard to fix warning

// Removed defaultProps for DoctorDashboard to fix warning

AdminDashboard.defaultProps = {
  menuItems: [
    { label: 'Dashboard', icon: 'home', screen: 'AdminDashboard' },
    { label: 'Profile', icon: 'person', screen: 'Profile' }
  ],
  ...AdminDashboard.defaultProps
};

CaregiverDashboard.defaultProps = {
  menuItems: [
    { label: 'Dashboard', icon: 'home', screen: 'CaregiverDashboard' },
    { label: 'Profile', icon: 'person', screen: 'Profile' }
  ],
  ...CaregiverDashboard.defaultProps
};

SupervisorDashboard.defaultProps = {
  menuItems: [
    { label: 'Dashboard', icon: 'home', screen: 'SupervisorDashboard' },
    { label: 'Profile', icon: 'person', screen: 'Profile' }
  ],
  ...SupervisorDashboard.defaultProps
};

export {
  // Base components
  DashboardLayout,
  DashboardCard,
  DashboardChart,
  UpcomingList,

  // Role-specific dashboards
  PatientDashboard,
  DoctorDashboard,
  AdminDashboard,
  CaregiverDashboard,
  SupervisorDashboard
};