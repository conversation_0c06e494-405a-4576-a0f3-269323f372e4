# NeuroCare - Healthcare Platform

NeuroCare is a comprehensive healthcare platform connecting patients with doctors through a secure and feature-rich application.

## Video Calling Integration

This application uses Jitsi Meet - a completely free, open-source video conferencing solution that can scale to millions of users with no usage costs.

### Setup Instructions

#### 1. No Account Required!

Unlike commercial solutions, Jitsi Meet is:
- 100% free and open source
- No account registration required 
- No API keys needed
- No usage limits

By default, we use the public Jitsi server at `meet.jit.si`, which is free for everyone.

#### 2. Server Configuration

1. Install server dependencies:
   ```bash
   cd server
   npm install
   ```

2. Start the server:
   ```bash
   npm start
   ```

#### 3. Client Configuration

1. Install client dependencies:
   ```bash
   cd client
   npm install
   ```

2. Start the client application:
   ```bash
   npm start
   ```

### Video Call Security

Our implementation follows security best practices:
- Randomized meeting room names
- Secure signaling through Firebase Cloud Messaging
- Patient-doctor relationship verification before call initiation
- Optionally can add password protection for meetings

### Call Recording and Storage

Video consultations can be recorded and securely stored:
- Files are stored in the server's file system at `server/uploads/call-recordings`
- Recording metadata is stored in Firestore
- Recordings are automatically linked to patient medical records

## Features

- Secure video consultations between doctors and patients
- Real-time messaging during calls
- Consultation note-taking
- Call recording
- Call duration tracking
- Automatic progress note creation from consultation notes

## Server API Endpoints

- `POST /api/video/create-call`: Create a new video call record
- `POST /api/video/upload-recording`: Upload call recordings
- `GET /api/video/:callId`: Get details about a specific call
- `POST /api/video/:callId/end`: End an active call

## File Path Handling

When working with file paths in this project, especially those from Windows systems that contain special characters or spaces, use the `pathHelper.js` utility:

```javascript
const { sanitizeFilePath, desanitizeFilePath } = require('./utils/pathHelper');

// When storing or displaying file paths
const safePath = sanitizeFilePath(originalWindowsPath);

// When retrieving file paths
const originalPath = desanitizeFilePath(safePath);
```

This prevents issues with file paths being improperly parsed or displayed, especially in web contexts.

## Setup Instructions

### Client Setup
1. Navigate to the client directory
   ```
   cd client
   ```
2. Install dependencies
   ```
   npm install
   ```
3. Start the development server
   ```
   npm start
   ```

### Server Setup
1. Navigate to the server directory
   ```
   cd server
   ```
2. Install dependencies
   ```
   npm install
   ```
3. Set up Firebase credentials:
   - Create a copy of `config/serviceAccountKey.example.json` as `config/serviceAccountKey.json`
   - Replace the placeholder values with your actual Firebase credentials
   ```
   cp config/serviceAccountKey.example.json config/serviceAccountKey.json
   ```
4. Start the server
   ```
   npm start
   ```

### Google Cloud Service Account
For Firebase and other Google Cloud services:

1. Create a service account in the Google Cloud Console
2. Download the service account key JSON file
3. Place it in `server/config/serviceAccountKey.json` (this file is gitignored)

**Important:** Never commit your service account keys to version control. The repository is configured to ignore these files.

## Security Note
Never commit sensitive files like `serviceAccountKey.json` to GitHub. These files have been added to `.gitignore`.
