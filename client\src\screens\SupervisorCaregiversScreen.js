import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Avatar, Chip, Searchbar, Button } from 'react-native-paper';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '../contexts/AuthContext';
import { ROLE_COLORS } from '../config/theme';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';
import { usersAPI } from '../config/api';

const SupervisorCaregiversScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const supervisorColors = ROLE_COLORS.supervisor;

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [caregivers, setCaregivers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [removing, setRemoving] = useState(false);

  useEffect(() => {
    loadPatients();
  }, []);

  useEffect(() => {
    if (selectedPatient) {
      loadCaregivers(selectedPatient.uid);
    } else {
      setCaregivers([]);
    }
  }, [selectedPatient]);

  const loadPatients = async () => {
    try {
      setLoading(true);
      // Get patients linked to this supervisor
      const linkedUsers = await usersAPI.getLinkedUsers('patients');
      setPatients(linkedUsers);
      
      // If there are patients, select the first one by default
      if (linkedUsers.length > 0) {
        setSelectedPatient(linkedUsers[0]);
      }
    } catch (error) {
      console.error('Error loading patients:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load patients. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadCaregivers = async (patientId) => {
    try {
      setLoading(true);
      const patientCaregivers = await firebaseCaregiverService.getPatientCaregivers(patientId);
      setCaregivers(patientCaregivers);
    } catch (error) {
      console.error('Error loading caregivers:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load caregivers. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPatients();
    if (selectedPatient) {
      await loadCaregivers(selectedPatient.uid);
    }
    setRefreshing(false);
  };

  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
  };

  const handleRemoveCaregiver = (caregiver) => {
    if (!selectedPatient) return;

    Alert.alert(
      'Remove Caregiver',
      `Are you sure you want to remove ${caregiver.firstName} ${caregiver.lastName} from ${selectedPatient.firstName} ${selectedPatient.lastName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              setRemoving(true);
              await firebaseCaregiverService.removeCaregiverAssignment(
                selectedPatient.uid,
                caregiver.uid
              );
              
              // Refresh the caregivers list
              await loadCaregivers(selectedPatient.uid);
              
              showMessage({
                message: 'Success',
                description: 'Caregiver removed successfully',
                type: 'success',
                backgroundColor: supervisorColors.primary,
              });
            } catch (error) {
              console.error('Error removing caregiver:', error);
              showMessage({
                message: 'Error',
                description: 'Failed to remove caregiver. Please try again.',
                type: 'danger',
              });
            } finally {
              setRemoving(false);
            }
          },
        },
      ]
    );
  };

  const handleAssignCaregiver = () => {
    if (selectedPatient) {
      navigation.navigate('SupervisorAssignCaregiverScreen', { patient: selectedPatient });
    } else {
      showMessage({
        message: 'Error',
        description: 'Please select a patient first',
        type: 'warning',
      });
    }
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  // Filter caregivers based on search query
  const filteredCaregivers = caregivers.filter(caregiver => {
    const fullName = `${caregiver.firstName} ${caregiver.lastName}`.toLowerCase();
    const email = caregiver.email?.toLowerCase() || '';
    const query = searchQuery.toLowerCase();
    
    return fullName.includes(query) || email.includes(query);
  });

  const renderPatientItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.patientCard,
        selectedPatient?.uid === item.uid && styles.selectedPatientCard
      ]}
      onPress={() => handlePatientSelect(item)}
    >
      <Avatar.Text
        size={40}
        label={`${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`}
        backgroundColor={selectedPatient?.uid === item.uid ? supervisorColors.primary : '#e0e0e0'}
        color={selectedPatient?.uid === item.uid ? '#fff' : '#333'}
      />
      <Text style={[
        styles.patientName,
        selectedPatient?.uid === item.uid && styles.selectedPatientName
      ]}>
        {`${item.firstName || ''} ${item.lastName || ''}`}
      </Text>
    </TouchableOpacity>
  );

  const renderCaregiverItem = ({ item }) => (
    <Card style={styles.caregiverCard}>
      <Card.Content style={styles.caregiverCardContent}>
        <Avatar.Text
          size={50}
          label={`${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`}
          backgroundColor={supervisorColors.primary}
          color="#fff"
        />
        <View style={styles.caregiverInfo}>
          <Text style={styles.caregiverName}>{`${item.firstName || ''} ${item.lastName || ''}`}</Text>
          <Text style={styles.caregiverEmail}>{item.email || ''}</Text>
          {item.speciality && (
            <Chip style={styles.specialityChip} textStyle={styles.specialityText}>
              {item.speciality}
            </Chip>
          )}
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveCaregiver(item)}
          disabled={removing}
        >
          <Ionicons name="close-circle" size={24} color="#FF5252" />
        </TouchableOpacity>
      </Card.Content>
    </Card>
  );

  const renderEmptyPatientsList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No patients found</Text>
      <Text style={styles.emptySubtext}>Add patients to your account first</Text>
      <Button
        mode="contained"
        onPress={() => navigation.navigate('Patients')}
        style={[styles.addButton, { backgroundColor: supervisorColors.primary }]}
      >
        Add Patients
      </Button>
    </View>
  );

  const renderEmptyCaregiverList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No caregivers assigned</Text>
      <Text style={styles.emptySubtext}>Assign caregivers to this patient</Text>
      <Button
        mode="contained"
        onPress={handleAssignCaregiver}
        style={[styles.addButton, { backgroundColor: supervisorColors.primary }]}
      >
        Assign Caregivers
      </Button>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Assigned Caregivers</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
        >
          <Ionicons name="refresh" size={24} color={supervisorColors.primary} />
        </TouchableOpacity>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={supervisorColors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          <View style={styles.patientsSection}>
            <Text style={styles.sectionTitle}>Select Patient</Text>
            {patients.length > 0 ? (
              <FlatList
                data={patients}
                renderItem={renderPatientItem}
                keyExtractor={(item) => item.uid}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.patientList}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={handleRefresh}
                    colors={[supervisorColors.primary]}
                  />
                }
              />
            ) : (
              renderEmptyPatientsList()
            )}
          </View>

          {selectedPatient && (
            <View style={styles.caregiversSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Caregivers for {selectedPatient.firstName} {selectedPatient.lastName}</Text>
                <Button
                  mode="contained"
                  onPress={handleAssignCaregiver}
                  style={[styles.assignButton, { backgroundColor: supervisorColors.primary }]}
                  icon="plus"
                >
                  Assign
                </Button>
              </View>

              <Searchbar
                placeholder="Search caregivers..."
                onChangeText={handleSearchChange}
                value={searchQuery}
                style={styles.searchBar}
              />

              {caregivers.length > 0 ? (
                <FlatList
                  data={filteredCaregivers}
                  renderItem={renderCaregiverItem}
                  keyExtractor={(item) => item.uid}
                  contentContainerStyle={styles.caregiverList}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={() => loadCaregivers(selectedPatient.uid)}
                      colors={[supervisorColors.primary]}
                    />
                  }
                />
              ) : (
                renderEmptyCaregiverList()
              )}
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  patientsSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  patientList: {
    paddingRight: 16,
  },
  patientCard: {
    alignItems: 'center',
    marginRight: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
    elevation: 1,
    width: 100,
  },
  selectedPatientCard: {
    backgroundColor: '#E8F5E9',
    borderWidth: 1,
    borderColor: ROLE_COLORS.supervisor.primary,
  },
  patientName: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
    color: '#333',
  },
  selectedPatientName: {
    fontWeight: 'bold',
    color: ROLE_COLORS.supervisor.primary,
  },
  caregiversSection: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  assignButton: {
    borderRadius: 8,
  },
  searchBar: {
    marginBottom: 16,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  caregiverList: {
    paddingBottom: 16,
  },
  caregiverCard: {
    marginBottom: 12,
    elevation: 1,
  },
  caregiverCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  caregiverInfo: {
    flex: 1,
    marginLeft: 16,
  },
  caregiverName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  caregiverEmail: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  specialityChip: {
    alignSelf: 'flex-start',
    backgroundColor: '#E1F5FE',
    height: 24,
  },
  specialityText: {
    fontSize: 10,
  },
  removeButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  addButton: {
    marginTop: 16,
  },
});

export default SupervisorCaregiversScreen;
