import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TextInput
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Avatar,
  Button,
  Card,
  Chip,
  Divider,
  Searchbar,
  FAB,
  Portal,
  Dialog,
  IconButton
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '../contexts/AuthContext';
import { usersAPI } from '../config/api';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';
import { ROLE_COLORS } from '../config/theme';

const SupervisorCaregiverManagementScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const supervisorColors = ROLE_COLORS.supervisor;

  // State variables
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [assignedCaregivers, setAssignedCaregivers] = useState([]);
  const [availableCaregivers, setAvailableCaregivers] = useState([]);
  const [selectedCaregiverId, setSelectedCaregiverId] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [caregiverCodeQuery, setCaregiverCodeQuery] = useState('');
  const [assigning, setAssigning] = useState(false);
  const [removing, setRemoving] = useState(false);
  const [codeDialogVisible, setCodeDialogVisible] = useState(false);
  const [addCaregiverDialogVisible, setAddCaregiverDialogVisible] = useState(false);

  // Load patients on component mount and when screen is focused
  useEffect(() => {
    loadPatients();

    // Add a listener for when the screen comes into focus
    const unsubscribe = navigation.addListener('focus', () => {
      // Refresh data when screen is focused
      if (selectedPatient) {
        loadCaregivers(selectedPatient.uid);
        loadAvailableCaregivers();
      } else {
        loadPatients();
      }
    });

    // Clean up the listener when component unmounts
    return unsubscribe;
  }, [navigation]);

  // Load caregivers when selected patient changes
  useEffect(() => {
    if (selectedPatient) {
      loadCaregivers(selectedPatient.uid);
      loadAvailableCaregivers();
    } else {
      setAssignedCaregivers([]);
      setAvailableCaregivers([]);
    }
  }, [selectedPatient]);

  // Filter caregivers based on search query
  const filteredAssignedCaregivers = assignedCaregivers.filter(caregiver => {
    const fullName = `${caregiver.firstName || ''} ${caregiver.lastName || ''}`.toLowerCase();
    const email = (caregiver.email || '').toLowerCase();
    const query = searchQuery.toLowerCase();
    return fullName.includes(query) || email.includes(query);
  });

  const filteredAvailableCaregivers = availableCaregivers.filter(caregiver => {
    // Filter out caregivers that are already assigned
    if (assignedCaregivers.some(assigned => assigned.uid === caregiver.uid)) {
      return false;
    }

    const fullName = `${caregiver.firstName || ''} ${caregiver.lastName || ''}`.toLowerCase();
    const email = (caregiver.email || '').toLowerCase();
    const query = searchQuery.toLowerCase();
    return fullName.includes(query) || email.includes(query);
  });

  // Load patients linked to this supervisor
  const loadPatients = async () => {
    try {
      setLoading(true);
      const linkedUsers = await usersAPI.getLinkedUsers('patients');
      setPatients(linkedUsers);

      // If there are patients, select the first one by default
      if (linkedUsers.length > 0) {
        setSelectedPatient(linkedUsers[0]);
      }
    } catch (error) {
      console.error('Error loading patients:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load patients. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load assigned caregivers for a patient
  const loadCaregivers = async (patientId) => {
    try {
      setLoading(true);
      const patientCaregivers = await firebaseCaregiverService.getPatientCaregivers(patientId);
      setAssignedCaregivers(patientCaregivers);
    } catch (error) {
      console.error('Error loading caregivers:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load caregivers. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load available caregivers for assignment
  const loadAvailableCaregivers = async () => {
    try {
      const caregivers = await firebaseCaregiverService.getAvailableCaregivers();
      setAvailableCaregivers(caregivers);
    } catch (error) {
      console.error('Error loading available caregivers:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load available caregivers. Please try again.',
        type: 'danger',
      });
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPatients();
    if (selectedPatient) {
      await loadCaregivers(selectedPatient.uid);
      await loadAvailableCaregivers();
    }
    setRefreshing(false);
  };

  // Handle patient selection
  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    setSelectedCaregiverId(null);
  };

  // Handle caregiver selection for assignment
  const handleSelectCaregiver = (caregiverId) => {
    setSelectedCaregiverId(caregiverId === selectedCaregiverId ? null : caregiverId);
  };

  // Handle assigning a caregiver
  const handleAssignCaregiver = async () => {
    if (!selectedPatient || !selectedCaregiverId) return;

    try {
      setAssigning(true);
      await firebaseCaregiverService.assignCaregiverToPatient(
        selectedPatient.uid,
        selectedCaregiverId
      );

      // Refresh the assigned caregivers list
      await loadCaregivers(selectedPatient.uid);

      // Reset selection
      setSelectedCaregiverId(null);

      showMessage({
        message: 'Success',
        description: 'Caregiver assigned successfully',
        type: 'success',
        backgroundColor: supervisorColors.primary,
      });
    } catch (error) {
      console.error('Error assigning caregiver:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to assign caregiver. Please try again.',
        type: 'danger',
      });
    } finally {
      setAssigning(false);
    }
  };

  // Handle assigning a caregiver by code
  const handleAssignCaregiverByCode = async () => {
    if (!selectedPatient || !caregiverCodeQuery.trim()) {
      showMessage({
        message: 'Error',
        description: 'Please enter a valid caregiver code',
        type: 'warning',
      });
      return;
    }

    try {
      setAssigning(true);
      await firebaseCaregiverService.assignCaregiverByCode(
        selectedPatient.uid,
        caregiverCodeQuery.trim()
      );

      // Refresh the assigned caregivers list
      await loadCaregivers(selectedPatient.uid);

      // Reset code and close dialog
      setCaregiverCodeQuery('');
      setCodeDialogVisible(false);

      showMessage({
        message: 'Success',
        description: 'Caregiver assigned successfully',
        type: 'success',
        backgroundColor: supervisorColors.primary,
      });
    } catch (error) {
      console.error('Error assigning caregiver by code:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to assign caregiver. Please check the code and try again.',
        type: 'danger',
      });
    } finally {
      setAssigning(false);
    }
  };

  // Handle removing a caregiver
  const handleRemoveCaregiver = (caregiverId) => {
    if (!selectedPatient) return;

    Alert.alert(
      'Remove Caregiver',
      'Are you sure you want to remove this caregiver?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              setRemoving(true);
              await firebaseCaregiverService.removeCaregiverAssignment(
                selectedPatient.uid,
                caregiverId
              );

              // Refresh the caregivers list
              await loadCaregivers(selectedPatient.uid);

              showMessage({
                message: 'Success',
                description: 'Caregiver removed successfully',
                type: 'success',
                backgroundColor: supervisorColors.primary,
              });
            } catch (error) {
              console.error('Error removing caregiver:', error);
              showMessage({
                message: 'Error',
                description: 'Failed to remove caregiver. Please try again.',
                type: 'danger',
              });
            } finally {
              setRemoving(false);
            }
          },
        },
      ]
    );
  };

  // Handle search query change
  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  // Render patient item
  const renderPatientItem = ({ item }) => {
    // Ensure we have valid initials even if firstName or lastName is undefined
    const firstInitial = item.firstName ? item.firstName.charAt(0).toUpperCase() : '';
    const lastInitial = item.lastName ? item.lastName.charAt(0).toUpperCase() : '';
    const initials = (firstInitial + lastInitial) || 'P';

    // Ensure we have a valid display name
    const displayName = [item.firstName, item.lastName].filter(Boolean).join(' ') || 'Patient';

    return (
      <TouchableOpacity
        style={[
          styles.patientCard,
          selectedPatient?.uid === item.uid && styles.selectedPatientCard
        ]}
        onPress={() => handlePatientSelect(item)}
      >
        <Avatar.Text
          size={40}
          label={initials}
          backgroundColor={selectedPatient?.uid === item.uid ? '#6A1B9A' : '#e0e0e0'}
          color={selectedPatient?.uid === item.uid ? '#fff' : '#333'}
        />
        <Text style={[
          styles.patientName,
          selectedPatient?.uid === item.uid && styles.selectedPatientName
        ]}>
          {displayName}
        </Text>
      </TouchableOpacity>
    );
  };

  // Render assigned caregiver item
  const renderAssignedCaregiverItem = ({ item }) => {
    // Ensure we have valid initials even if firstName or lastName is undefined
    const firstInitial = item.firstName ? item.firstName.charAt(0).toUpperCase() : '';
    const lastInitial = item.lastName ? item.lastName.charAt(0).toUpperCase() : '';
    const initials = (firstInitial + lastInitial) || 'C';

    // Ensure we have a valid display name
    const displayName = [item.firstName, item.lastName].filter(Boolean).join(' ') || 'Caregiver';

    return (
      <Card style={styles.caregiverCard}>
        <Card.Content style={styles.caregiverCardContent}>
          <Avatar.Text
            size={50}
            label={initials}
            backgroundColor="#4A148C"
            color="#fff"
          />
          <View style={styles.caregiverInfo}>
            <Text style={styles.caregiverName}>{displayName}</Text>
            <Text style={styles.caregiverEmail}>{item.email || ''}</Text>
            {item.speciality && (
              <Chip style={styles.specialityChip} textStyle={styles.specialityText}>
                {item.speciality}
              </Chip>
            )}
          </View>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveCaregiver(item.uid)}
            disabled={removing}
          >
            <Ionicons name="close-circle" size={24} color="#FF5252" />
          </TouchableOpacity>
        </Card.Content>
      </Card>
    );
  };

  // Render available caregiver item
  const renderAvailableCaregiverItem = ({ item }) => {
    // Ensure we have valid initials even if firstName or lastName is undefined
    const firstInitial = item.firstName ? item.firstName.charAt(0).toUpperCase() : '';
    const lastInitial = item.lastName ? item.lastName.charAt(0).toUpperCase() : '';
    const initials = (firstInitial + lastInitial) || 'C';

    // Ensure we have a valid display name
    const displayName = [item.firstName, item.lastName].filter(Boolean).join(' ') || 'Caregiver';

    return (
      <TouchableOpacity
        style={[
          styles.availableCaregiverCard,
          selectedCaregiverId === item.uid && styles.selectedCaregiverCard
        ]}
        onPress={() => handleSelectCaregiver(item.uid)}
      >
        <View style={styles.caregiverCardContent}>
          <Avatar.Text
            size={50}
            label={initials}
            backgroundColor="#4A148C"
            color="#fff"
          />
          <View style={styles.caregiverInfo}>
            <Text style={styles.caregiverName}>{displayName}</Text>
            <Text style={styles.caregiverEmail}>{item.email || ''}</Text>
            {item.speciality && (
              <Chip style={styles.specialityChip} textStyle={styles.specialityText}>
                {item.speciality}
              </Chip>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Render empty patients list
  const renderEmptyPatientsList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No patients found</Text>
      <Text style={styles.emptySubtext}>Add patients to your account first</Text>
      <Button
        mode="contained"
        onPress={() => navigation.navigate('Patients')}
        style={[styles.addButton, { backgroundColor: '#4A148C' }]}
      >
        Add Patients
      </Button>
    </View>
  );

  // Render empty caregivers list
  const renderEmptyCaregiverList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No caregivers assigned</Text>
      <Text style={styles.emptySubtext}>Assign caregivers to this patient</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Caregiver Management</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
        >
          <Ionicons name="refresh" size={24} color="#4A148C" />
        </TouchableOpacity>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4A148C" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          {/* Patient Selection Section */}
          <View style={styles.patientsSection}>
            <Text style={styles.sectionTitle}>Select Patient</Text>
            {patients.length > 0 ? (
              <FlatList
                data={patients}
                renderItem={renderPatientItem}
                keyExtractor={(item) => item.uid}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.patientList}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={handleRefresh}
                    colors={['#4A148C']}
                  />
                }
              />
            ) : (
              renderEmptyPatientsList()
            )}
          </View>

          {selectedPatient && (
            <>
              {/* Assigned Caregivers Section */}
              <View style={styles.assignedSection}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>
                    Assigned Caregivers for {selectedPatient.firstName || ''} {selectedPatient.lastName || ''}
                  </Text>
                </View>

                <Searchbar
                  placeholder="Search caregivers..."
                  onChangeText={handleSearchChange}
                  value={searchQuery}
                  style={styles.searchBar}
                />

                {assignedCaregivers.length > 0 ? (
                  <FlatList
                    data={filteredAssignedCaregivers}
                    renderItem={renderAssignedCaregiverItem}
                    keyExtractor={(item) => item.uid}
                    contentContainerStyle={styles.caregiverList}
                    refreshControl={
                      <RefreshControl
                        refreshing={refreshing}
                        onRefresh={() => loadCaregivers(selectedPatient.uid)}
                        colors={['#4A148C']}
                      />
                    }
                  />
                ) : (
                  renderEmptyCaregiverList()
                )}
              </View>

              <Divider style={styles.divider} />

              {/* Available Caregivers Section */}
              <View style={styles.availableSection}>
                <Text style={styles.sectionTitle}>Available Caregivers</Text>

                <View style={styles.assignButtonsContainer}>
                  <Button
                    mode="contained"
                    onPress={() => setAddCaregiverDialogVisible(true)}
                    style={[styles.addCaregiverButton, { backgroundColor: '#4A148C' }]}
                    icon="account-plus"
                  >
                    Add Caregiver
                  </Button>
                </View>

                {availableCaregivers.length > 0 ? (
                  <FlatList
                    data={filteredAvailableCaregivers}
                    renderItem={renderAvailableCaregiverItem}
                    keyExtractor={(item) => item.uid}
                    style={styles.availableCaregiversList}
                  />
                ) : (
                  <Text style={styles.noDataText}>No available caregivers found</Text>
                )}

                {selectedCaregiverId && (
                  <Button
                    mode="contained"
                    onPress={handleAssignCaregiver}
                    style={[styles.assignButton, { backgroundColor: '#4A148C' }]}
                    disabled={assigning}
                    loading={assigning}
                  >
                    Assign Selected Caregiver
                  </Button>
                )}
              </View>
            </>
          )}
        </View>
      )}

      {/* Dialog for choosing how to add a caregiver */}
      <Portal>
        <Dialog
          visible={addCaregiverDialogVisible}
          onDismiss={() => setAddCaregiverDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title>Add Caregiver</Dialog.Title>
          <Dialog.Content>
            <View style={styles.dialogOptionContainer}>
              <TouchableOpacity
                style={styles.dialogOption}
                onPress={() => {
                  setAddCaregiverDialogVisible(false);
                  navigation.navigate('ScanCaregiverQRScreen', {
                    patientId: selectedPatient.uid,
                    returnScreen: 'SupervisorCaregiverManagement',
                    mode: 'camera'
                  });
                }}
              >
                <Ionicons name="qr-code" size={40} color="#4A148C" />
                <Text style={styles.dialogOptionTitle}>Scan QR Code</Text>
                <Text style={styles.dialogOptionDescription}>Scan caregiver's QR code with camera</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.dialogOption}
                onPress={() => {
                  setAddCaregiverDialogVisible(false);
                  setCodeDialogVisible(true);
                }}
              >
                <Ionicons name="keypad" size={40} color="#4A148C" />
                <Text style={styles.dialogOptionTitle}>Enter Code</Text>
                <Text style={styles.dialogOptionDescription}>Manually enter caregiver's unique code</Text>
              </TouchableOpacity>
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => setAddCaregiverDialogVisible(false)}
              color="#4A148C"
            >
              Cancel
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Dialog for entering caregiver code */}
      <Portal>
        <Dialog
          visible={codeDialogVisible}
          onDismiss={() => setCodeDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title>Enter Caregiver Code</Dialog.Title>
          <Dialog.Content>
            <TextInput
              style={styles.codeInput}
              placeholder="Enter caregiver's unique code"
              value={caregiverCodeQuery}
              onChangeText={setCaregiverCodeQuery}
              autoCapitalize="none"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => setCodeDialogVisible(false)}
              color="#4A148C"
            >
              Cancel
            </Button>
            <Button
              onPress={handleAssignCaregiverByCode}
              loading={assigning}
              disabled={!caregiverCodeQuery.trim() || assigning}
              color="#4A148C"
            >
              Assign
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  patientsSection: {
    padding: 16,
    backgroundColor: '#fff',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  patientList: {
    paddingVertical: 8,
  },
  patientCard: {
    alignItems: 'center',
    marginRight: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
    elevation: 1,
    width: 100,
  },
  selectedPatientCard: {
    backgroundColor: '#EDE7F6',
    borderWidth: 1,
    borderColor: '#4A148C',
  },
  patientName: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
    color: '#333',
  },
  selectedPatientName: {
    fontWeight: 'bold',
    color: '#4A148C',
  },
  assignedSection: {
    padding: 16,
    backgroundColor: '#fff',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  searchBar: {
    marginBottom: 16,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  caregiverList: {
    paddingBottom: 16,
  },
  caregiverCard: {
    marginBottom: 8,
    elevation: 1,
  },
  caregiverCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  caregiverInfo: {
    flex: 1,
    marginLeft: 12,
  },
  caregiverName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  caregiverEmail: {
    fontSize: 12,
    color: '#666',
  },
  specialityChip: {
    marginTop: 4,
    height: 24,
    alignSelf: 'flex-start',
  },
  specialityText: {
    fontSize: 10,
  },
  removeButton: {
    padding: 8,
  },
  divider: {
    height: 8,
    backgroundColor: '#f0f0f0',
  },
  availableSection: {
    padding: 16,
    backgroundColor: '#fff',
    flex: 1,
  },
  availableCaregiversList: {
    flex: 1,
  },
  availableCaregiverCard: {
    backgroundColor: '#fff',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    elevation: 1,
  },
  selectedCaregiverCard: {
    backgroundColor: '#EDE7F6',
    borderWidth: 1,
    borderColor: '#4A148C',
  },
  assignButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  scanButton: {
    borderRadius: 8,
    flex: 1,
  },
  codeButton: {
    borderRadius: 8,
    flex: 1,
  },
  assignButton: {
    marginTop: 16,
    borderRadius: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    marginBottom: 16,
    textAlign: 'center',
  },
  addButton: {
    marginTop: 8,
  },
  noDataText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 24,
  },
  dialog: {
    backgroundColor: '#fff',
  },
  dialogOptionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  dialogOption: {
    flex: 1,
    alignItems: 'center',
    padding: 15,
    marginHorizontal: 5,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  dialogOptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 10,
    marginBottom: 5,
  },
  dialogOptionDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  codeInput: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  addCaregiverButton: {
    borderRadius: 8,
    width: '100%',
  },
});

export default SupervisorCaregiverManagementScreen;
