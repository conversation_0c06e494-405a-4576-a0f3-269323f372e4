import AsyncStorage from '@react-native-async-storage/async-storage';

// Key for AsyncStorage
const MEDICAL_NOTES_STORAGE_KEY = '@neurocare:medical_notes';

/**
 * Service for managing local storage of medical notes
 */
export const localMedicalNotesService = {
  /**
   * Save a medical note
   * @param {Object} noteData - The note data to save
   * @returns {Promise<Object>} - The saved note with ID
   */
  saveMedicalNote: async (noteData) => {
    try {
      // Get existing notes
      const existingNotesStr = await AsyncStorage.getItem(MEDICAL_NOTES_STORAGE_KEY);
      const existingNotes = existingNotesStr ? JSON.parse(existingNotesStr) : [];

      // Create new note with ID and timestamp
      const newNote = {
        id: Date.now().toString(),
        ...noteData,
        timestamp: new Date().toISOString(),
      };

      // Add to existing notes
      const updatedNotes = [...existingNotes, newNote];

      // Save back to storage
      await AsyncStorage.setItem(MEDICAL_NOTES_STORAGE_KEY, JSON.stringify(updatedNotes));

      return newNote;
    } catch (error) {
      console.error('Error saving medical note:', error);
      throw error;
    }
  },

  /**
   * Get all medical notes for a specific patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medical notes
   */
  getPatientMedicalNotes: async (patientId) => {
    try {
      const notesStr = await AsyncStorage.getItem(MEDICAL_NOTES_STORAGE_KEY);
      const notes = notesStr ? JSON.parse(notesStr) : [];
      
      // Filter notes for the specific patient
      return notes.filter(note => note.patientId === patientId);
    } catch (error) {
      console.error('Error getting patient medical notes:', error);
      throw error;
    }
  },

  /**
   * Get a specific medical note by ID
   * @param {string} noteId - The note ID
   * @returns {Promise<Object|null>} - The note or null if not found
   */
  getMedicalNoteById: async (noteId) => {
    try {
      const notesStr = await AsyncStorage.getItem(MEDICAL_NOTES_STORAGE_KEY);
      const notes = notesStr ? JSON.parse(notesStr) : [];
      
      return notes.find(note => note.id === noteId) || null;
    } catch (error) {
      console.error('Error getting medical note by ID:', error);
      throw error;
    }
  },

  /**
   * Update an existing medical note
   * @param {string} noteId - The ID of the note to update
   * @param {Object} updatedData - The updated note data
   * @returns {Promise<Object|null>} - The updated note or null if not found
   */
  updateMedicalNote: async (noteId, updatedData) => {
    try {
      const notesStr = await AsyncStorage.getItem(MEDICAL_NOTES_STORAGE_KEY);
      const notes = notesStr ? JSON.parse(notesStr) : [];
      
      const noteIndex = notes.findIndex(note => note.id === noteId);
      if (noteIndex === -1) return null;
      
      // Update the note
      const updatedNote = {
        ...notes[noteIndex],
        ...updatedData,
        updatedAt: new Date().toISOString()
      };
      
      notes[noteIndex] = updatedNote;
      
      // Save back to storage
      await AsyncStorage.setItem(MEDICAL_NOTES_STORAGE_KEY, JSON.stringify(notes));
      
      return updatedNote;
    } catch (error) {
      console.error('Error updating medical note:', error);
      throw error;
    }
  },

  /**
   * Delete a medical note
   * @param {string} noteId - The ID of the note to delete
   * @returns {Promise<boolean>} - True if deleted, false if not found
   */
  deleteMedicalNote: async (noteId) => {
    try {
      const notesStr = await AsyncStorage.getItem(MEDICAL_NOTES_STORAGE_KEY);
      const notes = notesStr ? JSON.parse(notesStr) : [];
      
      const noteIndex = notes.findIndex(note => note.id === noteId);
      if (noteIndex === -1) return false;
      
      // Remove the note
      notes.splice(noteIndex, 1);
      
      // Save back to storage
      await AsyncStorage.setItem(MEDICAL_NOTES_STORAGE_KEY, JSON.stringify(notes));
      
      return true;
    } catch (error) {
      console.error('Error deleting medical note:', error);
      throw error;
    }
  }
};
