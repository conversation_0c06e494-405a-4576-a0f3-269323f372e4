import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, RefreshControl, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { getThemeForRole, ROLE_COLORS } from '../../../config/theme';
import { Ionicons } from '@expo/vector-icons';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import DashboardChart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import { useAuth } from '../../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '../../../config/firebase';
import { format } from 'date-fns';

const AdminDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const theme = getThemeForRole('admin');
  const adminColors = ROLE_COLORS.admin;
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    activeUsers: '0',
    newPatients: '0',
    appointments: '0',
    revenue: '0'
  });

  const [tasks, setTasks] = useState([]);

  // Empty revenue data structure
  const [revenueData, setRevenueData] = useState({
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Revenue ($)']
  });

  // Empty user type data structure
  const [userTypeData, setUserTypeData] = useState({
    data: []
  });

  // Empty appointment data structure
  const [appointmentData, setAppointmentData] = useState({
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => `rgba(156, 39, 176, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Appointments']
  });

  const menuItems = [
    { label: 'Dashboard', icon: 'home', screen: 'AdminDashboard' },
    { label: 'User Management', icon: 'people', screen: 'UserManagement' },
    { label: 'Reports', icon: 'bar-chart', screen: 'Reports' },
    { label: 'Billing', icon: 'card', screen: 'Billing' },
    { label: 'Inventory', icon: 'cube', screen: 'Inventory' },
    { label: 'Support Tickets', icon: 'help-buoy', screen: 'SupportTickets' },
    { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
    { label: 'Profile', icon: 'person', screen: 'Profile' },
    { label: 'Settings', icon: 'settings', screen: 'Settings' }
  ];

  // Fetch dashboard data from Firebase
  const fetchDashboardData = async () => {
    try {
      // Fetch users data
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      // Count active users
      const activeUsers = usersSnapshot.docs.filter(doc =>
        doc.data().status === 'active' || !doc.data().status
      ).length;

      // Count new patients in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const newPatients = usersSnapshot.docs.filter(doc => {
        const userData = doc.data();
        const isPatient = userData.role?.toLowerCase() === 'patient';
        const createdAt = userData.createdAt?.toDate?.() || new Date(userData.createdAt);
        return isPatient && createdAt > thirtyDaysAgo;
      }).length;

      // Fetch appointments data
      const appointmentsCollection = collection(db, 'appointments');
      const appointmentsSnapshot = await getDocs(appointmentsCollection);
      const appointments = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Count total appointments
      const totalAppointments = appointments.length;

      // Calculate user distribution by role
      const usersByRole = {};
      usersSnapshot.docs.forEach(doc => {
        const role = doc.data().role?.toLowerCase() || 'other';
        usersByRole[role] = (usersByRole[role] || 0) + 1;
      });

      // Format user distribution for pie chart
      const userTypeChartData = Object.keys(usersByRole).map((role, index) => {
        const colors = ['#5C6BC0', '#26A69A', '#FFA726', '#EF5350', '#9E9E9E'];
        return {
          name: role.charAt(0).toUpperCase() + role.slice(1),
          value: usersByRole[role],
          color: colors[index % colors.length],
          legendFontColor: '#7F7F7F',
          legendFontSize: 12
        };
      });

      // Calculate weekly appointment distribution
      const last7Days = Array(7).fill(0).map((_, i) => {
        const d = new Date();
        d.setDate(d.getDate() - i);
        return format(d, 'yyyy-MM-dd');
      }).reverse();

      const weeklyAppointmentCounts = last7Days.map(date => {
        return appointments.filter(app => app.date === date).length;
      });

      // Update state with real data
      setStats({
        activeUsers: activeUsers.toString(),
        newPatients: newPatients.toString(),
        appointments: totalAppointments.toString(),
        revenue: '0' // Revenue data might not be available
      });

      setUserTypeData({
        data: userTypeChartData
      });

      setAppointmentData({
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
          {
            data: weeklyAppointmentCounts,
            color: (opacity = 1) => `rgba(156, 39, 176, ${opacity})`,
            strokeWidth: 2
          }
        ],
        legend: ['Appointments']
      });

      // Fetch administrative tasks if available
      // This is a placeholder - you would need to implement a tasks collection in Firebase
      const tasksCollection = collection(db, 'tasks');
      try {
        const tasksQuery = query(
          tasksCollection,
          where('assignedTo', '==', 'admin'),
          orderBy('dueDate', 'asc')
        );
        const tasksSnapshot = await getDocs(tasksQuery);

        const formattedTasks = tasksSnapshot.docs.map(doc => {
          const taskData = doc.data();
          return {
            id: doc.id,
            title: taskData.title || 'Task',
            description: taskData.description || '',
            time: taskData.dueDate ? `Due ${taskData.dueDate}` : 'No due date',
            type: 'task',
            status: taskData.status || 'pending'
          };
        });

        setTasks(formattedTasks);
      } catch (error) {
        console.log('No tasks collection available:', error);
        setTasks([]);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  return (
    <DashboardLayout
      title="Admin Dashboard"
      roleName="Administrator"
      menuItems={menuItems}
      userRole="admin"
      notifications={notifications}  // Pass notifications here
    >
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[adminColors.primary]} />
        }
      >
        <View style={styles.headerSection}>
          <LinearGradient
            colors={[adminColors.primaryLight, '#f5f5f5']}
            start={{ x: 0, y: 0 }}
            end={{ x: 2, y: 1 }}
            style={styles.headerGradient}
          >
            <View style={styles.greeting}>
              <View>
                <Text style={styles.greetingText}>
                  Welcome, {user?.firstName || 'Admin'}
                </Text>
                <Text style={styles.dateText}>
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        <View style={styles.contentSection}>
          <Text style={styles.sectionTitle}>System Overview</Text>

        <View style={styles.cardsContainer}>
          <View style={styles.cardRow}>
            <DashboardCard
              title="Active Users"
              value={stats.activeUsers}
              icon="people"
              iconColor="#5C6BC0"
              width="48%"
              onPress={() => navigation.navigate('UserManagement')}
            />
            <DashboardCard
              title="New Patients"
              value={stats.newPatients}
              icon="person-add"
              iconColor="#26A69A"
              width="48%"
              trend="up"
              trendValue="+8%"
              onPress={() => navigation.navigate('PatientRegistrations')}
            />
          </View>
          <View style={styles.cardRow}>
            <DashboardCard
              title="Appointments"
              value={stats.appointments}
              icon="calendar"
              iconColor="#FFA726"
              width="48%"
              onPress={() => navigation.navigate('AppointmentStats')}
            />
            <DashboardCard
              title="Revenue (USD)"
              value={stats.revenue}
              unit="$"
              icon="stats-chart"
              iconColor="#66BB6A"
              width="48%"
              trend="up"
              trendValue="+5%"
              onPress={() => navigation.navigate('FinanceReport')}
            />
          </View>
        </View>

        {/* Only show revenue chart if there's data */}
        {revenueData.datasets[0].data.some(value => value > 0) && (
          <DashboardChart
            title="Monthly Revenue"
            subtitle="Last 6 months"
            data={revenueData}
            type="line"
            chartConfig={{
              color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            }}
          />
        )}

        <View style={styles.chartRow}>
          {/* Only show user distribution chart if there's data */}
          {userTypeData.data.length > 0 && (
            <DashboardChart
              title="User Distribution"
              subtitle="By role"
              data={userTypeData}
              type="pie"
              height={180}
              width={width => width * 0.48}
              style={styles.halfChart}
            />
          )}

          {/* Only show appointments chart if there's data */}
          {appointmentData.datasets[0].data.some(value => value > 0) && (
            <DashboardChart
              title="Weekly Appointments"
              subtitle="Current week"
              data={appointmentData}
              type="bar"
              height={180}
              width={width => width * 0.48}
              style={styles.halfChart}
            />
          )}
        </View>

        <UpcomingList
          title="Administrative Tasks"
          data={tasks}
          onItemPress={(item) => navigation.navigate('TaskDetail', { id: item.id })}
          onViewAll={() => navigation.navigate('Tasks')}
          emptyText="No pending tasks"
        />

        <UpcomingList
          title="System Notifications"
          data={notifications}
          onItemPress={(item) => navigation.navigate('NotificationDetail', { id: item.id })}
          onViewAll={() => navigation.navigate('Notifications')}
          emptyText="No notifications"
        />

        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('AddUser')}
            >
              <View style={[styles.actionIcon, { backgroundColor: '#EDE7F6' }]}>
                <Ionicons name="person-add" size={24} color="#673AB7" />
              </View>
              <Text style={styles.actionText}>Add User</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('GenerateReport')}
            >
              <View style={[styles.actionIcon, { backgroundColor: '#E0F7FA' }]}>
                <Ionicons name="document-text" size={24} color="#00ACC1" />
              </View>
              <Text style={styles.actionText}>Generate Report</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('SystemSettings')}
            >
              <View style={[styles.actionIcon, { backgroundColor: '#FFF3E0' }]}>
                <Ionicons name="settings" size={24} color="#FF9800" />
              </View>
              <Text style={styles.actionText}>System Settings</Text>
            </TouchableOpacity>
          </View>
        </View>
        </View>
      </ScrollView>
    </DashboardLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  headerSection: {
    width: '100%',
    marginBottom: 20,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 25,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  contentSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  greeting: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  dateText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#212121',
  },
  cardsContainer: {
    marginBottom: 24,
    paddingHorizontal: 0,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  chartRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  halfChart: {
    width: '48%',
  },
  quickActions: {
    marginVertical: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  actionButton: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#424242',
    textAlign: 'center',
  },
});

export default AdminDashboard;