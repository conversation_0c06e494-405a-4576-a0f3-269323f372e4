import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../../../contexts/AuthContext';
import { firebasePrescriptionsService } from '../../../../services/firebasePrescriptionsService';
import { PATIENT_COLORS } from '../../../../config/theme';

const RecentPrescriptions = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadRecentPrescriptions();
  }, [user]);

  const loadRecentPrescriptions = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const patientId = user.uid;
      // Retrieve only prescriptions with "sent" status
      const allPrescriptions = await firebasePrescriptionsService.getPatientPrescriptions(patientId, 'sent');

      // Take only the 3 most recent prescriptions
      const recentPrescriptions = allPrescriptions.slice(0, 3);
      setPrescriptions(recentPrescriptions);
    } catch (error) {
      console.error('Error loading recent prescriptions:', error);
      setError('Failed to load prescriptions');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };



  const renderPrescriptionItem = ({ item }) => (
    <TouchableOpacity
      style={styles.prescriptionItem}
      onPress={() => navigation.navigate('PatientPrescriptions', { selectedPrescription: item })}
    >
      <View style={styles.prescriptionHeader}>
        <View style={styles.prescriptionIcon}>
          <Ionicons name="document-text" size={20} color="#fff" />
        </View>
        <View style={styles.prescriptionInfo}>
          <Text style={styles.prescriptionTitle}>Dr. {item.doctorName}</Text>
          <Text style={styles.prescriptionMedication}>
            {item.medications.length > 0
              ? `${item.medications.length} medication${item.medications.length > 1 ? 's' : ''}: ${item.medications.map(med => med.name).join(', ')}`
              : 'No medications'
            }
          </Text>
          <Text style={styles.prescriptionDate}>{formatDate(item.createdAt)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={PATIENT_COLORS.primary} />
        <Text style={styles.loadingText}>Loading prescriptions...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={24} color="#F44336" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (prescriptions.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="document-text-outline" size={48} color="#BDBDBD" />
        <Text style={styles.emptyText}>No prescriptions yet</Text>
        <Text style={styles.emptySubText}>
          Prescriptions sent by your doctors will appear here
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={prescriptions}
      renderItem={renderPrescriptionItem}
      keyExtractor={(item) => item.id}
      contentContainerStyle={styles.listContainer}
      scrollEnabled={false}
    />
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 8,
    color: '#757575',
    fontSize: 14,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginTop: 8,
    color: '#F44336',
    fontSize: 14,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#757575',
  },
  emptySubText: {
    marginTop: 4,
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
  },
  listContainer: {
    paddingVertical: 8,
  },
  prescriptionItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prescriptionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: PATIENT_COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  prescriptionInfo: {
    flex: 1,
    paddingRight: 8,
  },
  prescriptionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  prescriptionMedication: {
    fontSize: 13,
    color: '#757575',
    marginTop: 2,
  },
  prescriptionDate: {
    fontSize: 12,
    color: '#9E9E9E',
    marginTop: 2,
  },

});

export default RecentPrescriptions;
