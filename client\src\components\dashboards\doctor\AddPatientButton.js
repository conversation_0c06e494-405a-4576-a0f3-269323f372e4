import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PatientScannerModal from '../../scanner/PatientScannerModal';

const AddPatientButton = ({ onPatientAdded }) => {
  const [showScanner, setShowScanner] = useState(false);

  const handlePatientAdded = (patient) => {
    if (onPatientAdded) {
      onPatientAdded(patient);
    }
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: 'rgb(212, 0, 131)' }]}
        onPress={() => setShowScanner(true)}
      >
        <View style={styles.buttonContent}>
          <Ionicons name="person-add" size={20} color="#fff" />
          <Text style={styles.buttonText}>Add Patient</Text>
        </View>
      </TouchableOpacity>

      <PatientScannerModal
        visible={showScanner}
        onClose={() => setShowScanner(false)}
        onSuccess={handlePatientAdded}
        scannerTitle="Add New Patient"
      />
    </>
  );
};

const styles = StyleSheet.create({
  addButton: {
    borderRadius: 8,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default AddPatientButton;