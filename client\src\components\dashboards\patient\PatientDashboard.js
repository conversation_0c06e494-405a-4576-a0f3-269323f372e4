import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Animated,
  ScrollView,
  Platform,
  ActivityIndicator
} from 'react-native';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import UpcomingList from '../UpcomingList';
import { useAuth } from '../../../contexts/AuthContext';
import { useVitals } from '../../../contexts/VitalsContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import { useNavigation } from '@react-navigation/native';
import { localMedicationsService } from '../../../services/localStorageService';
import { Ionicons } from '@expo/vector-icons';
import MedicationReminders from './medications/MedicationReminders';
import RecentPrescriptions from './prescriptions/RecentPrescriptions';
import { LinearGradient } from 'expo-linear-gradient';
import { ROLE_COLORS } from '../../../config/theme';

// Get patient colors for use in styles
const PATIENT_COLORS = ROLE_COLORS.patient;


const PatientDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const { getAllVitals } = useVitals();
  const { getUpcomingAppointments, fetchAppointments } = useAppointments();
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [healthStats, setHealthStats] = useState({
    heartRate: '--',
    bloodPressure: '--/--',
    bloodGlucose: '--',
    weight: '--'
  });
  const [loadingVitals, setLoadingVitals] = useState(true);
  const [vitalStatus, setVitalStatus] = useState({
    heartRate: 'normal',
    bloodPressure: 'normal',
    bloodGlucose: 'normal',
    weight: 'normal'
  });
  const [appointments, setAppointments] = useState([]);
  const [medications, setMedications] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Game states
  const [numberGameActive, setNumberGameActive] = useState(false);
  const [numberGameStep, setNumberGameStep] = useState('intro'); // 'intro', 'memorize', 'recall', 'result'
  const [numberSequence, setNumberSequence] = useState([4, 7, 2, 9, 5]);
  const [userNumberInput, setUserNumberInput] = useState([]);
  const [numberGameResult, setNumberGameResult] = useState(null);

  const [visualGameActive, setVisualGameActive] = useState(false);
  const [visualGameStep, setVisualGameStep] = useState('intro'); // 'intro', 'memorize', 'recall', 'result'
  const [highlightedCells, setHighlightedCells] = useState([2, 5, 7]);
  const [userCellsSelection, setUserCellsSelection] = useState([]);
  const [visualGameResult, setVisualGameResult] = useState(null);





  const menuItems = [
    { label: 'Dashboard', icon: 'home', screen: 'PatientDashboard' },
    { label: 'Appointments', icon: 'calendar', screen: 'Appointments' },
    { label: 'Medications', icon: 'medkit', screen: 'Medications' },
    { label: 'Prescriptions', icon: 'document', screen: 'PatientPrescriptions' },
    { label: 'My Doctors', icon: 'medical', screen: 'MyDoctors' },
    { label: 'My Locations', icon: 'map', screen: 'MapScreen' },
    { label: 'Guidance', icon: 'navigate', screen: 'PatientGuidance' },
    { label: 'Messages', icon: 'chatbubble', screen: 'Messages' },
    { label: 'Payments', icon: 'card', screen: 'Payments' },
    { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
    { label: 'Profile', icon: 'person', screen: 'Profile' },
    { label: 'Settings', icon: 'settings', screen: 'Settings' }
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Reload vitals, medications, and appointments
      await loadLatestVitals();
      await loadMedications();
      await loadAppointments();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Function to load medications
  const loadMedications = async () => {
    try {
      const patientId = user?.uid || 'local-user';
      const medsData = await localMedicationsService.getPatientMedications(patientId);

      // Format medications for the UpcomingList component
      const formattedMedications = medsData.map(med => ({
        id: med.id,
        title: med.name,
        description: `${med.dosage}, ${med.frequency}`,
        time: med.nextDose || 'As prescribed',
        type: 'medication',
        status: 'active'
      }));

      setMedications(formattedMedications);
    } catch (error) {
      console.error('Error loading medications:', error);
    }
  };

  // Function to load appointments from Firebase
  const loadAppointments = async () => {
    try {
      console.log('Loading appointments...');
      console.log('Running on platform:', Platform.OS);
      console.log('Is running in development:', __DEV__);
      console.log('Current user:', user?.uid, user?.email);

      // Refresh appointments from Firebase
      const fetchedAppointments = await fetchAppointments(true);
      console.log('Appointments fetched directly:', fetchedAppointments?.length || 0);

      // Attendre un court instant pour s'assurer que le contexte est mis à jour
      await new Promise(resolve => setTimeout(resolve, 500));

      // Get upcoming appointments from context after fetching
      let upcomingAppointments = getUpcomingAppointments();
      console.log('Upcoming appointments from context:', upcomingAppointments);

      // Si le contexte est vide mais que nous avons des rendez-vous, utilisons les rendez-vous récupérés directement
      if ((!upcomingAppointments || upcomingAppointments.length === 0) && fetchedAppointments && fetchedAppointments.length > 0) {
        console.log('Context is empty but we have fetched appointments, using them directly');
        // Filtrer les rendez-vous à venir (non annulés et dans le futur)
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        upcomingAppointments = fetchedAppointments.filter(appointment => {
          // Vérifier si le rendez-vous est annulé
          if (appointment.status?.toLowerCase() === 'cancelled') {
            return false;
          }

          try {
            // Essayer de parser la date
            const dateParts = appointment.date.split('-');
            if (dateParts.length !== 3) return false;

            const year = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1;
            const day = parseInt(dateParts[2], 10);

            const appointmentDate = new Date(year, month, day);
            return appointmentDate >= today;
          } catch (error) {
            console.error('Error parsing date:', error);
            return false;
          }
        });
      }

      // Debugging: Log the raw appointments data
      console.log('Raw appointments data from context:', appointments);

      if (upcomingAppointments.length === 0) {
        console.log('No upcoming appointments found. This could be because:');
        console.log('1. There are no appointments in Firebase');
        console.log('2. All appointments are in the past');
        console.log('3. All appointments are cancelled');
        console.log('4. There is an issue with date parsing');
      }

      // Format appointments for the UpcomingList component
      const formattedAppointments = upcomingAppointments.map(appointment => {
        console.log('Formatting appointment for display:', appointment);

        // Parse the appointment date
        let appointmentDate = new Date();

        try {
          // Parse the date part
          const dateParts = appointment.date.split('-');
          if (dateParts.length === 3) {
            const year = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
            const day = parseInt(dateParts[2], 10);

            // Create a new date object with the date parts
            appointmentDate = new Date(year, month, day);

            // Handle 12-hour format with AM/PM for the time
            if (appointment.time.includes('AM') || appointment.time.includes('PM')) {
              // Extract hours and minutes from time string (e.g., "10:00 AM")
              const timeParts = appointment.time.replace(/\s*(AM|PM)\s*$/i, '').split(':');
              let hours = parseInt(timeParts[0], 10);
              const minutes = parseInt(timeParts[1], 10);

              // Adjust hours for PM
              if (appointment.time.includes('PM') && hours < 12) {
                hours += 12;
              }
              // Adjust for 12 AM
              if (appointment.time.includes('AM') && hours === 12) {
                hours = 0;
              }

              // Set the time part
              appointmentDate.setHours(hours, minutes, 0, 0);
            } else {
              // Handle 24-hour format
              const timeParts = appointment.time.split(':');
              if (timeParts.length >= 2) {
                const hours = parseInt(timeParts[0], 10);
                const minutes = parseInt(timeParts[1], 10);
                appointmentDate.setHours(hours, minutes, 0, 0);
              }
            }
          }
        } catch (error) {
          console.error('Error parsing appointment date:', error);
          // Use current date as fallback
          appointmentDate = new Date();
        }

        const now = new Date();

        // Format the time display
        let timeDisplay;
        const dayDiff = Math.round((appointmentDate - now) / (24 * 60 * 60 * 1000));

        if (dayDiff === 0) {
          timeDisplay = `Today, ${appointment.time}`;
        } else if (dayDiff === 1) {
          timeDisplay = `Tomorrow, ${appointment.time}`;
        } else {
          timeDisplay = `${appointmentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}, ${appointment.time}`;
        }

        // Format the appointment information for display
        return {
          id: appointment.id,
          title: appointment.doctor,
          description: `${appointment.specialty || 'Medical Appointment'}${appointment.reason ? ` - ${appointment.reason}` : ''}`,
          time: timeDisplay,
          type: 'appointment',
          status: appointment.status?.toLowerCase() || 'pending',
          // Add additional information that might be useful
          // Use the properly formatted date string that matches Firebase format
          appointmentDate: appointment.date,
          // Also store the date object for the UI to use directly
          appointmentDateObj: appointmentDate,
          appointmentTime: appointment.time,
          location: appointment.location || 'Neuro Care Medical Center',
          duration: appointment.duration ? `${appointment.duration} min` : '30 min',
          // Store the actual date object for sorting
          dateObject: appointmentDate
        };
      });

      // Sort appointments by date (from earliest to latest)
      formattedAppointments.sort((a, b) => {
        // First try to sort by the dateObject
        if (a.dateObject && b.dateObject) {
          console.log(`Comparing dates: ${a.appointmentDate} (${a.dateObject}) vs ${b.appointmentDate} (${b.dateObject})`);
          return a.dateObject.getTime() - b.dateObject.getTime();
        }

        // Fallback to sorting by appointmentDate
        // Parse dates in YYYY-MM-DD format for proper comparison
        const dateA = new Date(a.appointmentDate);
        const dateB = new Date(b.appointmentDate);

        if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
          console.log(`Comparing parsed dates: ${a.appointmentDate} (${dateA}) vs ${b.appointmentDate} (${dateB})`);

          // If dates are the same, sort by time
          if (dateA.getTime() === dateB.getTime()) {
            return a.appointmentTime.localeCompare(b.appointmentTime);
          }

          return dateA.getTime() - dateB.getTime();
        }

        // Last resort: direct string comparison
        console.log(`Comparing date strings: ${a.appointmentDate} vs ${b.appointmentDate}`);

        // Split the date strings and compare year, month, and day
        const [yearA, monthA, dayA] = a.appointmentDate.split('-').map(Number);
        const [yearB, monthB, dayB] = b.appointmentDate.split('-').map(Number);

        // Compare years
        if (yearA !== yearB) return yearA - yearB;
        // Compare months
        if (monthA !== monthB) return monthA - monthB;
        // Compare days
        if (dayA !== dayB) return dayA - dayB;

        // If dates are identical, sort by time
        return a.appointmentTime.localeCompare(b.appointmentTime);
      });

      console.log('Sorted appointments:', formattedAppointments.map(a => ({
        id: a.id,
        date: a.appointmentDate,
        time: a.appointmentTime
      })));

      // Vérifier si nous avons des rendez-vous à afficher
      if (formattedAppointments.length > 0) {
        console.log(`Setting ${formattedAppointments.length} appointments for display`);
        setAppointments(formattedAppointments);
      } else {
        console.log('No appointments to display');
        setAppointments([]);
      }
    } catch (error) {
      console.error('Error loading appointments from Firebase:', error);
    }
  };

  // Function to navigate to RecordVitals screen
  const navigateToRecordVitals = () => {
    navigation.navigate('RecordVitals');
  };

  // Function to navigate to HealthRecords screen
  const navigateToHealthRecords = () => {
    navigation.navigate('HealthRecords');
  };

  // Function to handle the request appointment button
  const handleRequestAppointment = () => {
    // Navigate to the Appointments screen
    navigation.navigate('Appointments', { openRequestModal: true });
  };

  // Function to determine heart rate status
  const getHeartRateStatus = (rate) => {
    if (!rate || rate === '--') return 'normal';
    const numRate = parseInt(rate);
    if (numRate < 60) return 'low';
    if (numRate > 100) return 'high';
    return 'normal';
  };

  // Function to determine blood pressure status
  const getBloodPressureStatus = (bp) => {
    if (!bp || bp === '--/--') return 'normal';
    const [systolic, diastolic] = bp.split('/').map(v => parseInt(v));
    if (systolic > 140 || diastolic > 90) return 'high';
    if (systolic < 90 || diastolic < 60) return 'low';
    return 'normal';
  };

  // Function to determine blood glucose status
  const getBloodGlucoseStatus = (glucose) => {
    if (!glucose || glucose === '--') return 'normal';
    const numGlucose = parseInt(glucose);
    if (numGlucose > 140) return 'high';
    if (numGlucose < 70) return 'low';
    return 'normal';
  };

  // Function to determine weight status
  const getWeightStatus = (weight) => {
    if (!weight || weight === '--') return 'normal';
    // This is a simplified example - in reality, you'd use BMI or other metrics
    return 'normal';
  };

  // Animation effect
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, []);

  // Function to load the latest vitals from Firebase
  const loadLatestVitals = async () => {
    setLoadingVitals(true);
    try {
      // Get all vitals from Firebase using VitalsContext
      const allVitals = await getAllVitals();

      // Get the most recent vital of each type for the health stats
      const newHealthStats = { ...healthStats };
      const newVitalStatus = { ...vitalStatus };
      const newRecentActivities = [];

      // Process heart rate readings
      if (allVitals.heartRate && allVitals.heartRate.length > 0) {
        // Sort by timestamp (newest first)
        const sortedHeartRates = [...allVitals.heartRate].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestHeartRate = sortedHeartRates[0];
        newHealthStats.heartRate = latestHeartRate.values.value.toString();
        newVitalStatus.heartRate = getHeartRateStatus(newHealthStats.heartRate);

        // Add all heart rate readings to recent activities
        sortedHeartRates.forEach(reading => {
          newRecentActivities.push({
            type: 'heartRate',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: 'heart',
            color: '#EA4335'
          });
        });
      }

      // Process blood pressure readings
      if (allVitals.bloodPressure && allVitals.bloodPressure.length > 0) {
        // Sort by timestamp (newest first)
        const sortedBP = [...allVitals.bloodPressure].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestBP = sortedBP[0];
        const bp = latestBP.values;
        newHealthStats.bloodPressure = `${bp.systolic}/${bp.diastolic}`;
        newVitalStatus.bloodPressure = getBloodPressureStatus(newHealthStats.bloodPressure);

        // Add all blood pressure readings to recent activities
        sortedBP.forEach(reading => {
          const bpValue = `${reading.values.systolic}/${reading.values.diastolic}`;
          newRecentActivities.push({
            type: 'bloodPressure',
            value: bpValue,
            timestamp: reading.timestamp,
            icon: 'fitness',
            color: '#4285F4'
          });
        });
      }

      // Process blood glucose readings
      if (allVitals.bloodGlucose && allVitals.bloodGlucose.length > 0) {
        // Sort by timestamp (newest first)
        const sortedGlucose = [...allVitals.bloodGlucose].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestGlucose = sortedGlucose[0];
        newHealthStats.bloodGlucose = latestGlucose.values.value.toString();
        newVitalStatus.bloodGlucose = getBloodGlucoseStatus(newHealthStats.bloodGlucose);

        // Add all glucose readings to recent activities
        sortedGlucose.forEach(reading => {
          newRecentActivities.push({
            type: 'bloodGlucose',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: 'water',
            color: '#FBBC05'
          });
        });
      }

      // Process weight readings
      if (allVitals.weight && allVitals.weight.length > 0) {
        // Sort by timestamp (newest first)
        const sortedWeight = [...allVitals.weight].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestWeight = sortedWeight[0];
        newHealthStats.weight = latestWeight.values.value.toString();
        newVitalStatus.weight = getWeightStatus(newHealthStats.weight);

        // Add all weight readings to recent activities
        sortedWeight.forEach(reading => {
          newRecentActivities.push({
            type: 'weight',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: 'body',
            color: '#34A853'
          });
        });
      }

      // Sort all activities by timestamp (newest first)
      newRecentActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      setHealthStats(newHealthStats);
      setVitalStatus(newVitalStatus);
      setRecentActivities(newRecentActivities);
    } catch (error) {
      console.error('Error loading vitals from Firebase:', error);
    } finally {
      setLoadingVitals(false);
    }
  };

  // Load initial data
  useEffect(() => {
    loadLatestVitals();
    loadMedications();
    loadAppointments();
  }, [user]);

  return (
    <>
      <DashboardLayout
        title="Patient Dashboard"
        roleName="Patient"
        menuItems={menuItems}
        userRole="patient"
        notifications={notifications}
      >
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[PATIENT_COLORS.primary]} />
        }
      >
        {/* Modern Header with Enhanced Gradient */}
        <View style={styles.headerSection}>
          <LinearGradient
            colors={[
              PATIENT_COLORS.primary,
              PATIENT_COLORS.primaryLight,
              'rgba(248, 249, 250, 0.95)'
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerGradient}
          >
            <Animated.View
              style={[
                styles.greeting,
                { opacity: fadeAnim }
              ]}
            >
              <View>
                <Animated.Text
                  style={[
                    styles.greetingText,
                    {
                      transform: [
                        { translateY: fadeAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })}
                      ]
                    }
                  ]}
                >
                  Hello, {user?.firstName || 'Patient'}
                </Animated.Text>
                <Animated.Text
                  style={[
                    styles.dateText,
                    {
                      transform: [
                        { translateY: fadeAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [15, 0]
                        })}
                      ]
                    }
                  ]}
                >
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Animated.Text>
              </View>
            </Animated.View>
          </LinearGradient>
        </View>

        {/* Health Summary Section with Improved UI */}
        {/* Quick Action Buttons Grid */}
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={handleRequestAppointment}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: '#FF9800', shadowColor: 'rgba(255, 152, 0, 0.6)' }]}>
              <Ionicons name="calendar" size={24} color="#fff" style={{ textShadowColor: 'rgba(0,0,0,0.5)', textShadowOffset: {width: 1, height: 1}, textShadowRadius: 3 }} />
            </View>
            <Text style={styles.quickActionText}>Request Appointment</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('MapScreen')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: '#4DB6AC', shadowColor: 'rgba(77, 182, 172, 0.6)' }]}>
              <Ionicons name="map" size={24} color="#fff" style={{ textShadowColor: 'rgba(0,0,0,0.5)', textShadowOffset: {width: 1, height: 1}, textShadowRadius: 3 }} />
            </View>
            <Text style={styles.quickActionText}>My Locations</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={navigateToRecordVitals}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: '#EA4335', shadowColor: 'rgba(234, 67, 53, 0.6)' }]}>
              <Ionicons name="pulse" size={24} color="#fff" style={{ textShadowColor: 'rgba(0,0,0,0.5)', textShadowOffset: {width: 1, height: 1}, textShadowRadius: 3 }} />
            </View>
            <Text style={styles.quickActionText}>Record</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('PatientGuidance')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: '#9C27B0', shadowColor: 'rgba(156, 39, 176, 0.6)' }]}>
              <Ionicons name="navigate" size={24} color="#fff" style={{ textShadowColor: 'rgba(0,0,0,0.5)', textShadowOffset: {width: 1, height: 1}, textShadowRadius: 3 }} />
            </View>
            <Text style={styles.quickActionText}>Guidance</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Medications')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: '#4CAF50', shadowColor: 'rgba(76, 175, 80, 0.6)' }]}>
              <Ionicons name="medkit" size={24} color="#fff" style={{ textShadowColor: 'rgba(0,0,0,0.5)', textShadowOffset: {width: 1, height: 1}, textShadowRadius: 3 }} />
            </View>
            <Text style={styles.quickActionText}>Add Medication</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.contentSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="pulse" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>Your Health Summary</Text>
            </View>
            <View style={styles.sectionActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={navigateToHealthRecords}
              >
                <Ionicons name="stats-chart-outline" size={14} color="#FF9800" />
                <Text style={styles.actionButtonText}>History</Text>
              </TouchableOpacity>

            </View>
          </View>

        {/* Health Cards with Loading State */}
        {loadingVitals ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={PATIENT_COLORS.primary} />
            <Text style={styles.loadingText}>Loading your health data...</Text>
          </View>
        ) : (
          <View style={styles.cardsContainer}>
            <View style={styles.cardRow}>
              <DashboardCard
                title="Heart Rate"
                value={healthStats.heartRate}
                unit="bpm"
                icon="heart"
                iconColor="#EA4335"
                width="48%"
                status={vitalStatus.heartRate}
                gradientStart="#FFEBEE"
                gradientEnd="#FFCDD2"
              />
              <DashboardCard
                title="Blood Pressure"
                value={healthStats.bloodPressure}
                unit="mmHg"
                icon="fitness"
                iconColor="#4285F4"
                width="48%"
                status={vitalStatus.bloodPressure}
                gradientStart="#E3F2FD"
                gradientEnd="#BBDEFB"
              />
            </View>
            <View style={styles.cardRow}>
              <DashboardCard
                title="Blood Glucose"
                value={healthStats.bloodGlucose}
                unit="mg/dL"
                icon="water"
                iconColor="#FBBC05"
                width="48%"
                status={vitalStatus.bloodGlucose}
                gradientStart="#FFF8E1"
                gradientEnd="#FFECB3"
              />
              <DashboardCard
                title="Weight"
                value={healthStats.weight}
                unit="kg"
                icon="body"
                iconColor="#34A853"
                width="48%"
                status={vitalStatus.weight}
                gradientStart="#E8F5E9"
                gradientEnd="#C8E6C9"
              />
            </View>
          </View>
        )}
        </View>

        {/* Recent Activity Section with Enhanced UI */}
        <View style={styles.activitySection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="time" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>Recent Activity</Text>
            </View>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('HealthRecords')}
            >
              <Ionicons name="list-outline" size={14} color="#FF9800" />
              <Text style={styles.actionButtonText}>View All</Text>
            </TouchableOpacity>
          </View>
          {recentActivities.length === 0 ? (
            <View style={styles.emptyActivity}>
              <Ionicons name="calendar-outline" size={48} color="#BDBDBD" />
              <Text style={styles.emptyActivityText}>No recent activity</Text>
            </View>
          ) : (
            <View style={styles.activityItems}>
              {recentActivities.slice(0, 3).map((activity, index) => {
                const date = new Date(activity.timestamp);

                // Format date in a more readable way
                const today = new Date();
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);

                let dateLabel;
                if (date.toDateString() === today.toDateString()) {
                  dateLabel = 'Today';
                } else if (date.toDateString() === yesterday.toDateString()) {
                  dateLabel = 'Yesterday';
                } else {
                  dateLabel = date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric'
                  });
                }

                // Format time
                const timeString = date.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit'
                });

                return (
                  <View key={index} style={styles.activityItem}>
                    <View style={[styles.activityIcon, { backgroundColor: activity.color }]}>
                      <Ionicons name={activity.icon} size={20} color="#fff" />
                    </View>
                    <View style={styles.activityContent}>
                      <Text style={styles.activityTitle}>
                        {activity.type === 'heartRate' && `Heart Rate: ${activity.value} bpm`}
                        {activity.type === 'bloodPressure' && `Blood Pressure: ${activity.value} mmHg`}
                        {activity.type === 'bloodGlucose' && `Blood Glucose: ${activity.value} mg/dL`}
                        {activity.type === 'weight' && `Weight: ${activity.value} kg`}
                      </Text>
                      <View style={styles.activityTimeContainer}>
                        <Ionicons name="calendar-outline" size={12} color="#757575" style={styles.activityTimeIcon} />
                        <Text style={styles.activityTime}>{dateLabel}</Text>
                        <Ionicons name="time-outline" size={12} color="#757575" style={[styles.activityTimeIcon, {marginLeft: 8}]} />
                        <Text style={styles.activityTime}>{timeString}</Text>
                      </View>
                    </View>
                  </View>
                );
              })}
            </View>
          )}
        </View>

        {/* Appointments and Medications Section */}
        <View style={styles.listsContainer}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="calendar" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>Upcoming Appointments</Text>
            </View>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Appointments')}
            >
              <Ionicons name="calendar-outline" size={14} color="#FF9800" />
              <Text style={styles.actionButtonText}>View All</Text>
            </TouchableOpacity>
          </View>

          {/* Appointments List with Loading State */}
          {refreshing ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={PATIENT_COLORS.primary} />
              <Text style={styles.loadingText}>Loading appointments...</Text>
            </View>
          ) : (
            <UpcomingList
              title=""
              data={appointments}
              onItemPress={(item) => navigation.navigate('Appointments', { id: item.id })}
              onViewAll={() => navigation.navigate('Appointments')}
              emptyText="No upcoming appointments"
            />
          )}

          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="medkit" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>Medications</Text>
            </View>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Medications')}
            >
              <Ionicons name="list-outline" size={14} color="#FF9800" />
              <Text style={styles.actionButtonText}>View All</Text>
            </TouchableOpacity>
          </View>

          <UpcomingList
            title=""
            data={medications}
            onItemPress={(item) => navigation.navigate('MedicationDetail', { id: item.id })}
            onViewAll={() => navigation.navigate('Medications')}
            emptyText="No current medications"
          />
        </View>

        {/* Medication Reminders Section with Enhanced UI */}
        <View style={styles.medicationRemindersSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="alarm" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>Medication Reminders</Text>
            </View>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Medications')}
            >
              <Ionicons name="list-outline" size={14} color="#FF9800" />
              <Text style={styles.actionButtonText}>View All</Text>
            </TouchableOpacity>
          </View>
          <MedicationReminders />
        </View>

        {/* Prescriptions Section */}
        <View style={styles.prescriptionsSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="document-text" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>My Prescriptions</Text>
            </View>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('PatientPrescriptions')}
            >
              <Ionicons name="list-outline" size={14} color="#FF9800" />
              <Text style={styles.actionButtonText}>View All</Text>
            </TouchableOpacity>
          </View>
          <RecentPrescriptions />
        </View>



        {/* Health Tips Section with Enhanced UI */}
        <View style={styles.tipsSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIcon}>
                <Ionicons name="bulb" size={18} color={PATIENT_COLORS.primary} />
              </View>
              <Text style={styles.sectionTitle}>Health Tips</Text>
            </View>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="refresh-outline" size={14} color="#FF9800" />
              <Text style={styles.actionButtonText}>Refresh</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.tipsWrapper}>
            <View style={styles.tipCard}>
              <View style={[styles.tipIconContainer, { backgroundColor: '#FFF8E1' }]}>
                <Ionicons name="water-outline" size={20} color="#FBBC05" />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>Stay Hydrated</Text>
                <Text style={styles.tipText}>Drink at least 8 glasses of water daily to maintain good blood pressure.</Text>
              </View>
            </View>

            <View style={styles.tipCard}>
              <View style={[styles.tipIconContainer, { backgroundColor: '#E3F2FD' }]}>
                <Ionicons name="walk-outline" size={20} color="#4285F4" />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>Daily Exercise</Text>
                <Text style={styles.tipText}>A 30-minute walk each day can significantly improve your heart health.</Text>
              </View>
            </View>

            <View style={styles.tipCard}>
              <View style={[styles.tipIconContainer, { backgroundColor: '#E8F5E9' }]}>
                <Ionicons name="nutrition-outline" size={20} color="#34A853" />
              </View>
              <View style={styles.tipContent}>
                <Text style={styles.tipTitle}>Balanced Diet</Text>
                <Text style={styles.tipText}>Include fruits and vegetables in every meal for essential nutrients.</Text>
              </View>
            </View>
          </View>
        </View>



        {/* Memory Exercise Section */}
        <View style={styles.memoryExerciseSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Memory Exercises</Text>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="refresh-outline" size={16} color={PATIENT_COLORS.primary} />
              <Text style={[styles.actionButtonText, {color: PATIENT_COLORS.primary}]}>New</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.exerciseCard}>
            <View style={styles.exerciseHeader}>
              <View style={styles.exerciseIconContainer}>
                <Ionicons name="school-outline" size={24} color="#fff" />
              </View>
              <Text style={styles.exerciseTitle}>Number Sequence</Text>
            </View>

            {!numberGameActive && (
              <>
                <Text style={styles.exerciseInstructions}>
                  Memorize a sequence of numbers and repeat it backwards.
                </Text>

                <View style={styles.exerciseActions}>
                  <TouchableOpacity
                    style={styles.exerciseButton}
                    onPress={() => {
                      // Generate random sequence
                      const newSequence = Array(5).fill(0).map(() => Math.floor(Math.random() * 9) + 1);
                      setNumberSequence(newSequence);
                      setNumberGameActive(true);
                      setNumberGameStep('memorize');
                      setUserNumberInput([]);
                      setNumberGameResult(null);

                      // Auto-advance to recall after 5 seconds
                      setTimeout(() => {
                        setNumberGameStep('recall');
                      }, 5000);
                    }}
                  >
                    <Text style={styles.exerciseButtonText}>Start Game</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}

            {numberGameActive && numberGameStep === 'memorize' && (
              <>
                <Text style={styles.exerciseInstructions}>
                  Memorize this sequence - you'll need to repeat it backwards:
                </Text>

                <View style={styles.numberSequence}>
                  {numberSequence.map((num, index) => (
                    <View key={index} style={styles.numberBubble}>
                      <Text style={styles.numberText}>{num}</Text>
                    </View>
                  ))}
                </View>

                <Text style={styles.exerciseTimer}>Memorizing time: 5 seconds</Text>
              </>
            )}

            {numberGameActive && numberGameStep === 'recall' && (
              <>
                <Text style={styles.exerciseInstructions}>
                  Now enter the sequence in reverse order:
                </Text>

                <View style={styles.numberSequence}>
                  {userNumberInput.map((num, index) => (
                    <View key={index} style={styles.numberBubble}>
                      <Text style={styles.numberText}>{num}</Text>
                    </View>
                  ))}
                  {userNumberInput.length < numberSequence.length && (
                    <View style={[styles.numberBubble, styles.emptyBubble]}>
                      <Text style={styles.numberText}>?</Text>
                    </View>
                  )}
                </View>

                <View style={styles.numberPad}>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                    <TouchableOpacity
                      key={num}
                      style={styles.numberPadButton}
                      onPress={() => {
                        if (userNumberInput.length < numberSequence.length) {
                          setUserNumberInput([...userNumberInput, num]);

                          // Check if this was the last number
                          if (userNumberInput.length === numberSequence.length - 1) {
                            // Compare results
                            const reversedSequence = [...numberSequence].reverse();
                            const userSequence = [...userNumberInput, num];

                            let correct = 0;
                            for (let i = 0; i < reversedSequence.length; i++) {
                              if (reversedSequence[i] === userSequence[i]) {
                                correct++;
                              }
                            }

                            const accuracy = Math.round((correct / reversedSequence.length) * 100);
                            setNumberGameResult({ accuracy, correct, total: reversedSequence.length });

                            // Show results after a short delay
                            setTimeout(() => {
                              setNumberGameStep('result');
                            }, 500);
                          }
                        }
                      }}
                    >
                      <Text style={styles.numberPadButtonText}>{num}</Text>
                    </TouchableOpacity>
                  ))}
                  <TouchableOpacity
                    style={[styles.numberPadButton, styles.clearButton]}
                    onPress={() => setUserNumberInput([])}
                  >
                    <Text style={styles.clearButtonText}>Clear</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}

            {numberGameActive && numberGameStep === 'result' && (
              <>
                <Text style={styles.resultTitle}>
                  {numberGameResult.accuracy >= 80 ? 'Great job!' : 'Nice try!'}
                </Text>

                <View style={styles.resultContainer}>
                  <Text style={styles.resultText}>Your accuracy: {numberGameResult.accuracy}%</Text>
                  <Text style={styles.resultText}>Correct numbers: {numberGameResult.correct}/{numberGameResult.total}</Text>

                  <View style={styles.resultSequenceContainer}>
                    <Text style={styles.resultLabel}>Correct sequence:</Text>
                    <View style={styles.numberSequence}>
                      {[...numberSequence].reverse().map((num, index) => (
                        <View key={index} style={[styles.numberBubble, styles.smallBubble]}>
                          <Text style={styles.numberText}>{num}</Text>
                        </View>
                      ))}
                    </View>

                    <Text style={styles.resultLabel}>Your sequence:</Text>
                    <View style={styles.numberSequence}>
                      {userNumberInput.map((num, index) => (
                        <View
                          key={index}
                          style={[
                            styles.numberBubble,
                            styles.smallBubble,
                            num === [...numberSequence].reverse()[index] ? styles.correctBubble : styles.incorrectBubble
                          ]}
                        >
                          <Text style={styles.numberText}>{num}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </View>

                <View style={styles.exerciseActions}>
                  <TouchableOpacity
                    style={styles.exerciseButton}
                    onPress={() => {
                      setNumberGameActive(false);
                      setNumberGameStep('intro');
                    }}
                  >
                    <Text style={styles.exerciseButtonText}>Play Again</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>

          <View style={styles.performanceCard}>
            <Text style={styles.performanceTitle}>Your Daily Performance</Text>
            <View style={styles.performanceStats}>
              <View style={styles.performanceStat}>
                <Text style={styles.performanceValue}>85%</Text>
                <Text style={styles.performanceLabel}>Accuracy</Text>
              </View>
              <View style={styles.performanceDivider} />
              <View style={styles.performanceStat}>
                <Text style={styles.performanceValue}>3</Text>
                <Text style={styles.performanceLabel}>Exercises</Text>
              </View>
              <View style={styles.performanceDivider} />
              <View style={styles.performanceStat}>
                <Text style={styles.performanceValue}>12</Text>
                <Text style={styles.performanceLabel}>Day Streak</Text>
              </View>
            </View>
          </View>

          <View style={styles.exerciseCard}>
            <View style={styles.exerciseHeader}>
              <View style={[styles.exerciseIconContainer, { backgroundColor: '#FBBC05' }]}>
                <Ionicons name="images-outline" size={24} color="#fff" />
              </View>
              <Text style={styles.exerciseTitle}>Visual Memory</Text>
            </View>

            {!visualGameActive && (
              <>
                <Text style={styles.exerciseInstructions}>
                  Remember the positions of the highlighted squares in the grid.
                </Text>

                <View style={styles.exerciseActions}>
                  <TouchableOpacity
                    style={styles.exerciseButton}
                    onPress={() => {
                      // Generate random highlighted cells
                      const numCells = 3;
                      const newHighlightedCells = [];
                      while (newHighlightedCells.length < numCells) {
                        const cell = Math.floor(Math.random() * 9);
                        if (!newHighlightedCells.includes(cell)) {
                          newHighlightedCells.push(cell);
                        }
                      }

                      setHighlightedCells(newHighlightedCells);
                      setVisualGameActive(true);
                      setVisualGameStep('memorize');
                      setUserCellsSelection([]);
                      setVisualGameResult(null);

                      // Auto-advance to recall after 3 seconds
                      setTimeout(() => {
                        setVisualGameStep('recall');
                      }, 3000);
                    }}
                  >
                    <Text style={styles.exerciseButtonText}>Start Game</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}

            {visualGameActive && visualGameStep === 'memorize' && (
              <>
                <Text style={styles.exerciseInstructions}>
                  Memorize these highlighted squares:
                </Text>

                <View style={styles.memoryGrid}>
                  {Array(9).fill(0).map((_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.gridCell,
                        highlightedCells.includes(index) && styles.highlightedCell
                      ]}
                    />
                  ))}
                </View>

                <Text style={styles.exerciseTimer}>Memorizing time: 3 seconds</Text>
              </>
            )}

            {visualGameActive && visualGameStep === 'recall' && (
              <>
                <Text style={styles.exerciseInstructions}>
                  Tap on the squares that were highlighted:
                </Text>

                <View style={styles.memoryGrid}>
                  {Array(9).fill(0).map((_, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.gridCell,
                        userCellsSelection.includes(index) && styles.selectedCell
                      ]}
                      onPress={() => {
                        if (userCellsSelection.includes(index)) {
                          // Remove if already selected
                          setUserCellsSelection(userCellsSelection.filter(cell => cell !== index));
                        } else if (userCellsSelection.length < highlightedCells.length) {
                          // Add if not at max selections
                          const newSelection = [...userCellsSelection, index];
                          setUserCellsSelection(newSelection);

                          // Check if this was the last selection
                          if (newSelection.length === highlightedCells.length) {
                            // Compare results
                            let correct = 0;
                            for (const cell of newSelection) {
                              if (highlightedCells.includes(cell)) {
                                correct++;
                              }
                            }

                            const accuracy = Math.round((correct / highlightedCells.length) * 100);
                            setVisualGameResult({ accuracy, correct, total: highlightedCells.length });

                            // Show results after a short delay
                            setTimeout(() => {
                              setVisualGameStep('result');
                            }, 500);
                          }
                        }
                      }}
                    />
                  ))}
                </View>

                <View style={styles.exerciseActions}>
                  <TouchableOpacity
                    style={[styles.exerciseButton, styles.secondaryButton]}
                    onPress={() => setUserCellsSelection([])}
                  >
                    <Text style={styles.secondaryButtonText}>Clear</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}

            {visualGameActive && visualGameStep === 'result' && (
              <>
                <Text style={styles.resultTitle}>
                  {visualGameResult.accuracy >= 80 ? 'Great job!' : 'Nice try!'}
                </Text>

                <View style={styles.resultContainer}>
                  <Text style={styles.resultText}>Your accuracy: {visualGameResult.accuracy}%</Text>
                  <Text style={styles.resultText}>Correct squares: {visualGameResult.correct}/{visualGameResult.total}</Text>

                  <View style={styles.resultGridContainer}>
                    <Text style={styles.resultLabel}>Correct squares:</Text>
                    <View style={styles.memoryGrid}>
                      {Array(9).fill(0).map((_, index) => (
                        <View
                          key={index}
                          style={[
                            styles.gridCell,
                            styles.smallCell,
                            highlightedCells.includes(index) && styles.highlightedCell
                          ]}
                        />
                      ))}
                    </View>

                    <Text style={styles.resultLabel}>Your selection:</Text>
                    <View style={styles.memoryGrid}>
                      {Array(9).fill(0).map((_, index) => (
                        <View
                          key={index}
                          style={[
                            styles.gridCell,
                            styles.smallCell,
                            userCellsSelection.includes(index) && (
                              highlightedCells.includes(index) ? styles.correctCell : styles.incorrectCell
                            )
                          ]}
                        />
                      ))}
                    </View>
                  </View>
                </View>

                <View style={styles.exerciseActions}>
                  <TouchableOpacity
                    style={styles.exerciseButton}
                    onPress={() => {
                      setVisualGameActive(false);
                      setVisualGameStep('intro');
                    }}
                  >
                    <Text style={styles.exerciseButtonText}>Play Again</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>


      </ScrollView>
      </DashboardLayout>
    </>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#f8f9fc', // Lighter, more modern background color
  },
  headerSection: {
    width: '100%',
    marginBottom: 24,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 28,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    borderTopWidth: 0,
  },
  greeting: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingTextContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginTop: 10,
    marginBottom: 20,
  },
  quickActionButton: {
    width: '48%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    elevation: 15,
    shadowColor: 'rgba(0,0,0,0.7)',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.7,
    shadowRadius: 8,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  greetingText: {
    fontSize: 26,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
    letterSpacing: 0.3,
  },
  dateText: {
    fontSize: 15,
    color: 'rgba(255, 255, 255, 0.95)',
    marginTop: 4,
    letterSpacing: 0.2,
  },
  contentSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 10,
    backgroundColor: 'rgba(0,0,0,0.03)',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 19,
    fontWeight: 'bold',
    color: '#212121',
    letterSpacing: 0.3,
  },
  sectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    marginLeft: 4,
    backgroundColor: '#f0f0f0',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    maxWidth: 90, // Limit width to prevent overflow
  },
  primaryActionButton: {
    backgroundColor: '#FF9800', // Orange color to match Request Appointment
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    color: '#FF9800', // Match the orange color
  },
  primaryActionButtonText: {
    color: '#fff',
  },
  requestAppointmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF9800', // Orange color as shown in the image
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    maxWidth: 180, // Limit width to prevent overflow
  },
  requestAppointmentText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 4,
    fontSize: 12,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    minHeight: 150,
  },
  loadingText: {
    fontSize: 15,
    color: '#757575',
    marginTop: 12,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  cardsContainer: {
    marginBottom: 16,
  },
  activitySection: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  activityItems: {
    marginTop: 8,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.18,
    shadowRadius: 5,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
    transform: [{ translateY: 0 }], // For animation purposes
  },
  activityIcon: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 0.2,
    marginBottom: 4,
  },
  activityTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    backgroundColor: 'rgba(0,0,0,0.03)',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  activityTimeIcon: {
    marginRight: 4,
  },
  activityTime: {
    fontSize: 12,
    color: '#757575',
    fontWeight: '500',
  },
  emptyActivity: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  emptyActivityText: {
    fontSize: 15,
    color: '#757575',
    marginTop: 12,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  tipsSection: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  tipsWrapper: {
    marginTop: 8,
  },
  medicationRemindersSection: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.18,
    shadowRadius: 5,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
    transform: [{ translateY: 0 }], // For animation purposes
  },
  tipIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  tipContent: {
    flex: 1,
    marginLeft: 8,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  tipText: {
    fontSize: 14,
    color: '#757575',
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  memoryExerciseSection: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  listsContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  exerciseCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  exerciseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  exerciseIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4285F4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  exerciseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  exerciseInstructions: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 16,
    lineHeight: 20,
  },
  numberSequence: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  numberBubble: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E1F5FE',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
    borderWidth: 2,
    borderColor: '#4285F4',
  },
  numberText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4285F4',
  },
  exerciseActions: {
    alignItems: 'center',
  },
  exerciseButton: {
    backgroundColor: '#4285F4',
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
  },
  exerciseButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  performanceCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  performanceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  performanceStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  performanceStat: {
    alignItems: 'center',
  },
  performanceValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  performanceLabel: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
  },
  performanceDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#BDBDBD',
  },
  memoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: 180,
    height: 180,
    alignSelf: 'center',
    marginBottom: 20,
  },
  gridCell: {
    width: 56,
    height: 56,
    margin: 2,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  highlightedCell: {
    backgroundColor: '#FFECB3',
    borderWidth: 2,
    borderColor: '#FBBC05',
  },
  selectedCell: {
    backgroundColor: '#E1F5FE',
    borderWidth: 2,
    borderColor: '#4285F4',
  },
  correctCell: {
    backgroundColor: '#C8E6C9',
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  incorrectCell: {
    backgroundColor: '#FFCDD2',
    borderWidth: 2,
    borderColor: '#F44336',
  },
  smallCell: {
    width: 40,
    height: 40,
    margin: 2,
  },
  emptyBubble: {
    backgroundColor: '#f0f0f0',
    borderColor: '#BDBDBD',
  },
  smallBubble: {
    width: 30,
    height: 30,
    marginHorizontal: 3,
  },
  correctBubble: {
    backgroundColor: '#C8E6C9',
    borderColor: '#4CAF50',
  },
  incorrectBubble: {
    backgroundColor: '#FFCDD2',
    borderColor: '#F44336',
  },
  numberPad: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 10,
    marginBottom: 10,
    width: '100%',
  },
  numberPadButton: {
    width: 50,
    height: 50,
    margin: 5,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  numberPadButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    backgroundColor: '#FFEBEE',
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#F44336',
  },
  exerciseTimer: {
    textAlign: 'center',
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
    marginTop: 10,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4285F4',
    textAlign: 'center',
    marginBottom: 15,
  },
  resultContainer: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
  },
  resultText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
    textAlign: 'center',
  },
  resultSequenceContainer: {
    marginTop: 15,
  },
  resultGridContainer: {
    marginTop: 15,
    alignItems: 'center',
  },
  resultLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#757575',
    marginBottom: 5,
    marginTop: 10,
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#BDBDBD',
  },
  secondaryButtonText: {
    color: '#757575',
    fontWeight: 'bold',
    fontSize: 14,
  },
  cardsContainer: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  prescriptionsSection: {
    marginBottom: 24,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  prescriptionCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginTop: 10,
  },
  prescriptionCardText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  prescriptionButton: {
    backgroundColor: PATIENT_COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  prescriptionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

export default PatientDashboard;