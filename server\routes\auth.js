const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');

// Get user profile
router.get('/profile', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const userDoc = await admin.firestore()
            .collection('users')
            .doc(userId)
            .get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User profile not found' });
        }

        res.json(userDoc.data());
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ error: 'Failed to fetch user profile' });
    }
});

// Update user profile
router.put('/profile', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const updateData = req.body;
        
        // Remove sensitive fields if they exist
        delete updateData.email;
        delete updateData.emailVerified;
        delete updateData.role;

        await admin.firestore()
            .collection('users')
            .doc(userId)
            .set({
                ...updateData,
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            }, { merge: true });

        res.json({ success: true });
    } catch (error) {
        console.error('Error updating user profile:', error);
        res.status(500).json({ error: 'Failed to update user profile' });
    }
});

// Create initial user profile after registration
router.post('/create-profile', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const { displayName, phoneNumber, dateOfBirth, emergencyContacts } = req.body;

        const userProfile = {
            uid: userId,
            displayName,
            phoneNumber,
            dateOfBirth,
            emergencyContacts,
            role: 'patient', // Default role
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        };

        await admin.firestore()
            .collection('users')
            .doc(userId)
            .set(userProfile);

        res.status(201).json({ success: true, profile: userProfile });
    } catch (error) {
        console.error('Error creating user profile:', error);
        res.status(500).json({ error: 'Failed to create user profile' });
    }
});

// Add caregiver
router.post('/add-caregiver', verifyToken, async (req, res) => {
    try {
        const patientId = req.user.uid;
        const { caregiverEmail } = req.body;

        // Get caregiver user by email
        const caregiverRecord = await admin.auth().getUserByEmail(caregiverEmail);
        
        // Add caregiver relationship to Firestore
        await admin.firestore().collection('caregiverRelationships').add({
            patientId,
            caregiverId: caregiverRecord.uid,
            status: 'pending',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.status(201).json({ success: true });
    } catch (error) {
        console.error('Error adding caregiver:', error);
        res.status(500).json({ error: 'Failed to add caregiver' });
    }
});

// Accept caregiver invitation
router.post('/accept-caregiver', verifyToken, async (req, res) => {
    try {
        const caregiverId = req.user.uid;
        const { relationshipId } = req.body;

        const relationshipRef = admin.firestore()
            .collection('caregiverRelationships')
            .doc(relationshipId);

        await relationshipRef.update({
            status: 'active',
            acceptedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.json({ success: true });
    } catch (error) {
        console.error('Error accepting caregiver invitation:', error);
        res.status(500).json({ error: 'Failed to accept invitation' });
    }
});

module.exports = router;
