/**
 * Firestore Database Schema
 * This file documents the data structure of our Firestore collections
 */

const userSchema = {
    uid: 'string', // Firebase Auth UID
    displayName: 'string',
    email: 'string',
    phoneNumber: 'string',
    dateOfBirth: 'timestamp',
    gender: 'string',
    role: 'string', // 'patient', 'doctor', or 'caregiver'
    emergencyContacts: [{
        name: 'string',
        relationship: 'string',
        phoneNumber: 'string'
    }],
    medicalInfo: {
        primaryDiagnosis: 'string',
        secondaryDiagnoses: ['string'],
        conditions: ['string'],
        allergies: ['string'],
        familyHistory: ['string'],
        pastSurgeries: [{
            procedure: 'string',
            date: 'timestamp',
            hospital: 'string'
        }],
        bloodType: 'string',
        height: 'number',
        weight: 'number',
        bmi: 'number',
        medications: [{
            name: 'string',
            dosage: 'string',
            frequency: 'string',
            startDate: 'timestamp',
            prescribingDoctor: 'string',
            pharmacy: 'string',
            sideEffects: ['string'],
            purpose: 'string'
        }]
    },
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
};

const reminderSchema = {
    userId: 'string', // Reference to user
    title: 'string',
    body: 'string',
    type: 'string', // 'medication', 'appointment', 'activity'
    scheduledTime: 'timestamp',
    recurring: 'boolean',
    recurringPattern: 'string', // 'daily', 'weekly', 'monthly'
    status: 'string', // 'scheduled', 'completed', 'missed'
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
};

const symptomLogSchema = {
    userId: 'string', // Reference to user
    symptoms: [{
        type: 'string',
        severity: 'number', // 1-10
        description: 'string'
    }],
    mood: 'string',
    activities: ['string'],
    notes: 'string',
    timestamp: 'timestamp'
};

const caregiverRelationshipSchema = {
    patientId: 'string', // Reference to patient user
    caregiverId: 'string', // Reference to caregiver user
    status: 'string', // 'pending', 'active', 'inactive'
    permissions: ['string'], // Array of permission types
    createdAt: 'timestamp',
    acceptedAt: 'timestamp'
};

const activityLogSchema = {
    userId: 'string', // Reference to user
    type: 'string', // 'medication', 'exercise', 'social', 'cognitive'
    description: 'string',
    duration: 'number', // in minutes
    completed: 'boolean',
    timestamp: 'timestamp'
};

const securityAlertSchema = {
    userId: 'string', // Reference to user
    type: 'string', // 'fall', 'location', 'behavior'
    severity: 'string', // 'low', 'medium', 'high'
    description: 'string',
    location: {
        latitude: 'number',
        longitude: 'number',
        address: 'string'
    },
    resolved: 'boolean',
    resolvedBy: 'string', // Reference to user who resolved
    timestamp: 'timestamp',
    resolvedAt: 'timestamp'
};

const vitalSignsSchema = {
    userId: 'string', // Reference to patient
    bloodPressure: {
        systolic: 'number',
        diastolic: 'number',
        unit: 'string' // mmHg
    },
    heartRate: {
        value: 'number',
        unit: 'string' // bpm
    },
    temperature: {
        value: 'number',
        unit: 'string' // °C or °F
    },
    respiratoryRate: {
        value: 'number',
        unit: 'string' // breaths per minute
    },
    oxygenSaturation: {
        value: 'number',
        unit: 'string' // %
    },
    timestamp: 'timestamp',
    recordedBy: 'string' // Reference to user who recorded
};

const doctorPatientSchema = {
    doctorId: 'string', // Reference to doctor user
    patientId: 'string', // Reference to patient user
    status: 'string', // 'active', 'inactive', 'pending'
    startDate: 'timestamp',
    endDate: 'timestamp',
    specialNotes: 'string',
    treatmentPlan: 'string',
    followUpSchedule: 'string',
    lastVisit: 'timestamp',
    nextVisit: 'timestamp'
};

const medicalRecordsSchema = {
    patientId: 'string', // Reference to patient
    type: 'string', // 'lab', 'imaging', 'progress', 'vaccination'
    title: 'string',
    description: 'string',
    date: 'timestamp',
    doctorId: 'string', // Reference to doctor
    facility: 'string',
    attachments: ['string'], // URLs to stored files
    notes: 'string',
    status: 'string', // 'normal', 'abnormal', 'pending'
    followUpRequired: 'boolean'
};

const appointmentSchema = {
    patientId: 'string', // Reference to patient
    doctorId: 'string', // Reference to doctor
    dateTime: 'timestamp',
    type: 'string', // 'initial', 'follow-up', 'emergency', 'routine'
    status: 'string', // 'scheduled', 'completed', 'cancelled', 'no-show'
    notes: 'string',
    followUpRequired: 'boolean',
    followUpNotes: 'string',
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
};

const prescriptionSchema = {
    doctorId: 'string', // Reference to doctor
    doctorName: 'string', // Doctor's name for display
    patientId: 'string', // Reference to patient
    patientName: 'string', // Patient's name for display
    medications: [{
        name: 'string',
        dosage: 'string',
        instructions: 'string'
    }],
    notes: 'string',
    prescriptionImage: 'string', // URL to stored image
    status: 'string', // 'pending', 'sent', 'filled'
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
};

module.exports = {
    userSchema,
    reminderSchema,
    symptomLogSchema,
    caregiverRelationshipSchema,
    activityLogSchema,
    securityAlertSchema,
    vitalSignsSchema,
    doctorPatientSchema,
    medicalRecordsSchema,
    appointmentSchema,
    prescriptionSchema
};
