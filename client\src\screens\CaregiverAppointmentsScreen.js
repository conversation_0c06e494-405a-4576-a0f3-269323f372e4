import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  ScrollView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Avatar, Chip, Modal, Portal, Button, Title, Paragraph } from 'react-native-paper';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { firebaseAppointmentsService } from '../services/firebaseAppointmentsService';
import { ROLE_COLORS } from '../config/theme';

// Get caregiver colors for use in styles
const caregiverColors = ROLE_COLORS.caregiver;

const CaregiverAppointmentsScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');

  // Fetch appointments on component mount
  useEffect(() => {
    fetchAppointments();
  }, []);

  // Fetch appointments from Firebase
  const fetchAppointments = async () => {
    try {
      setLoading(true);
      
      // Get appointments for patients linked to this caregiver
      const options = {
        includePatientDetails: true,
        dateOrder: 'asc',  // Upcoming appointments first
        timeOrder: 'asc'   // Earlier times first for same day
      };
      
      const fetchedAppointments = await firebaseAppointmentsService.getCaregiverPatientAppointments(options);
      setAppointments(fetchedAppointments);
      applyFilters(fetchedAppointments, filterStatus);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      Alert.alert('Error', 'Failed to load appointments. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Apply filters to appointments
  const applyFilters = (appointmentsToFilter, status) => {
    let filtered = [...appointmentsToFilter];
    
    // Filter by status if not 'all'
    if (status !== 'all') {
      filtered = filtered.filter(appointment => 
        appointment.status.toLowerCase() === status.toLowerCase()
      );
    }
    
    setFilteredAppointments(filtered);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    applyFilters(appointments, status);
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchAppointments();
  };

  // Handle appointment selection
  const handleAppointmentPress = (appointment) => {
    setSelectedAppointment(appointment);
    setDetailsModalVisible(true);
  };

  // Get status color
  const getStatusColor = (status) => {
    const statusLower = status?.toLowerCase() || '';
    switch (statusLower) {
      case 'confirmed':
      case 'completed':
        return '#4CAF50'; // Green
      case 'pending':
        return '#FF9800'; // Orange
      case 'cancelled':
        return '#F44336'; // Red
      case 'rescheduled':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'No date';
      return format(parseISO(dateString), 'dd MMMM yyyy', { locale: fr });
    } catch (error) {
      try {
        // Try alternative format
        return format(new Date(dateString), 'dd MMMM yyyy', { locale: fr });
      } catch (e) {
        console.error('Error formatting date:', e);
        return dateString;
      }
    }
  };

  // Render appointment item
  const renderAppointmentItem = ({ item }) => {
    const statusColor = getStatusColor(item.status);
    const patientName = item.patient?.firstName 
      ? `${item.patient.firstName} ${item.patient.lastName || ''}`
      : item.patientName || 'Patient';
    
    const doctorName = item.doctor?.firstName 
      ? `${item.doctor.firstName} ${item.doctor.lastName || ''}`
      : item.doctor || 'Doctor';
    
    const appointmentDate = formatDate(item.date);
    
    return (
      <TouchableOpacity
        style={styles.appointmentCard}
        onPress={() => handleAppointmentPress(item)}
      >
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <View style={styles.patientInfo}>
                <Text style={styles.patientName}>{patientName}</Text>
                <Text style={styles.appointmentDate}>{appointmentDate}</Text>
                <Text style={styles.appointmentTime}>{item.time}</Text>
              </View>
              <View style={styles.statusContainer}>
                <Chip
                  style={[styles.statusChip, { backgroundColor: statusColor + '20' }]}
                  textStyle={{ color: statusColor }}
                >
                  {item.status.toUpperCase()}
                </Chip>
              </View>
            </View>
            <View style={styles.doctorContainer}>
              <Ionicons name="medical" size={16} color={caregiverColors.primary} />
              <Text style={styles.doctorName}>{doctorName}</Text>
            </View>
            {item.reason && (
              <Text style={styles.reason}>Reason: {item.reason}</Text>
            )}
          </Card.Content>
        </Card>
      </TouchableOpacity>
    );
  };

  // Render appointment details modal
  const renderDetailsModal = () => (
    <Portal>
      <Modal
        visible={detailsModalVisible}
        onDismiss={() => setDetailsModalVisible(false)}
        contentContainerStyle={styles.modalContainer}
      >
        {selectedAppointment && (
          <ScrollView>
            <View style={styles.modalHeader}>
              <Title style={styles.modalTitle}>Appointment Details</Title>
              <TouchableOpacity
                onPress={() => setDetailsModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalContent}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Patient:</Text>
                <Text style={styles.detailValue}>
                  {selectedAppointment.patient?.firstName 
                    ? `${selectedAppointment.patient.firstName} ${selectedAppointment.patient.lastName || ''}`
                    : selectedAppointment.patientName || 'Patient'}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Doctor:</Text>
                <Text style={styles.detailValue}>
                  {selectedAppointment.doctor?.firstName 
                    ? `${selectedAppointment.doctor.firstName} ${selectedAppointment.doctor.lastName || ''}`
                    : selectedAppointment.doctor || 'Doctor'}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date:</Text>
                <Text style={styles.detailValue}>
                  {formatDate(selectedAppointment.date)}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Time:</Text>
                <Text style={styles.detailValue}>{selectedAppointment.time}</Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Status:</Text>
                <Chip
                  style={[
                    styles.statusChip,
                    { backgroundColor: getStatusColor(selectedAppointment.status) + '20' }
                  ]}
                  textStyle={{ color: getStatusColor(selectedAppointment.status) }}
                >
                  {selectedAppointment.status.toUpperCase()}
                </Chip>
              </View>
              
              {selectedAppointment.reason && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Reason:</Text>
                  <Text style={styles.detailValue}>{selectedAppointment.reason}</Text>
                </View>
              )}
              
              {selectedAppointment.notes && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Notes:</Text>
                  <Text style={styles.detailValue}>{selectedAppointment.notes}</Text>
                </View>
              )}
            </View>
            
            <Button
              mode="outlined"
              onPress={() => setDetailsModalVisible(false)}
              style={styles.closeModalButton}
            >
              Close
            </Button>
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  return (
    <View style={styles.container}>
      {renderDetailsModal()}
      
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'all' && styles.activeFilterButton
            ]}
            onPress={() => handleFilterChange('all')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'all' && styles.activeFilterButtonText
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'pending' && styles.activeFilterButton
            ]}
            onPress={() => handleFilterChange('pending')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'pending' && styles.activeFilterButtonText
              ]}
            >
              Pending
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'confirmed' && styles.activeFilterButton
            ]}
            onPress={() => handleFilterChange('confirmed')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'confirmed' && styles.activeFilterButtonText
              ]}
            >
              Confirmed
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'completed' && styles.activeFilterButton
            ]}
            onPress={() => handleFilterChange('completed')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'completed' && styles.activeFilterButtonText
              ]}
            >
              Completed
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterStatus === 'cancelled' && styles.activeFilterButton
            ]}
            onPress={() => handleFilterChange('cancelled')}
          >
            <Text
              style={[
                styles.filterButtonText,
                filterStatus === 'cancelled' && styles.activeFilterButtonText
              ]}
            >
              Cancelled
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={caregiverColors.primary} />
          <Text style={styles.loadingText}>Loading appointments...</Text>
        </View>
      ) : filteredAppointments.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No appointments found</Text>
          <Text style={styles.emptySubText}>
            {filterStatus !== 'all'
              ? `No ${filterStatus} appointments found. Try another filter.`
              : 'Your patients have no scheduled appointments.'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredAppointments}
          renderItem={renderAppointmentItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[caregiverColors.primary]}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  filterContainer: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  activeFilterButton: {
    backgroundColor: caregiverColors.primary,
  },
  filterButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: '#fff',
  },
  listContainer: {
    padding: 15,
  },
  appointmentCard: {
    marginBottom: 15,
  },
  card: {
    borderRadius: 10,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  appointmentDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  appointmentTime: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusChip: {
    height: 28,
  },
  doctorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  doctorName: {
    fontSize: 14,
    color: '#666',
    marginLeft: 5,
  },
  reason: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 5,
  },
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  modalContent: {
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'center',
  },
  detailLabel: {
    width: 80,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#555',
  },
  detailValue: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  closeModalButton: {
    marginTop: 10,
    borderColor: caregiverColors.primary,
    borderWidth: 1,
  },
});

export default CaregiverAppointmentsScreen;
