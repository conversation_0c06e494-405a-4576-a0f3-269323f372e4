import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Vibration
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { PATIENT_COLORS, ROLE_COLORS } from '../../config/theme';
import { useAuth } from '../../contexts/AuthContext';
import { videoCallService } from '../../services/videoCallService';

const VideoCallNotification = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const [visible, setVisible] = useState(false);
  const [callData, setCallData] = useState(null);
  const [callerInfo, setCallerInfo] = useState(null);
  const [animation] = useState(new Animated.Value(0));

  // Check for active calls periodically
  useEffect(() => {
    // Make sure we have the required objects
    if (!user || !navigation) {
      console.log('VideoCallNotification: User or navigation not available yet');
      return;
    }

    // Make sure user has the required properties
    if (!user.id || !user.role) {
      console.log('VideoCallNotification: User missing id or role');
      return;
    }

    const checkForCalls = async () => {
      try {
        const activeCalls = await videoCallService.getActiveCalls(user.id, user.role);

        if (activeCalls && activeCalls.length > 0) {
          // Get the most recent call
          const latestCall = activeCalls[0];
          setCallData(latestCall);

          // Get caller information
          const callerId = user.role === 'doctor' ? latestCall.patientId : latestCall.doctorId;
          // In a real app, you would fetch the caller's information from your database
          setCallerInfo({
            id: callerId,
            name: user.role === 'doctor' ? 'Patient' : 'Doctor',
            role: user.role === 'doctor' ? 'patient' : 'doctor'
          });

          // Show notification
          setVisible(true);

          // Vibrate to alert the user
          Vibration.vibrate([500, 500, 500]);

          // Animate the notification
          Animated.loop(
            Animated.sequence([
              Animated.timing(animation, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true
              }),
              Animated.timing(animation, {
                toValue: 0,
                duration: 1000,
                useNativeDriver: true
              })
            ])
          ).start();
        }
      } catch (error) {
        console.error('Error checking for active calls:', error);
      }
    };

    console.log('VideoCallNotification: Setting up call checking for user', user.id, user.role);

    // Check immediately and then every 10 seconds
    checkForCalls();
    const interval = setInterval(checkForCalls, 10000);

    return () => clearInterval(interval);
  }, [user, navigation]);

  // Handle accepting the call
  const handleAcceptCall = () => {
    setVisible(false);

    if (!navigation) {
      console.error('Navigation is not available');
      return;
    }

    // Navigate to the appropriate video call screen based on user role
    if (user.role === 'patient') {
      navigation.navigate('PatientVideoCall', {
        callId: callData.id,
        roomName: callData.roomName,
        doctorInfo: callerInfo
      });
    } else {
      // For doctors, navigate to the Chatroom with video call parameters
      navigation.navigate('Chatroom', {
        patientInfo: callerInfo,
        callId: callData.id,
        roomName: callData.roomName,
        startVideoCall: true  // Flag to automatically start video call
      });
    }
  };

  // Handle declining the call
  const handleDeclineCall = async () => {
    try {
      if (callData) {
        await videoCallService.endCall(callData.id);
      }
      setVisible(false);
    } catch (error) {
      console.error('Error declining call:', error);
      setVisible(false);
    }
  };

  if (!visible) return null;

  // Determine colors based on user role
  const colors = user.role === 'doctor' ? ROLE_COLORS.doctor : PATIENT_COLORS;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
    >
      <View style={styles.modalContainer}>
        <Animated.View
          style={[
            styles.notificationContainer,
            {
              borderColor: colors.primary,
              transform: [
                {
                  scale: animation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.05]
                  })
                }
              ]
            }
          ]}
        >
          <View style={styles.callerInfo}>
            <View style={[styles.callerAvatar, { backgroundColor: colors.primary }]}>
              <Ionicons name="person" size={40} color="#fff" />
            </View>
            <View style={styles.callerDetails}>
              <Text style={styles.callerName}>
                {callerInfo?.name || (user.role === 'doctor' ? 'Patient' : 'Doctor')} is calling...
              </Text>
              <Text style={styles.callType}>Video Consultation</Text>
            </View>
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, styles.declineButton]}
              onPress={handleDeclineCall}
            >
              <Ionicons name="close-circle" size={30} color="#fff" />
              <Text style={styles.actionText}>Decline</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.acceptButton]}
              onPress={handleAcceptCall}
            >
              <Ionicons name="videocam" size={30} color="#fff" />
              <Text style={styles.actionText}>Accept</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  notificationContainer: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  callerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  callerAvatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  callerDetails: {
    flex: 1,
  },
  callerName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  callType: {
    fontSize: 14,
    color: '#666',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    width: 120,
  },
  declineButton: {
    backgroundColor: '#E53935',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  actionText: {
    color: '#fff',
    marginTop: 5,
    fontWeight: 'bold',
  },
});

export default VideoCallNotification;
