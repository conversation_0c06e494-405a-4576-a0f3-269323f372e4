import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Image,
  Modal,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { firebasePrescriptionsService } from '../services/firebasePrescriptionsService';
import * as ImagePicker from 'expo-image-picker';
import DoctorPatientSelectorModal from '../components/doctor/DoctorPatientSelectorModal';
import { ROLE_COLORS } from '../config/theme';

const NewPrescriptionScreen = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const doctorColors = ROLE_COLORS.doctor;

  // Form states
  const [patientId, setPatientId] = useState('');
  const [patientName, setPatientName] = useState('');
  const [medications, setMedications] = useState([]);
  const [notes, setNotes] = useState('');
  const [prescriptionImage, setPrescriptionImage] = useState(null);

  // UI states
  const [loading, setLoading] = useState(false);
  const [showPatientSelector, setShowPatientSelector] = useState(false);

  // Simplified medication handling
  const [medicationName, setMedicationName] = useState('');
  const [medicationDosage, setMedicationDosage] = useState('');
  const [medicationInstructions, setMedicationInstructions] = useState('');

  // Request camera permissions
  useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to upload prescription images.');
      }
    })();
  }, []);

  const handlePatientSelected = (patient) => {
    setPatientId(patient.id);
    setPatientName(patient.name);
  };

  const handleAddMedication = () => {
    // Validate input
    if (!medicationName.trim()) {
      Alert.alert('Error', 'Medication name is required');
      return;
    }

    // Create new medication object
    const newMedication = {
      name: medicationName,
      dosage: medicationDosage,
      instructions: medicationInstructions
    };

    // Add to medications array
    const updatedMedications = [...medications, newMedication];
    setMedications(updatedMedications);

    // Clear input fields
    setMedicationName('');
    setMedicationDosage('');
    setMedicationInstructions('');

    // Show success message
    Alert.alert('Success', 'Medication added successfully');
  };

  const handleRemoveMedication = (index) => {
    const updatedMedications = [...medications];
    updatedMedications.splice(index, 1);
    setMedications(updatedMedications);
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setPrescriptionImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const takePicture = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setPrescriptionImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture');
    }
  };

  const handleSubmit = async () => {
    // Validate form
    if (!patientName) {
      Alert.alert('Error', 'Please select a patient');
      return;
    }

    if (medications.length === 0) {
      Alert.alert('Error', 'Please add at least one medication');
      return;
    }

    // Check if any medication is missing required fields
    const invalidMedication = medications.find(med => !med.name.trim());
    if (invalidMedication) {
      Alert.alert('Error', 'All medications must have a name');
      return;
    }

    try {
      setLoading(true);

      // Prepare prescription data
      const prescriptionData = {
        doctorId: user?.uid,
        doctorName: `Dr. ${user?.lastName || 'Doctor'}`,
        patientId: patientId,
        patientName,
        medications,
        notes,
        prescriptionImage,
        status: 'pending'
      };

      // Save prescription to Firebase
      const result = await firebasePrescriptionsService.savePrescription(prescriptionData);

      // Check if there was a permission error
      if (result._permissionError) {
        Alert.alert(
          'Permission Error',
          'You do not have permission to save prescriptions to Firebase. Please contact your administrator to update Firestore security rules. The prescription has been saved locally.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } else {
        Alert.alert(
          'Success',
          'Prescription saved to Firebase successfully',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error) {
      console.error('Error creating prescription in Firebase:', error);
      Alert.alert('Error', 'Failed to create prescription. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>New Prescription</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* Patient Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Patient Information</Text>
          <TouchableOpacity
            style={styles.patientSelector}
            onPress={() => setShowPatientSelector(true)}
          >
            {patientName ? (
              <View style={styles.selectedPatient}>
                <Ionicons name="person" size={24} color={doctorColors.primary} />
                <Text style={styles.selectedPatientName}>{patientName}</Text>
              </View>
            ) : (
              <View style={styles.patientPlaceholder}>
                <Ionicons name="person-add" size={24} color="#757575" />
                <Text style={styles.patientPlaceholderText}>Select Patient</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Medications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Add Medication</Text>

          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Medication Name*</Text>
            <TextInput
              style={styles.formInput}
              placeholder="e.g., Amoxicillin"
              value={medicationName}
              onChangeText={setMedicationName}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Dosage</Text>
            <TextInput
              style={styles.formInput}
              placeholder="e.g., 500mg twice daily"
              value={medicationDosage}
              onChangeText={setMedicationDosage}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Instructions</Text>
            <TextInput
              style={[styles.formInput, styles.textArea]}
              placeholder="e.g., Take with food"
              multiline
              value={medicationInstructions}
              onChangeText={setMedicationInstructions}
            />
          </View>

          <TouchableOpacity
            style={[styles.addMedicationButton, { backgroundColor: doctorColors.primary }]}
            onPress={handleAddMedication}
          >
            <Ionicons name="add-circle" size={20} color="white" />
            <Text style={styles.addMedicationButtonText}>Add Medication</Text>
          </TouchableOpacity>

          <View style={{marginTop: 20}}>
            <Text style={styles.medicationsListTitle}>Medications List ({medications.length})</Text>

            {medications.length === 0 ? (
              <View style={styles.emptyMedications}>
                <Ionicons name="medkit-outline" size={32} color="#BDBDBD" />
                <Text style={styles.emptyMedicationsText}>No medications added</Text>
              </View>
            ) : (
              <View>
                {medications.map((medication, index) => (
                  <View key={index} style={styles.medicationItem}>
                    <View style={styles.medicationInfo}>
                      <Text style={styles.medicationName}>{medication.name || 'Unnamed medication'}</Text>
                      <Text style={styles.medicationDosage}>{medication.dosage || 'No dosage specified'}</Text>
                      {medication.instructions ? (
                        <Text style={styles.medicationInstructions}>{medication.instructions}</Text>
                      ) : (
                        <Text style={[styles.medicationInstructions, {fontStyle: 'italic'}]}>No instructions</Text>
                      )}
                    </View>
                    <TouchableOpacity
                      style={styles.medicationActionButton}
                      onPress={() => handleRemoveMedication(index)}
                    >
                      <Ionicons name="trash" size={18} color="#F44336" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>

        {/* Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Add any additional notes or instructions"
            multiline
            value={notes}
            onChangeText={setNotes}
          />
        </View>

        {/* Prescription Image */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Prescription Image (Optional)</Text>

          {prescriptionImage ? (
            <View style={styles.imageContainer}>
              <Image source={{ uri: prescriptionImage }} style={styles.prescriptionImage} />
              <TouchableOpacity
                style={styles.removeImageButton}
                onPress={() => setPrescriptionImage(null)}
              >
                <Ionicons name="close-circle" size={24} color="#F44336" />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.imageActions}>
              <TouchableOpacity
                style={[styles.imageActionButton, { backgroundColor: '#E1F5FE' }]}
                onPress={takePicture}
              >
                <Ionicons name="camera" size={24} color="#0288D1" />
                <Text style={[styles.imageActionText, { color: '#0288D1' }]}>Take Photo</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.imageActionButton, { backgroundColor: '#E8F5E9' }]}
                onPress={pickImage}
              >
                <Ionicons name="image" size={24} color="#43A047" />
                <Text style={[styles.imageActionText, { color: '#43A047' }]}>Upload Image</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: doctorColors.primary }]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <>
              <Ionicons name="checkmark-circle" size={20} color="white" />
              <Text style={styles.submitButtonText}>Create Prescription</Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>

      {/* Doctor Patient Selector Modal */}
      <DoctorPatientSelectorModal
        visible={showPatientSelector}
        onClose={() => setShowPatientSelector(false)}
        onSuccess={handlePatientSelected}
        scannerTitle="Select Your Patient"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  patientSelector: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
  },
  selectedPatient: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedPatientName: {
    fontSize: 16,
    color: '#333',
    marginLeft: 8,
  },
  patientPlaceholder: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  patientPlaceholderText: {
    fontSize: 16,
    color: '#757575',
    marginLeft: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  emptyMedications: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 24,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  emptyMedicationsText: {
    fontSize: 14,
    color: '#757575',
    marginTop: 8,
  },
  medicationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  medicationInfo: {
    flex: 1,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  medicationDosage: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 4,
  },
  medicationInstructions: {
    fontSize: 14,
    color: '#757575',
  },
  medicationActions: {
    flexDirection: 'row',
  },
  medicationActionButton: {
    padding: 8,
    marginLeft: 4,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  prescriptionImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  imageActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  imageActionButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  imageActionText: {
    marginTop: 8,
    fontWeight: '500',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 32,
  },
  submitButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  addMedicationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
    borderRadius: 8,
    marginTop: 8,
  },
  addMedicationButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  medicationsListTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
});

export default NewPrescriptionScreen;
