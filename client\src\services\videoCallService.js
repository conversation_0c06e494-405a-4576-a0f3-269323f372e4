import { db, collection, addDoc, doc, getDoc, updateDoc, serverTimestamp, query, where, getDocs } from '../config/firebase';
import axios from 'axios';

/**
 * Service for managing video calls
 */
export const videoCallService = {
  /**
   * Create a new video call
   * @param {string} patientId - ID of the patient to call
   * @param {string} roomName - Jitsi Meet room name
   * @returns {Promise<Object>} - Call information
   */
  createCall: async (patientId, roomName) => {
    try {
      // Call the server API to create a call record
      const response = await axios.post('/api/video/create-call', {
        patientId,
        roomName
      });

      return response.data;
    } catch (error) {
      console.error('Error creating video call:', error);
      throw error;
    }
  },

  /**
   * End an active video call
   * @param {string} callId - ID of the call to end
   * @returns {Promise<void>}
   */
  endCall: async (callId) => {
    try {
      await axios.post(`/api/video/${callId}/end`);
    } catch (error) {
      console.error('Error ending video call:', error);
      throw error;
    }
  },

  /**
   * Get details of a specific call
   * @param {string} callId - ID of the call to retrieve
   * @returns {Promise<Object>} - Call information
   */
  getCallDetails: async (callId) => {
    try {
      const response = await axios.get(`/api/video/${callId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting call details:', error);
      throw error;
    }
  },

  /**
   * Get active calls for a user
   * @param {string} userId - User ID
   * @param {string} role - User role ('doctor' or 'patient')
   * @returns {Promise<Array>} - List of active calls
   */
  getActiveCalls: async (userId, role) => {
    try {
      // Validate inputs
      if (!userId || !role) {
        console.warn('Invalid parameters for getActiveCalls:', { userId, role });
        return [];
      }

      // Create a query based on the user's role
      const callsCollection = collection(db, 'videoCalls');
      const fieldToQuery = role === 'doctor' ? 'doctorId' : 'patientId';

      const callsQuery = query(
        callsCollection,
        where(fieldToQuery, '==', userId),
        where('status', '==', 'active')
      );

      const callsSnapshot = await getDocs(callsQuery);

      if (callsSnapshot.empty) {
        return [];
      }

      return callsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting active calls:', error);
      return []; // Return empty array instead of throwing
    }
  }
};

export default videoCallService;
