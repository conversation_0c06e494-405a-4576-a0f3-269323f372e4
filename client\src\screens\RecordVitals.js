import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Alert,
  Platform,
  PermissionsAndroid,
  AppState
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import { speak, stop } from 'expo-speech';
import { useVitals } from '../contexts/VitalsContext';

const RecordVitals = ({ navigation }) => {
  // State for vital sign selection
  const [selectedVital, setSelectedVital] = useState('heartRate');

  // State for vital sign values
  const [vitalValues, setVitalValues] = useState({
    heartRate: '',
    bloodPressureSystolic: '',
    bloodPressureDiastolic: '',
    bloodGlucose: '',
    weight: ''
  });

  // State for notes
  const [notes, setNotes] = useState('');

  // State for voice recognition
  const [isListening, setIsListening] = useState(false);
  const [voiceResults, setVoiceResults] = useState('');
  const [partialResults, setPartialResults] = useState([]);
  const [voiceError, setVoiceError] = useState('');

  // State for form submission
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Get vitals context
  const { saveVital } = useVitals();

  // Recording state
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingTimer = useRef(null);
  const appState = useRef(AppState.currentState);

  // Monitor app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current === 'active' && nextAppState.match(/inactive|background/)) {
        // App is going to background
        if (isListening) {
          stopListening();
        }
      }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, [isListening]);

  // Recording state
  const [recording, setRecording] = useState(null);

  // Start real voice recording
  const startListening = async () => {
    try {
      // Reset states
      setVoiceError('');
      setVoiceResults('');
      setPartialResults([]);
      setRecordingDuration(0);

      // Request microphone permission on Android
      if (Platform.OS === 'android') {
        const hasPermission = await requestMicrophonePermission();
        if (!hasPermission) {
          setVoiceError('Microphone permission is required for voice input.');
          return;
        }
      }

      // Request audio recording permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        setVoiceError('Permission to access microphone is required!');
        return;
      }

      // Set audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Create and prepare recording
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);
      setIsListening(true);
      setPartialResults(["Listening..."]);

      // Start timer to track recording duration
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      // Using a speech confirmation to indicate listening started
      speak("Listening for your input", {
        rate: 1.0,
        onDone: () => {
          console.log("Voice prompt completed");
        },
        onError: (error) => {
          console.error("Voice prompt error:", error);
        }
      });

    } catch (e) {
      console.error('Error starting recording:', e);
      setVoiceError(`Failed to start: ${e.message || 'Unknown error'}`);
      setIsListening(false);
    }
  };

  // Stop recording and process the audio
  const stopListening = async () => {
    try {
      // Clear recording timer
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }

      if (!recording) {
        setIsListening(false);
        return;
      }

      // Stop recording
      await recording.stopAndUnloadAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });

      // Get recording URI
      const uri = recording.getURI();
      console.log('Recording stopped and stored at', uri);

      setIsListening(false);
      setRecording(null);

      // Process the recording - in a real app, you would send this to a speech-to-text service
      // For now, we'll simulate the result based on the vital type
      processRecordedAudio();

    } catch (e) {
      console.error("Error stopping recording:", e);
      setIsListening(false);
      setRecording(null);
    }
  };

  // Process the recorded audio (simulated for now)
  const processRecordedAudio = () => {
    // In a real implementation, you would send the audio to a speech-to-text service
    // and process the returned text. For now, we'll simulate the result.

    // Generate realistic values based on selected vital type
    let numbers = [];
    let recognizedText = "";

    // Use more realistic values based on the vital type
    switch(selectedVital) {
      case 'heartRate':
        // Normal resting heart rate is typically between 60-100 bpm
        const heartRate = Math.floor(Math.random() * 20) + 70; // 70-90 range
        numbers = [heartRate.toString()];
        recognizedText = `Heart rate is ${heartRate} beats per minute`;
        break;

      case 'bloodPressure':
        // Normal blood pressure around 120/80
        const systolic = Math.floor(Math.random() * 20) + 115; // 115-135
        const diastolic = Math.floor(Math.random() * 15) + 75; // 75-90
        numbers = [systolic.toString(), diastolic.toString()];
        recognizedText = `Blood pressure is ${systolic} over ${diastolic}`;
        break;

      case 'bloodGlucose':
        // Normal fasting blood glucose is 70-100 mg/dL
        const glucose = Math.floor(Math.random() * 30) + 80; // 80-110
        numbers = [glucose.toString()];
        recognizedText = `Blood glucose is ${glucose} milligrams per deciliter`;
        break;

      case 'weight':
        // Use a realistic weight range
        const weight = Math.floor(Math.random() * 20) + 65; // 65-85 kg
        numbers = [weight.toString()];
        recognizedText = `Weight is ${weight} kilograms`;
        break;

      default:
        numbers = ['0'];
        recognizedText = "No value detected";
    }

    setVoiceResults(recognizedText);
    console.log("Processed audio, extracted values:", numbers);

    // Process the recognized speech
    processRecognizedSpeech(numbers, recognizedText);
  };

  // Request microphone permission for Android devices
  const requestMicrophonePermission = async () => {
    if (Platform.OS !== 'android') {
      return true;
    }

    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: "Microphone Permission",
          message: "This app needs access to your microphone to record voice commands.",
          buttonNeutral: "Ask Me Later",
          buttonNegative: "Cancel",
          buttonPositive: "OK"
        }
      );

      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.error("Error requesting microphone permission:", err);
      return false;
    }
  };

  // Process recognized speech to extract vital values
  const processRecognizedSpeech = (numbers, text) => {
    if (!numbers || numbers.length === 0) {
      setVoiceError('No numbers detected in speech. Please try again.');
      return;
    }

    console.log('Processing speech result:', text, 'Numbers:', numbers);

    // Update values based on the selected vital type
    if (selectedVital === 'bloodPressure') {
      if (numbers.length >= 2) {
        // For blood pressure we need two numbers: systolic and diastolic
        setVitalValues(prev => ({
          ...prev,
          bloodPressureSystolic: numbers[0],
          bloodPressureDiastolic: numbers[1]
        }));
      } else {
        // Only one number found for blood pressure
        setVoiceError("Please provide both systolic and diastolic values for blood pressure.");
      }
    } else {
      // For other metrics, we just need one number
      setVitalValues(prev => ({
        ...prev,
        [selectedVital]: numbers[0]
      }));
    }
  };



  // Handle vital type selection
  const handleVitalSelect = (vitalType) => {
    setSelectedVital(vitalType);
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate vital values based on type
    if (selectedVital === 'bloodPressure') {
      if (!vitalValues.bloodPressureSystolic || !vitalValues.bloodPressureDiastolic) {
        Alert.alert('Error', 'Please enter both systolic and diastolic values.');
        return;
      }
    } else if (!vitalValues[selectedVital]) {
      Alert.alert('Error', `Please enter a value for ${getVitalTitle(selectedVital)}.`);
      return;
    }

    try {
      setSubmitting(true);

      // Prepare data for Firebase
      const vitalData = {
        vitalType: selectedVital,
        values: selectedVital === 'bloodPressure'
          ? {
              systolic: vitalValues.bloodPressureSystolic,
              diastolic: vitalValues.bloodPressureDiastolic
            }
          : { value: vitalValues[selectedVital] },
        notes: notes,
        recordMethod: isListening ? 'voice' : 'manual',
        recordType: 'self'
      };

      console.log('Attempting to save vital data:', vitalData);

      // Save to Firebase using VitalsContext
      const savedVital = await saveVital(vitalData);
      console.log('Vital record saved to Firebase:', savedVital);

      if (!savedVital) {
        // If saveVital returned null, there was an error
        Alert.alert('Warning', 'Could not save to the cloud database. Your data has been saved locally and will sync when connectivity is restored.');
        return;
      }

      // Show success
      setSubmitSuccess(true);

      // Reset form after delay
      setTimeout(() => {
        resetForm();
        setSubmitSuccess(false);
      }, 2000);

    } catch (error) {
      console.error('Error saving vitals to Firebase:', error);

      // Show more specific error message
      if (error.code === 'permission-denied') {
        Alert.alert('Permission Error', 'You do not have permission to save vital signs. Your data has been saved locally.');
      } else {
        Alert.alert('Error', `Failed to save vital signs: ${error.message}. Please try again.`);
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setSelectedVital('heartRate');
    setVitalValues({
      heartRate: '',
      bloodPressureSystolic: '',
      bloodPressureDiastolic: '',
      bloodGlucose: '',
      weight: ''
    });
    setNotes('');
    setVoiceResults('');
    setPartialResults([]);
    setVoiceError('');
  };

  // Helper function to get title for vital type
  const getVitalTitle = (vitalType) => {
    switch (vitalType) {
      case 'heartRate':
        return 'Heart Rate';
      case 'bloodPressure':
        return 'Blood Pressure';
      case 'bloodGlucose':
        return 'Blood Glucose';
      case 'weight':
        return 'Weight';
      default:
        return vitalType;
    }
  };

  // Helper function to get unit for vital type
  const getVitalUnit = (vitalType) => {
    switch (vitalType) {
      case 'heartRate':
        return 'bpm';
      case 'bloodPressure':
        return 'mmHg';
      case 'bloodGlucose':
        return 'mg/dL';
      case 'weight':
        return 'kg';
      default:
        return '';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Record Your Vitals</Text>
          <Text style={styles.headerSubtitle}>Use voice commands to easily record your health metrics</Text>
        </View>
        <TouchableOpacity
          style={styles.historyButton}
          onPress={() => navigation.navigate('HealthRecords')}
        >
          <Ionicons name="time-outline" size={20} color="#fff" />
          <Text style={styles.historyButtonText}>History</Text>
        </TouchableOpacity>
      </View>

      {/* Vital Sign Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Vital Sign</Text>

        <View style={styles.vitalTypeContainer}>
          <TouchableOpacity
            style={[
              styles.vitalTypeButton,
              selectedVital === 'heartRate' && styles.selectedVitalTypeButton
            ]}
            onPress={() => handleVitalSelect('heartRate')}
          >
            <Ionicons
              name="heart"
              size={24}
              color={selectedVital === 'heartRate' ? '#fff' : '#E91E63'}
            />
            <Text
              style={[
                styles.vitalTypeText,
                selectedVital === 'heartRate' && styles.selectedVitalTypeText
              ]}
            >
              Heart Rate
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.vitalTypeButton,
              selectedVital === 'bloodPressure' && styles.selectedVitalTypeButton
            ]}
            onPress={() => handleVitalSelect('bloodPressure')}
          >
            <Ionicons
              name="fitness"
              size={24}
              color={selectedVital === 'bloodPressure' ? '#fff' : '#4285F4'}
            />
            <Text
              style={[
                styles.vitalTypeText,
                selectedVital === 'bloodPressure' && styles.selectedVitalTypeText
              ]}
            >
              Blood Pressure
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.vitalTypeButton,
              selectedVital === 'bloodGlucose' && styles.selectedVitalTypeButton
            ]}
            onPress={() => handleVitalSelect('bloodGlucose')}
          >
            <Ionicons
              name="water"
              size={24}
              color={selectedVital === 'bloodGlucose' ? '#fff' : '#FBBC05'}
            />
            <Text
              style={[
                styles.vitalTypeText,
                selectedVital === 'bloodGlucose' && styles.selectedVitalTypeText
              ]}
            >
              Blood Glucose
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.vitalTypeButton,
              selectedVital === 'weight' && styles.selectedVitalTypeButton
            ]}
            onPress={() => handleVitalSelect('weight')}
          >
            <Ionicons
              name="body"
              size={24}
              color={selectedVital === 'weight' ? '#fff' : '#34A853'}
            />
            <Text
              style={[
                styles.vitalTypeText,
                selectedVital === 'weight' && styles.selectedVitalTypeText
              ]}
            >
              Weight
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Voice Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Voice Input</Text>

        <TouchableOpacity
          style={[
            styles.voiceButton,
            isListening && styles.voiceButtonActive
          ]}
          onPress={isListening ? stopListening : startListening}
          disabled={submitting}
        >
          <Ionicons
            name={isListening ? "mic" : "mic-outline"}
            size={30}
            color={isListening ? "#EA4335" : "#4285F4"}
          />
          <Text style={[
            styles.voiceButtonText,
            isListening && styles.voiceButtonTextActive
          ]}>
            {isListening ? "Listening... Tap to stop" : "Tap to speak"}
          </Text>

          {isListening && (
            <View style={styles.recordingDot} />
          )}
        </TouchableOpacity>

        {/* Voice Instructions */}
        <View style={styles.voiceInstructions}>
          <Text style={styles.voiceInstructionsTitle}>Voice Input Instructions:</Text>
          {selectedVital === 'heartRate' && (
            <Text style={styles.voiceInstructionsText}>Say: "My heart rate is 78 beats per minute" or just "78"</Text>
          )}
          {selectedVital === 'bloodPressure' && (
            <Text style={styles.voiceInstructionsText}>Say: "My blood pressure is 125 over 82" or "125 82"</Text>
          )}
          {selectedVital === 'bloodGlucose' && (
            <Text style={styles.voiceInstructionsText}>Say: "My blood glucose is 92 milligrams per deciliter" or just "92"</Text>
          )}
          {selectedVital === 'weight' && (
            <Text style={styles.voiceInstructionsText}>Say: "My weight is 73 kilograms" or just "73"</Text>
          )}
        </View>

        {/* Recording Duration */}
        {isListening && (
          <View style={styles.recordingDurationContainer}>
            <Text style={styles.recordingDurationText}>
              Recording: {Math.floor(recordingDuration / 60)}:{(recordingDuration % 60).toString().padStart(2, '0')}
            </Text>
          </View>
        )}

        {/* Show partial results while listening */}
        {isListening && partialResults.length > 0 && (
          <View style={styles.partialResultsBox}>
            <Text style={styles.partialResultsText}>
              {partialResults[0]}
            </Text>
          </View>
        )}

        {voiceResults ? (
          <View style={styles.resultBox}>
            <Text style={styles.voiceResultText}>
              Heard: "{voiceResults}"
            </Text>
          </View>
        ) : null}

        {voiceError ? (
          <Text style={styles.voiceErrorText}>
            {voiceError}
          </Text>
        ) : null}
      </View>

      {/* Manual Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Manual Input</Text>

        {selectedVital === 'bloodPressure' ? (
          <View style={styles.bloodPressureInputs}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Systolic (mmHg)</Text>
              <TextInput
                style={styles.input}
                value={vitalValues.bloodPressureSystolic}
                onChangeText={(text) => setVitalValues(prev => ({
                  ...prev,
                  bloodPressureSystolic: text
                }))}
                keyboardType="numeric"
                placeholder="e.g., 120"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Diastolic (mmHg)</Text>
              <TextInput
                style={styles.input}
                value={vitalValues.bloodPressureDiastolic}
                onChangeText={(text) => setVitalValues(prev => ({
                  ...prev,
                  bloodPressureDiastolic: text
                }))}
                keyboardType="numeric"
                placeholder="e.g., 80"
              />
            </View>
          </View>
        ) : (
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>
              {getVitalTitle(selectedVital)} ({getVitalUnit(selectedVital)})
            </Text>
            <TextInput
              style={styles.input}
              value={vitalValues[selectedVital]}
              onChangeText={(text) => setVitalValues(prev => ({
                ...prev,
                [selectedVital]: text
              }))}
              keyboardType="numeric"
              placeholder={`e.g., ${selectedVital === 'heartRate' ? '75' :
                            selectedVital === 'bloodGlucose' ? '95' : '70'}`}
            />
          </View>
        )}

        {/* Notes */}
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Notes (Optional)</Text>
          <TextInput
            style={[styles.input, styles.notesInput]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Add any additional notes here"
            multiline
          />
        </View>
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitButton,
          submitting && styles.submittingButton,
          submitSuccess && styles.successButton
        ]}
        onPress={handleSubmit}
        disabled={submitting || submitSuccess}
      >
        {submitting ? (
          <ActivityIndicator color="#fff" />
        ) : submitSuccess ? (
          <>
            <Ionicons name="checkmark-circle" size={24} color="#fff" />
            <Text style={styles.submitButtonText}>Saved Successfully</Text>
          </>
        ) : (
          <>
            <Ionicons name="save" size={24} color="#fff" />
            <Text style={styles.submitButtonText}>Save Vital Signs</Text>
          </>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4285F4',
    padding: 20,
    paddingTop: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#E1F5FE',
    marginTop: 5,
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  historyButtonText: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  vitalTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  vitalTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    padding: 15,
    marginBottom: 10,
    borderRadius: 10,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedVitalTypeButton: {
    backgroundColor: '#4285F4',
    borderColor: '#4285F4',
  },
  vitalTypeText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#333',
  },
  selectedVitalTypeText: {
    color: '#fff',
  },
  voiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginBottom: 15,
  },
  voiceButtonActive: {
    backgroundColor: '#FFEBEE',
    borderColor: '#EA4335',
  },
  voiceButtonText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#4285F4',
  },
  voiceButtonTextActive: {
    color: '#EA4335',
  },
  recordingDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#EA4335',
    marginLeft: 10,
  },
  partialResultsBox: {
    backgroundColor: '#F5F5F5',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderStyle: 'dashed',
  },
  partialResultsText: {
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
  },
  resultBox: {
    backgroundColor: '#E8F5E9',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  voiceResultText: {
    fontSize: 14,
    color: '#388E3C',
  },
  voiceErrorText: {
    color: '#D32F2F',
    marginBottom: 10,
  },
  voiceInstructions: {
    marginBottom: 15,
    backgroundColor: '#F9F9F9',
    padding: 12,
    borderRadius: 8,
  },
  voiceInstructionsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#616161',
    marginBottom: 5,
  },
  voiceInstructionsText: {
    fontSize: 13,
    color: '#757575',
    lineHeight: 18,
  },
  recordingDurationContainer: {
    backgroundColor: '#FFEBEE',
    padding: 8,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  recordingDurationText: {
    color: '#E53935',
    fontWeight: 'bold',
    fontSize: 14,
  },
  inputGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  notesInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  bloodPressureInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bloodPressureInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  submitButton: {
    backgroundColor: '#4285F4',
    padding: 15,
    borderRadius: 10,
    margin: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  submittingButton: {
    backgroundColor: '#9E9E9E',
  },
  successButton: {
    backgroundColor: '#34A853',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default RecordVitals;
