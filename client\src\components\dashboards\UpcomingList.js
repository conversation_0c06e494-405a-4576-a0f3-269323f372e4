import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ListItem = ({ item, onPress }) => {
  const getIconName = (type) => {
    switch (type) {
      case 'appointment':
        return 'calendar';
      case 'task':
        return 'checkbox-outline';
      case 'medication':
        return 'medkit';
      case 'message':
        return 'mail';
      case 'payment':
        return 'card';
      default:
        return 'alert-circle';
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'upcoming':
        return '#FFA000'; // Orange
      case 'completed':
        return '#4CAF50'; // Green
      case 'cancelled':
        return '#F44336'; // Red
      case 'pending':
        return '#2196F3'; // Blue
      case 'confirmed':
        return '#4CAF50'; // Green
      case 'rescheduled':
        return '#9C27B0'; // Purple
      default:
        return '#2196F3'; // Default blue
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'upcoming':
        return 'Upcoming';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'rescheduled':
        return 'Rescheduled';
      default:
        return status || 'Unknown';
    }
  };

  const statusColor = getStatusColor(item.status);
  const statusText = getStatusText(item.status);

  // Format date for display if available
  const formatDate = () => {
    if (item.appointmentDate) {
      try {
        // Parse the date from YYYY-MM-DD format
        const dateParts = item.appointmentDate.split('-');
        if (dateParts.length === 3) {
          const year = parseInt(dateParts[0], 10);
          const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
          const day = parseInt(dateParts[2], 10);

          const date = new Date(year, month, day);
          return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
          });
        }
        return '';
      } catch (error) {
        console.error('Error formatting date:', error);
        return '';
      }
    }
    return '';
  };

  const formattedDate = formatDate();

  return (
    <TouchableOpacity
      style={[styles.listItem, { borderLeftWidth: 4, borderLeftColor: statusColor }]}
      onPress={() => onPress && onPress(item)}
      activeOpacity={0.7}
    >
      {/* Date column for appointments */}
      {item.type === 'appointment' && item.appointmentDate && (
        <View style={[styles.dateColumn, { backgroundColor: `${statusColor}15` }]}>
          <Text style={[styles.dateDay, { color: statusColor }]}>
            {(() => {
              // Parse the date from YYYY-MM-DD format
              const dateParts = item.appointmentDate.split('-');
              if (dateParts.length === 3) {
                const year = parseInt(dateParts[0], 10);
                const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
                const day = parseInt(dateParts[2], 10);

                const date = new Date(year, month, day);
                return date.getDate();
              }
              return '';
            })()}
          </Text>
          <Text style={[styles.dateMonth, { color: statusColor }]}>
            {(() => {
              // Parse the date from YYYY-MM-DD format
              const dateParts = item.appointmentDate.split('-');
              if (dateParts.length === 3) {
                const year = parseInt(dateParts[0], 10);
                const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
                const day = parseInt(dateParts[2], 10);

                const date = new Date(year, month, day);
                return date.toLocaleString('default', { month: 'short' });
              }
              return '';
            })()}
          </Text>
        </View>
      )}

      {/* Icon for non-appointment items or as fallback */}
      {(item.type !== 'appointment' || !item.appointmentDate) && (
        <View style={[styles.iconContainer, { backgroundColor: `${statusColor}20` }]}>
          <Ionicons name={getIconName(item.type)} size={20} color={statusColor} />
        </View>
      )}

      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemTitle}>{item.title}</Text>
          <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
            <Text style={[styles.statusText, { color: statusColor }]}>{statusText}</Text>
          </View>
        </View>

        <Text style={styles.itemDescription}>{item.description}</Text>

        <View style={styles.timeContainer}>
          <Ionicons name="time-outline" size={14} color="#757575" style={styles.timeIcon} />
          <Text style={styles.itemTime}>{item.time}</Text>

          {item.duration && (
            <View style={styles.durationContainer}>
              <Ionicons name="hourglass-outline" size={14} color="#757575" style={styles.timeIcon} />
              <Text style={styles.itemTime}>{item.duration}</Text>
            </View>
          )}
        </View>
      </View>
      <View style={styles.chevronContainer}>
        <Ionicons name="chevron-forward" size={20} color={statusColor} />
      </View>
    </TouchableOpacity>
  );
};

const UpcomingList = ({
  title = 'Upcoming',
  data = [],
  onItemPress,
  onViewAll,
  emptyText = 'No upcoming items',
  backgroundColor = '#fff',
  maxItems = 5,
  style,
}) => {
  // Debugging: Log the data being passed to UpcomingList
  console.log(`UpcomingList (${title}) received data:`, data);

  // Vérifier si les données sont valides
  const validData = Array.isArray(data) ? data : [];

  const hasItems = validData && validData.length > 0;
  const displayData = hasItems ? validData.slice(0, maxItems) : [];
  const hasMore = hasItems && validData.length > maxItems;

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {title && (
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          {hasMore && (
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={onViewAll}
              activeOpacity={0.7}
            >
              <Text style={styles.viewAll}>View All</Text>
              <Ionicons name="chevron-forward" size={16} color="#1976D2" />
            </TouchableOpacity>
          )}
        </View>
      )}

      {hasItems ? (
        <View>
          {displayData.map((item, index) => (
            <React.Fragment key={`${item.id || index}`}>
              {index > 0 && <View style={{ height: 8 }} />}
              <ListItem item={item} onPress={onItemPress} />
            </React.Fragment>
          ))}
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={48} color="#E0E0E0" />
          <Text style={styles.emptyText}>{emptyText}</Text>
          {title.toLowerCase().includes('appointment') && (
            <TouchableOpacity
              style={styles.emptyActionButton}
              onPress={onViewAll}
              activeOpacity={0.7}
            >
              <Ionicons name="add-circle-outline" size={16} color="#FFFFFF" style={{ marginRight: 6 }} />
              <Text style={styles.emptyActionButtonText}>Request Appointment</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
  },
  viewAll: {
    fontSize: 14,
    color: '#1976D2',
    fontWeight: '500',
    marginRight: 4,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
    marginBottom: 8,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  // Date column for appointments
  dateColumn: {
    width: 48,
    height: 48,
    borderRadius: 10,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  dateDay: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1976D2',
  },
  dateMonth: {
    fontSize: 12,
    color: '#1976D2',
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  // Icon container for non-appointment items
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212121',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  itemDescription: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 6,
    lineHeight: 20,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  timeIcon: {
    marginRight: 4,
  },
  itemTime: {
    fontSize: 12,
    color: '#757575',
    fontWeight: '500',
  },
  chevronContainer: {
    marginLeft: 8,
    opacity: 0.7,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
  },
  emptyText: {
    marginTop: 10,
    marginBottom: 16,
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
  },
  emptyActionButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  emptyActionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default UpcomingList;