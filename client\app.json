{"expo": {"name": "NeuroCare", "slug": "NeuroCare", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "NeuroCare needs access to your location to show your position on the map and save your favorite locations.", "NSCameraUsageDescription": "This app uses the camera to scan QR codes of patients"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.kiritogx.NeuroCare", "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "NeuroCare needs access to your location to show your position on the map and save your favorite locations."}], ["expo-image-picker", {"photosPermission": "NeuroCare needs access to your photos to upload images", "cameraPermission": "NeuroCare needs camera access to scan QR codes for patient identification"}], ["expo-barcode-scanner", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera to scan QR codes"}]]}}