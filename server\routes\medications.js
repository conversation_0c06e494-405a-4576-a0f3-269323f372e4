const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');

// Initialize Firestore
const db = admin.firestore();

/**
 * @route   POST /api/medications
 * @desc    Add a new medication
 * @access  Private
 */
router.post('/', verifyToken, async (req, res) => {
    try {
        const { name, dosage, frequency, frequencyType, frequencyDays, frequencyTimes, instructions } = req.body;
        const userId = req.user.uid;

        // Validate required fields
        if (!name) {
            return res.status(400).json({ error: 'Medication name is required' });
        }

        const medicationData = {
            patientId: userId,
            name,
            dosage,
            frequency,
            frequencyType,
            frequencyDays,
            frequencyTimes,
            instructions,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        };

        const docRef = await db.collection('medications').add(medicationData);
        
        // Get the newly created document to return with server timestamp
        const newDoc = await docRef.get();
        
        res.status(201).json({
            id: docRef.id,
            ...newDoc.data()
        });
    } catch (error) {
        console.error('Error adding medication:', error);
        res.status(500).json({ error: 'Failed to add medication' });
    }
});

/**
 * @route   GET /api/medications
 * @desc    Get all medications for the authenticated user
 * @access  Private
 */
router.get('/', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;

        const snapshot = await db.collection('medications')
            .where('patientId', '==', userId)
            .orderBy('createdAt', 'desc')
            .get();

        const medications = [];
        snapshot.forEach(doc => {
            medications.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(medications);
    } catch (error) {
        console.error('Error getting medications:', error);
        res.status(500).json({ error: 'Failed to get medications' });
    }
});

/**
 * @route   GET /api/medications/:id
 * @desc    Get a specific medication by ID
 * @access  Private
 */
router.get('/:id', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const medicationId = req.params.id;

        const docRef = await db.collection('medications').doc(medicationId).get();

        if (!docRef.exists) {
            return res.status(404).json({ error: 'Medication not found' });
        }

        const medicationData = docRef.data();

        // Ensure the user owns this medication
        if (medicationData.patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to access this medication' });
        }

        res.json({
            id: docRef.id,
            ...medicationData
        });
    } catch (error) {
        console.error('Error getting medication:', error);
        res.status(500).json({ error: 'Failed to get medication' });
    }
});

/**
 * @route   PUT /api/medications/:id
 * @desc    Update a medication
 * @access  Private
 */
router.put('/:id', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const medicationId = req.params.id;
        const updateData = req.body;

        // Get the medication to check ownership
        const docRef = db.collection('medications').doc(medicationId);
        const doc = await docRef.get();

        if (!doc.exists) {
            return res.status(404).json({ error: 'Medication not found' });
        }

        // Ensure the user owns this medication
        if (doc.data().patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to update this medication' });
        }

        // Add updated timestamp
        updateData.updatedAt = admin.firestore.FieldValue.serverTimestamp();

        await docRef.update(updateData);

        // Get the updated document
        const updatedDoc = await docRef.get();

        res.json({
            id: medicationId,
            ...updatedDoc.data()
        });
    } catch (error) {
        console.error('Error updating medication:', error);
        res.status(500).json({ error: 'Failed to update medication' });
    }
});

/**
 * @route   DELETE /api/medications/:id
 * @desc    Delete a medication
 * @access  Private
 */
router.delete('/:id', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const medicationId = req.params.id;

        // Get the medication to check ownership
        const docRef = db.collection('medications').doc(medicationId);
        const doc = await docRef.get();

        if (!doc.exists) {
            return res.status(404).json({ error: 'Medication not found' });
        }

        // Ensure the user owns this medication
        if (doc.data().patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to delete this medication' });
        }

        await docRef.delete();

        // Also delete any reminders for this medication
        const remindersQuery = await db.collection('medicationReminders')
            .where('medicationId', '==', medicationId)
            .get();

        const batch = db.batch();
        remindersQuery.forEach(doc => {
            batch.delete(doc.ref);
        });
        
        await batch.commit();

        res.json({ success: true, message: 'Medication deleted successfully' });
    } catch (error) {
        console.error('Error deleting medication:', error);
        res.status(500).json({ error: 'Failed to delete medication' });
    }
});

/**
 * @route   POST /api/medications/reminders
 * @desc    Add a medication reminder
 * @access  Private
 */
router.post('/reminders', verifyToken, async (req, res) => {
    try {
        const { medicationId, medicationName, dosage, instructions, scheduledTime } = req.body;
        const userId = req.user.uid;

        // Validate required fields
        if (!medicationId || !scheduledTime) {
            return res.status(400).json({ error: 'Medication ID and scheduled time are required' });
        }

        const reminderData = {
            patientId: userId,
            medicationId,
            medicationName,
            dosage,
            instructions,
            scheduledTime: new Date(scheduledTime),
            status: 'scheduled', // scheduled, completed, missed
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };

        const docRef = await db.collection('medicationReminders').add(reminderData);
        
        // Get the newly created document to return with server timestamp
        const newDoc = await docRef.get();
        
        res.status(201).json({
            id: docRef.id,
            ...newDoc.data()
        });
    } catch (error) {
        console.error('Error adding medication reminder:', error);
        res.status(500).json({ error: 'Failed to add medication reminder' });
    }
});

/**
 * @route   GET /api/medications/reminders
 * @desc    Get all medication reminders for the authenticated user
 * @access  Private
 */
router.get('/reminders', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;

        const snapshot = await db.collection('medicationReminders')
            .where('patientId', '==', userId)
            .orderBy('scheduledTime', 'asc')
            .get();

        const reminders = [];
        snapshot.forEach(doc => {
            reminders.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(reminders);
    } catch (error) {
        console.error('Error getting medication reminders:', error);
        res.status(500).json({ error: 'Failed to get medication reminders' });
    }
});

/**
 * @route   PUT /api/medications/reminders/:id
 * @desc    Update a medication reminder status
 * @access  Private
 */
router.put('/reminders/:id', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const reminderId = req.params.id;
        const { status } = req.body;

        // Validate status
        if (!status || !['scheduled', 'completed', 'missed'].includes(status)) {
            return res.status(400).json({ error: 'Valid status is required (scheduled, completed, missed)' });
        }

        // Get the reminder to check ownership
        const docRef = db.collection('medicationReminders').doc(reminderId);
        const doc = await docRef.get();

        if (!doc.exists) {
            return res.status(404).json({ error: 'Reminder not found' });
        }

        // Ensure the user owns this reminder
        if (doc.data().patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to update this reminder' });
        }

        // Update the status
        await docRef.update({
            status,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Get the updated document
        const updatedDoc = await docRef.get();

        res.json({
            id: reminderId,
            ...updatedDoc.data()
        });
    } catch (error) {
        console.error('Error updating medication reminder:', error);
        res.status(500).json({ error: 'Failed to update medication reminder' });
    }
});

module.exports = router;
