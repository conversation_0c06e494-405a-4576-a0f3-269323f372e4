import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ROLE_COLORS } from '../../config/theme';
import { Animated } from 'react-native';
import axios from 'axios';
import VideoCall from '../video/VideoCall';



const ChatRoom = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const route = useRoute();
  const scrollViewRef = useRef();

  // State for UI and controls
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [showChat, setShowChat] = useState(true);
  const [showParticipants, setShowParticipants] = useState(false);
  const [showMedicalNotes, setShowMedicalNotes] = useState(false);
  const [medicalNote, setMedicalNote] = useState('');
  const [consultationTime, setConsultationTime] = useState(0);
  const [callId, setCallId] = useState(null);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [isMicOn, setIsMicOn] = useState(true);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [roomName, setRoomName] = useState('');

  // Animation values
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Get patient info and call parameters from route params
  const patientInfo = route.params?.patientInfo || {};
  const startVideoCall = route.params?.startVideoCall || false;
  const initialRoomName = route.params?.roomName;

  // Initialize chat session
  useEffect(() => {
    const initializeChat = async () => {
      try {
        // Check if user data is available
        if (!user || !user.uid) {
          console.error('User data is not available or incomplete');
          throw new Error('User data is not available');
        }

        // Create a unique chat ID using user.uid instead of user.id
        const chatId = `neurocare_chat_${user.uid}_${patientInfo.id}_${Date.now()}`;

        // Create a new chat record in the backend
        const chatResponse = await axios.post('/api/communication/create-chat', {
          doctorId: user.uid, // Use uid instead of id
          patientId: patientInfo.id,
          chatId: chatId
        });

        if (chatResponse.data && chatResponse.data.chatId) {
          setCallId(chatResponse.data.chatId);
        }

      } catch (error) {
        console.error('Error initializing chat:', error);

        // Provide more detailed error logging
        if (error.message === 'Network Error') {
          console.error('Network Error Details:');
          console.error('- API Endpoint: /api/communication/create-chat');

          // Use user.uid instead of user.id and check if it exists
          const doctorId = user?.uid || 'unknown';
          console.error('- Request Data:', { doctorId, patientId: patientInfo.id });

          // Use a fallback local ID for development/testing when backend is unavailable
          const fallbackId = `local_chat_${doctorId}_${patientInfo.id}_${Date.now()}`;
          console.log('Using fallback chat ID:', fallbackId);
          setCallId(fallbackId);

          // Show a less disruptive warning instead of forcing navigation back
          Alert.alert(
            'Connection Warning',
            'Unable to connect to the server. Some features may be limited.',
            [{ text: 'Continue Anyway' }]
          );
          return;
        }

        // For other errors, show the standard error message
        Alert.alert(
          'Chat Error',
          'Failed to initialize the chat. Please try again.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    };

    initializeChat();
  }, []);

  // Animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true
      }),
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true
      })
    ]).start();
  }, []);

  // Auto-start video call if requested
  useEffect(() => {
    if (startVideoCall && initialRoomName) {
      // Set a short delay to ensure the component is fully mounted
      const timer = setTimeout(() => {
        setShowVideoCall(true);
        setRoomName(initialRoomName);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [startVideoCall, initialRoomName]);

  // Timer for consultation duration
  useEffect(() => {
    const timer = setInterval(() => {
      setConsultationTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Send a new message
  const sendMessage = async () => {
    if (message.trim() === '') return;

    // Create the message object first so we can add it to UI immediately
    const newMessage = {
      id: Date.now(),
      sender: 'doctor',
      senderName: `Dr. ${user?.lastName || 'Unknown'}`,
      text: message,
      timestamp: new Date(),
      status: 'sending' // Add a status to track message state
    };

    // Add message to UI immediately for better UX
    const updatedMessages = [...messages, newMessage];
    setMessages(updatedMessages);
    setMessage('');

    // Scroll to bottom
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Send message to the backend
      await axios.post(`/api/communication/chat/${callId}/message`, {
        content: message,
        type: 'text'
      });

      // Update message status to sent
      const sentMessages = updatedMessages.map(msg =>
        msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
      );
      setMessages(sentMessages);
    } catch (error) {
      console.error('Error sending message:', error);

      if (error.message === 'Network Error') {
        console.error('Network Error Details:');
        console.error('- API Endpoint:', `/api/communication/chat/${callId}/message`);
        console.error('- Message content:', message);

        // Update message status to indicate error
        const errorMessages = updatedMessages.map(msg =>
          msg.id === newMessage.id ? { ...msg, status: 'error' } : msg
        );
        setMessages(errorMessages);

        // Show a toast or non-blocking notification instead of an alert
        Alert.alert(
          'Message Not Sent',
          'Unable to send message due to network issues. The message is saved locally.',
          [{ text: 'OK' }]
        );
      } else {
        // For other errors
        Alert.alert('Error', 'Failed to send message. Please try again.');
      }
    }
  };

  // Fetch messages from the server
  useEffect(() => {
    if (!callId) return;

    const fetchMessages = async () => {
      try {
        const response = await axios.get(`/api/communication/chat/${callId}/messages?limit=50`);
        if (response.data) {
          const formattedMessages = response.data.map(msg => ({
            id: msg.id,
            sender: msg.senderId === user?.uid ? 'doctor' : 'patient',
            senderName: msg.senderId === user?.uid ?
              `Dr. ${user?.lastName || 'Unknown'}` :
              `${patientInfo.firstName || 'Patient'} ${patientInfo.lastName || ''}`,
            text: msg.content,
            timestamp: new Date(msg.timestamp),
            status: 'sent'
          }));

          // Preserve locally created messages that haven't been sent yet
          const localMessages = messages.filter(msg =>
            msg.status === 'sending' || msg.status === 'error'
          );

          // Combine server messages with local messages
          const combinedMessages = [...formattedMessages, ...localMessages];

          // Sort by timestamp
          combinedMessages.sort((a, b) => a.timestamp - b.timestamp);

          setMessages(combinedMessages);

          // Scroll to bottom
          setTimeout(() => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }
      } catch (error) {
        console.error('Error fetching messages:', error);

        if (error.message === 'Network Error') {
          console.error('Network Error Details:');
          console.error('- API Endpoint:', `/api/communication/chat/${callId}/messages`);

          // Don't show an alert for network errors during background fetching
          // as it would be disruptive to the user experience
          console.log('Using cached messages due to network error');
        }
      }
    };

    // Initial fetch
    fetchMessages();

    // Set up polling for new messages
    const messageInterval = setInterval(fetchMessages, 10000);

    return () => clearInterval(messageInterval);
  }, [callId]);

  // Toggle sidebar panels
  const togglePanel = (panel) => {
    if (panel === 'chat') {
      setShowChat(true);
      setShowParticipants(false);
      setShowMedicalNotes(false);
    } else if (panel === 'participants') {
      setShowChat(false);
      setShowParticipants(true);
      setShowMedicalNotes(false);
    } else if (panel === 'notes') {
      setShowChat(false);
      setShowParticipants(false);
      setShowMedicalNotes(true);
    }

    // Animate panel
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true
    }).start();
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // End consultation
  const endConsultation = async () => {
    try {
      if (callId) {
        // End the call on the server
        await axios.post(`/api/video/${callId}/end`);
      }

      // Animation for closing
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true
        })
      ]).start(() => {
        navigation.goBack();
      });
    } catch (error) {
      console.error('Error ending consultation:', error);
      navigation.goBack();
    }
  };

  // Save medical notes
  const saveMedicalNotes = async () => {
    if (!medicalNote.trim()) {
      Alert.alert('Error', 'Please enter consultation notes before saving.');
      return;
    }

    try {
      await axios.post(`/api/medical/consultations/${callId}/notes`, {
        notes: medicalNote
      });

      Alert.alert('Success', 'Consultation notes saved successfully.');
    } catch (error) {
      console.error('Error saving notes:', error);
      Alert.alert('Error', 'Failed to save notes. Please try again.');
    }
  };

  // Create prescription
  const createPrescription = () => {
    navigation.navigate('NewPrescription', {
      patientInfo: patientInfo,
      consultationId: callId,
      notes: medicalNote
    });
  };



  // Doctor colors
  const doctorColors = ROLE_COLORS.doctor;



  return (
    <Animated.View
      style={[
        styles.animatedContainer,
        {
          opacity: fadeAnim,
          transform: [
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [50, 0]
              })
            }
          ]
        }
      ]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <LinearGradient
          colors={[doctorColors.primaryLight, '#f5f5f5']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 0.2 }}
          style={styles.container}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={endConsultation}
              >
                <Ionicons name="arrow-back" size={24} color="#fff" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>
                Consultation with {patientInfo.firstName} {patientInfo.lastName}
              </Text>
            </View>
            <View style={styles.headerRight}>
              <Text style={styles.timer}>{formatTime(consultationTime)}</Text>
            </View>
          </View>

          <View style={styles.content}>
            {/* Video call container */}
            <View style={styles.videoContainer}>
              {showVideoCall ? (
                <VideoCall
                  patientInfo={patientInfo}
                  onCallEnd={() => setShowVideoCall(false)}
                  callId={callId}
                  setCallId={setCallId}
                  initialRoomName={initialRoomName}
                />
              ) : (
                <View style={styles.startCallContainer}>
                  <TouchableOpacity
                    style={styles.startCallButton}
                    onPress={() => setShowVideoCall(true)}
                  >
                    <Ionicons name="videocam" size={40} color="#fff" />
                    <Text style={styles.startCallText}>Start Video Call</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* Chat panel */}
            <View style={styles.sidePanel}>
                {/* Panel tabs */}
                <View style={styles.panelTabs}>
                  <TouchableOpacity
                    style={[styles.panelTab, showChat && styles.activeTab]}
                    onPress={() => togglePanel('chat')}
                  >
                    <Ionicons
                      name="chatbubble"
                      size={20}
                      color={showChat ? doctorColors.primary : '#757575'}
                    />
                    <Text style={[styles.tabText, showChat && styles.activeTabText]}>Chat</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.panelTab, showParticipants && styles.activeTab]}
                    onPress={() => togglePanel('participants')}
                  >
                    <Ionicons
                      name="people"
                      size={20}
                      color={showParticipants ? doctorColors.primary : '#757575'}
                    />
                    <Text style={[styles.tabText, showParticipants && styles.activeTabText]}>Participants</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.panelTab, showMedicalNotes && styles.activeTab]}
                    onPress={() => togglePanel('notes')}
                  >
                    <Ionicons
                      name="document-text"
                      size={20}
                      color={showMedicalNotes ? doctorColors.primary : '#757575'}
                    />
                    <Text style={[styles.tabText, showMedicalNotes && styles.activeTabText]}>Notes</Text>
                  </TouchableOpacity>
                </View>

                {/* Panel content */}
                <Animated.View
                  style={[
                    styles.panelContent,
                    { transform: [{ translateX: slideAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0]
                    })}] }
                  ]}
                >
                  {showChat && (
                    <View style={styles.chatContainer}>
                      <ScrollView
                        ref={scrollViewRef}
                        style={styles.messagesContainer}
                        contentContainerStyle={styles.messagesContent}
                      >
                        {messages.length === 0 && (
                          <Text style={styles.noMessagesText}>
                            No messages yet. Start the conversation!
                          </Text>
                        )}
                        {messages.map((msg) => (
                          <View
                            key={msg.id}
                            style={[
                              styles.messageItem,
                              msg.sender === 'doctor' ? styles.doctorMessage : styles.patientMessage
                            ]}
                          >
                            <View style={styles.messageHeader}>
                              <Text style={styles.messageSender}>{msg.senderName}</Text>
                              <Text style={styles.messageTime}>{formatTimestamp(msg.timestamp)}</Text>
                            </View>
                            <Text style={styles.messageText}>{msg.text}</Text>
                            {msg.status && msg.sender === 'doctor' && (
                              <View style={styles.messageStatus}>
                                {msg.status === 'sending' && (
                                  <Text style={styles.statusText}>Sending...</Text>
                                )}
                                {msg.status === 'error' && (
                                  <View style={styles.errorStatus}>
                                    <Text style={styles.errorStatusText}>Not sent</Text>
                                    <TouchableOpacity
                                      onPress={() => {
                                        // Remove the message with error status
                                        const filteredMessages = messages.filter(m => m.id !== msg.id);
                                        setMessages(filteredMessages);
                                        // Set the message text back to input for retry
                                        setMessage(msg.text);
                                      }}
                                    >
                                      <Text style={styles.retryText}>Retry</Text>
                                    </TouchableOpacity>
                                  </View>
                                )}
                              </View>
                            )}
                          </View>
                        ))}
                      </ScrollView>

                      <View style={styles.inputContainer}>
                        <TextInput
                          style={styles.input}
                          value={message}
                          onChangeText={setMessage}
                          placeholder="Type a message..."
                          placeholderTextColor="#999"
                          multiline
                        />
                        <TouchableOpacity
                          style={styles.sendButton}
                          onPress={sendMessage}
                        >
                          <Ionicons name="send" size={24} color={doctorColors.primary} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}

                  {showParticipants && (
                    <View style={styles.participantsContainer}>
                      <View style={styles.participant}>
                        <View style={styles.participantAvatar}>
                          <Text style={styles.avatarText}>
                            {user.firstName?.charAt(0) || 'D'}
                          </Text>
                        </View>
                        <View style={styles.participantInfo}>
                          <Text style={styles.participantName}>
                            Dr. {user.lastName} (You)
                          </Text>
                          <Text style={styles.participantRole}>Doctor</Text>
                        </View>
                        <View style={styles.participantStatus}>
                          <Ionicons name="mic" size={16} color={isMicOn ? "#4CAF50" : "#F44336"} />
                          <Ionicons name="videocam" size={16} color={isCameraOn ? "#4CAF50" : "#F44336"} />
                        </View>
                      </View>

                      <View style={styles.participant}>
                        <View style={[styles.participantAvatar, styles.patientAvatar]}>
                          <Text style={styles.avatarText}>
                            {patientInfo.firstName?.charAt(0)}
                          </Text>
                        </View>
                        <View style={styles.participantInfo}>
                          <Text style={styles.participantName}>
                            {patientInfo.firstName} {patientInfo.lastName}
                          </Text>
                          <Text style={styles.participantRole}>Patient</Text>
                        </View>
                      </View>

                      <View style={styles.inviteContainer}>
                        <TouchableOpacity
                          style={styles.inviteButton}
                          onPress={() => {
                            // This functionality requires the Jitsi Meet API
                            Alert.alert('Info', 'Invite functionality is not available in this version.');
                          }}
                        >
                          <Ionicons name="person-add" size={20} color="#fff" />
                          <Text style={styles.inviteText}>Invite Specialist</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}

                  {showMedicalNotes && (
                    <View style={styles.notesContainer}>
                      <Text style={styles.notesTitle}>Consultation Notes</Text>
                      <Text style={styles.notesSubtitle}>
                        Patient: {patientInfo.firstName} {patientInfo.lastName}, {patientInfo.age} years
                      </Text>

                      <TextInput
                        style={styles.notesInput}
                        value={medicalNote}
                        onChangeText={setMedicalNote}
                        placeholder="Enter consultation notes here..."
                        placeholderTextColor="#999"
                        multiline
                        numberOfLines={10}
                      />

                      <View style={styles.notesActions}>
                        <TouchableOpacity
                          style={styles.notesButton}
                          onPress={saveMedicalNotes}
                        >
                          <Ionicons name="save" size={20} color="#fff" />
                          <Text style={styles.notesButtonText}>Save Notes</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.notesButton, styles.prescriptionButton]}
                          onPress={createPrescription}
                        >
                          <Ionicons name="medical" size={20} color="#fff" />
                          <Text style={styles.notesButtonText}>Create Prescription</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                </Animated.View>
              </View>
          </View>
        </LinearGradient>
      </KeyboardAvoidingView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  animatedContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#555',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ROLE_COLORS.doctor.primary,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timer: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  videoContainer: {
    flex: 2,
    backgroundColor: '#000',
    borderRadius: 8,
    margin: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  startCallContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  startCallButton: {
    backgroundColor: ROLE_COLORS.doctor.primary,
    padding: 20,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  startCallText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
    fontWeight: 'bold',
  },
  fullScreenVideo: {
    flex: 1,
  },
  jitsiView: {
    flex: 1,
    height: '100%',
    width: '100%',
  },
  noMessagesText: {
    textAlign: 'center',
    color: '#999',
    marginTop: 20,
  },
  videoControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 10,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  controlButtonOff: {
    backgroundColor: '#F44336',
  },
  controlButtonActive: {
    backgroundColor: '#4CAF50',
  },
  endCallButton: {
    backgroundColor: '#F44336',
    transform: [{ rotate: '135deg' }],
  },
  sidePanel: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 8,
    overflow: 'hidden',
    maxWidth: 320,
  },
  panelTabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  panelTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    backgroundColor: '#f5f5f5',
  },
  activeTab: {
    backgroundColor: '#fff',
    borderBottomWidth: 2,
    borderBottomColor: ROLE_COLORS.doctor.primary,
  },
  tabText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#757575',
  },
  activeTabText: {
    color: ROLE_COLORS.doctor.primary,
    fontWeight: '500',
  },
  panelContent: {
    flex: 1,
  },
  chatContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 12,
  },
  messageItem: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    maxWidth: '85%',
  },
  doctorMessage: {
    alignSelf: 'flex-end',
    backgroundColor: ROLE_COLORS.doctor.primaryLighter,
    borderBottomRightRadius: 0,
  },
  patientMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#f0f0f0',
    borderBottomLeftRadius: 0,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  messageSender: {
    fontSize: 12,
    fontWeight: '500',
    color: '#616161',
  },
  messageTime: {
    fontSize: 10,
    color: '#9e9e9e',
    marginLeft: 8,
  },
  messageText: {
    fontSize: 14,
    color: '#212121',
  },
  messageStatus: {
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  statusText: {
    fontSize: 10,
    color: '#757575',
    fontStyle: 'italic',
  },
  errorStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorStatusText: {
    fontSize: 10,
    color: '#F44336',
    marginRight: 4,
  },
  retryText: {
    fontSize: 10,
    color: ROLE_COLORS.doctor.primary,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  input: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 14,
  },
  sendButton: {
    marginLeft: 8,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantsContainer: {
    flex: 1,
    padding: 16,
  },
  participant: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  participantAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ROLE_COLORS.doctor.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  patientAvatar: {
    backgroundColor: ROLE_COLORS.patient.primary,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  participantInfo: {
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
  },
  participantRole: {
    fontSize: 12,
    color: '#757575',
  },
  participantStatus: {
    flexDirection: 'row',
    width: 50,
    justifyContent: 'space-between',
  },
  inviteContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  inviteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ROLE_COLORS.doctor.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  inviteText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: '500',
  },
  notesContainer: {
    flex: 1,
    padding: 16,
  },
  notesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 4,
  },
  notesSubtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 16,
  },
  notesInput: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#212121',
    textAlignVertical: 'top',
    minHeight: 200,
  },
  notesActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  notesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ROLE_COLORS.doctor.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  prescriptionButton: {
    backgroundColor: '#4CAF50',
  },
  notesButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: '500',
  },
});

export default ChatRoom;
