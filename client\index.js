import 'react-native-gesture-handler';
import { registerRootComponent } from 'expo';
import { enableScreens } from 'react-native-screens';
import 'expo/build/Expo.fx';
import { Platform, LogBox } from 'react-native';
import App from './App';

// Ignore Firebase errors
LogBox.ignoreLogs([
  'Native module RNFBAppModule not found',
  'RCTBridge required dispatch_sync',
  'Cannot read property',
  'TypeError',
  'undefined is not an object'
]);

// Enable native screens implementation
enableScreens();

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
