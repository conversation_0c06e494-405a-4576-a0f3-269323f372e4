const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const db = admin.firestore();

// Middleware to verify authentication
const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Invalid token' });
    }
};

// Middleware to verify doctor role
const verifyDoctor = async (req, res, next) => {
    try {
        const userDoc = await db.collection('users').doc(req.user.uid).get();
        if (!userDoc.exists || userDoc.data().role !== 'doctor') {
            return res.status(403).json({ error: 'Access denied. Doctor role required.' });
        }
        next();
    } catch (error) {
        res.status(500).json({ error: 'Error verifying doctor role' });
    }
};

// Add vital signs
router.post('/vital-signs', authenticateUser, async (req, res) => {
    try {
        const { patientId, vitalSigns } = req.body;
        
        // Validate required fields
        if (!patientId || !vitalSigns) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        const vitalSignsData = {
            userId: patientId,
            ...vitalSigns,
            recordedBy: req.user.uid,
            timestamp: admin.firestore.FieldValue.serverTimestamp()
        };

        const docRef = await db.collection('vitalSigns').add(vitalSignsData);
        res.status(201).json({ id: docRef.id, ...vitalSignsData });
    } catch (error) {
        res.status(500).json({ error: 'Error adding vital signs' });
    }
});

// Get patient's vital signs
router.get('/vital-signs/:patientId', authenticateUser, async (req, res) => {
    try {
        const { patientId } = req.params;
        const { startDate, endDate } = req.query;

        let query = db.collection('vitalSigns')
            .where('userId', '==', patientId)
            .orderBy('timestamp', 'desc');

        if (startDate && endDate) {
            query = query.where('timestamp', '>=', new Date(startDate))
                        .where('timestamp', '<=', new Date(endDate));
        }

        const snapshot = await query.get();
        const vitalSigns = [];
        snapshot.forEach(doc => {
            vitalSigns.push({ id: doc.id, ...doc.data() });
        });

        res.json(vitalSigns);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching vital signs' });
    }
});

// Add medical record
router.post('/medical-records', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const { patientId, record } = req.body;
        
        if (!patientId || !record) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        const medicalRecord = {
            patientId,
            doctorId: req.user.uid,
            ...record,
            date: admin.firestore.FieldValue.serverTimestamp(),
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };

        const docRef = await db.collection('medicalRecords').add(medicalRecord);
        res.status(201).json({ id: docRef.id, ...medicalRecord });
    } catch (error) {
        res.status(500).json({ error: 'Error adding medical record' });
    }
});

// Get patient's medical records
router.get('/medical-records/:patientId', authenticateUser, async (req, res) => {
    try {
        const { patientId } = req.params;
        const { type, startDate, endDate } = req.query;

        let query = db.collection('medicalRecords')
            .where('patientId', '==', patientId)
            .orderBy('date', 'desc');

        if (type) {
            query = query.where('type', '==', type);
        }

        if (startDate && endDate) {
            query = query.where('date', '>=', new Date(startDate))
                        .where('date', '<=', new Date(endDate));
        }

        const snapshot = await query.get();
        const records = [];
        snapshot.forEach(doc => {
            records.push({ id: doc.id, ...doc.data() });
        });

        res.json(records);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching medical records' });
    }
});

// Create doctor-patient relationship
router.post('/doctor-patient', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const { patientId, treatmentPlan, followUpSchedule } = req.body;

        if (!patientId) {
            return res.status(400).json({ error: 'Patient ID is required' });
        }

        // Check if relationship already exists
        const existingRelation = await db.collection('doctorPatientRelations')
            .where('doctorId', '==', req.user.uid)
            .where('patientId', '==', patientId)
            .get();

        if (!existingRelation.empty) {
            return res.status(400).json({ error: 'Relationship already exists' });
        }

        const relationship = {
            doctorId: req.user.uid,
            patientId,
            status: 'active',
            startDate: admin.firestore.FieldValue.serverTimestamp(),
            treatmentPlan,
            followUpSchedule,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };

        const docRef = await db.collection('doctorPatientRelations').add(relationship);
        res.status(201).json({ id: docRef.id, ...relationship });
    } catch (error) {
        res.status(500).json({ error: 'Error creating doctor-patient relationship' });
    }
});

// Get doctor's patients
router.get('/doctor-patients', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const snapshot = await db.collection('doctorPatientRelations')
            .where('doctorId', '==', req.user.uid)
            .where('status', '==', 'active')
            .get();

        const relationships = [];
        for (const doc of snapshot.docs) {
            const relationship = { id: doc.id, ...doc.data() };
            // Get patient details
            const patientDoc = await db.collection('users').doc(relationship.patientId).get();
            if (patientDoc.exists) {
                relationship.patient = patientDoc.data();
            }
            relationships.push(relationship);
        }

        res.json(relationships);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching doctor-patient relationships' });
    }
});

// Update doctor-patient relationship
router.put('/doctor-patient/:relationshipId', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const { relationshipId } = req.params;
        const updates = req.body;

        const docRef = db.collection('doctorPatientRelations').doc(relationshipId);
        const doc = await docRef.get();

        if (!doc.exists) {
            return res.status(404).json({ error: 'Relationship not found' });
        }

        if (doc.data().doctorId !== req.user.uid) {
            return res.status(403).json({ error: 'Not authorized to update this relationship' });
        }

        await docRef.update({
            ...updates,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.json({ id: relationshipId, ...updates });
    } catch (error) {
        res.status(500).json({ error: 'Error updating doctor-patient relationship' });
    }
});

// Get patient's complete medical history
router.get('/patient-history/:patientId', authenticateUser, async (req, res) => {
    try {
        const { patientId } = req.params;
        
        // Get patient's basic info
        const patientDoc = await db.collection('users').doc(patientId).get();
        if (!patientDoc.exists) {
            return res.status(404).json({ error: 'Patient not found' });
        }
        
        const patientData = patientDoc.data();
        
        // Get vital signs history
        const vitalSignsSnapshot = await db.collection('vitalSigns')
            .where('userId', '==', patientId)
            .orderBy('timestamp', 'desc')
            .limit(10)
            .get();
            
        const vitalSigns = [];
        vitalSignsSnapshot.forEach(doc => {
            vitalSigns.push({ id: doc.id, ...doc.data() });
        });
        
        // Get medical records
        const medicalRecordsSnapshot = await db.collection('medicalRecords')
            .where('patientId', '==', patientId)
            .orderBy('date', 'desc')
            .get();
            
        const medicalRecords = [];
        medicalRecordsSnapshot.forEach(doc => {
            medicalRecords.push({ id: doc.id, ...doc.data() });
        });
        
        // Get appointments history
        const appointmentsSnapshot = await db.collection('appointments')
            .where('patientId', '==', patientId)
            .orderBy('dateTime', 'desc')
            .get();
            
        const appointments = [];
        appointmentsSnapshot.forEach(doc => {
            appointments.push({ id: doc.id, ...doc.data() });
        });
        
        // Get doctor relationships
        const relationshipsSnapshot = await db.collection('doctorPatientRelations')
            .where('patientId', '==', patientId)
            .where('status', '==', 'active')
            .get();
            
        const relationships = [];
        for (const doc of relationshipsSnapshot.docs) {
            const relationship = { id: doc.id, ...doc.data() };
            const doctorDoc = await db.collection('users').doc(relationship.doctorId).get();
            if (doctorDoc.exists) {
                relationship.doctor = doctorDoc.data();
            }
            relationships.push(relationship);
        }
        
        res.json({
            patient: patientData,
            vitalSigns,
            medicalRecords,
            appointments,
            relationships
        });
    } catch (error) {
        res.status(500).json({ error: 'Error fetching patient history' });
    }
});

// Add lab results
router.post('/lab-results', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const { patientId, results } = req.body;
        
        if (!patientId || !results) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        
        const labRecord = {
            patientId,
            doctorId: req.user.uid,
            type: 'lab',
            title: results.title || 'Lab Results',
            description: results.description,
            results: results.data,
            date: admin.firestore.FieldValue.serverTimestamp(),
            status: results.status || 'normal',
            attachments: results.attachments || [],
            notes: results.notes,
            followUpRequired: results.followUpRequired || false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };
        
        const docRef = await db.collection('medicalRecords').add(labRecord);
        res.status(201).json({ id: docRef.id, ...labRecord });
    } catch (error) {
        res.status(500).json({ error: 'Error adding lab results' });
    }
});

// Add imaging results
router.post('/imaging-results', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const { patientId, results } = req.body;
        
        if (!patientId || !results) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        
        const imagingRecord = {
            patientId,
            doctorId: req.user.uid,
            type: 'imaging',
            title: results.title || 'Imaging Results',
            description: results.description,
            results: results.data,
            date: admin.firestore.FieldValue.serverTimestamp(),
            status: results.status || 'normal',
            attachments: results.attachments || [],
            notes: results.notes,
            followUpRequired: results.followUpRequired || false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };
        
        const docRef = await db.collection('medicalRecords').add(imagingRecord);
        res.status(201).json({ id: docRef.id, ...imagingRecord });
    } catch (error) {
        res.status(500).json({ error: 'Error adding imaging results' });
    }
});

// Add progress notes
router.post('/progress-notes', authenticateUser, verifyDoctor, async (req, res) => {
    try {
        const { patientId, note } = req.body;
        
        if (!patientId || !note) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        
        const progressNote = {
            patientId,
            doctorId: req.user.uid,
            type: 'progress',
            title: note.title || 'Progress Note',
            description: note.description,
            content: note.content,
            date: admin.firestore.FieldValue.serverTimestamp(),
            status: note.status || 'normal',
            followUpRequired: note.followUpRequired || false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };
        
        const docRef = await db.collection('medicalRecords').add(progressNote);
        res.status(201).json({ id: docRef.id, ...progressNote });
    } catch (error) {
        res.status(500).json({ error: 'Error adding progress note' });
    }
});

// Get patient's lab results
router.get('/lab-results/:patientId', authenticateUser, async (req, res) => {
    try {
        const { patientId } = req.params;
        const { startDate, endDate } = req.query;
        
        let query = db.collection('medicalRecords')
            .where('patientId', '==', patientId)
            .where('type', '==', 'lab')
            .orderBy('date', 'desc');
            
        if (startDate && endDate) {
            query = query.where('date', '>=', new Date(startDate))
                        .where('date', '<=', new Date(endDate));
        }
        
        const snapshot = await query.get();
        const labResults = [];
        snapshot.forEach(doc => {
            labResults.push({ id: doc.id, ...doc.data() });
        });
        
        res.json(labResults);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching lab results' });
    }
});

// Get patient's imaging results
router.get('/imaging-results/:patientId', authenticateUser, async (req, res) => {
    try {
        const { patientId } = req.params;
        const { startDate, endDate } = req.query;
        
        let query = db.collection('medicalRecords')
            .where('patientId', '==', patientId)
            .where('type', '==', 'imaging')
            .orderBy('date', 'desc');
            
        if (startDate && endDate) {
            query = query.where('date', '>=', new Date(startDate))
                        .where('date', '<=', new Date(endDate));
        }
        
        const snapshot = await query.get();
        const imagingResults = [];
        snapshot.forEach(doc => {
            imagingResults.push({ id: doc.id, ...doc.data() });
        });
        
        res.json(imagingResults);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching imaging results' });
    }
});

// Get patient's progress notes
router.get('/progress-notes/:patientId', authenticateUser, async (req, res) => {
    try {
        const { patientId } = req.params;
        const { startDate, endDate } = req.query;
        
        let query = db.collection('medicalRecords')
            .where('patientId', '==', patientId)
            .where('type', '==', 'progress')
            .orderBy('date', 'desc');
            
        if (startDate && endDate) {
            query = query.where('date', '>=', new Date(startDate))
                        .where('date', '<=', new Date(endDate));
        }
        
        const snapshot = await query.get();
        const progressNotes = [];
        snapshot.forEach(doc => {
            progressNotes.push({ id: doc.id, ...doc.data() });
        });
        
        res.json(progressNotes);
    } catch (error) {
        res.status(500).json({ error: 'Error fetching progress notes' });
    }
});

module.exports = router; 