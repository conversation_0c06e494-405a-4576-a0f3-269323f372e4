import { db, auth } from '../config/firebase';
import { collection, query, where, getDocs, addDoc, updateDoc, doc, getDoc, deleteDoc, serverTimestamp, arrayUnion } from 'firebase/firestore';
import axios from 'axios';
import { API_URL } from '../config/api';

/**
 * Service for managing caregiver relationships
 */
export const firebaseCaregiverService = {
  /**
   * Assign a caregiver to a patient (supervisor only)
   * @param {string} patientId - The ID of the patient
   * @param {string} caregiverId - The ID of the caregiver
   * @returns {Promise<Object>} - The result of the assignment
   */
  assignCaregiverToPatient: async (patientId, caregiverId) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();

      const response = await axios.post(
        `${API_URL}/api/users/assign-caregiver`,
        { patientId, caregiverId },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error assigning caregiver to patient:', error);
      throw error;
    }
  },

  /**
   * Get all available caregivers (supervisor only)
   * @returns {Promise<Array>} - List of available caregivers
   */
  getAvailableCaregivers: async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();

      const response = await axios.get(
        `${API_URL}/api/users/available-caregivers`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting available caregivers:', error);
      throw error;
    }
  },

  /**
   * Get caregivers assigned to a patient (supervisor only)
   * @param {string} patientId - The ID of the patient
   * @returns {Promise<Array>} - List of assigned caregivers
   */
  getPatientCaregivers: async (patientId) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();

      const response = await axios.get(
        `${API_URL}/api/users/patient-caregivers/${patientId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting patient caregivers:', error);
      throw error;
    }
  },

  /**
   * Remove a caregiver assignment (supervisor only)
   * @param {string} patientId - The ID of the patient
   * @param {string} caregiverId - The ID of the caregiver
   * @returns {Promise<Object>} - The result of the removal
   */
  removeCaregiverAssignment: async (patientId, caregiverId) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();

      const response = await axios.delete(
        `${API_URL}/api/users/remove-caregiver-assignment`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          },
          data: { patientId, caregiverId }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error removing caregiver assignment:', error);
      throw error;
    }
  },

  /**
   * Assign a caregiver to a patient using caregiver's code (supervisor only)
   * @param {string} patientId - The ID of the patient
   * @param {string} caregiverCode - The unique code of the caregiver
   * @returns {Promise<Object>} - The result of the assignment
   */
  assignCaregiverByCode: async (patientId, caregiverCode) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();

      // Use the server endpoint to assign caregiver by code
      const response = await axios.post(
        `${API_URL}/api/users/assign-caregiver-by-code`,
        { patientId, caregiverCode },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error assigning caregiver by code:', error);
      throw error;
    }
  },

  /**
   * Get patient vitals from Firebase
   * @param {string} patientId - The patient ID
   * @param {string} vitalType - Optional vital type to filter by
   * @returns {Promise<Array>} - Array of vital records
   */
  getPatientVitals: async (patientId, vitalType = null) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Query Firestore for vitals
      const vitalsCollection = collection(db, 'vitals');
      let q;

      if (vitalType) {
        q = query(
          vitalsCollection,
          where('userId', '==', patientId),
          where('vitalType', '==', vitalType)
        );
      } else {
        q = query(
          vitalsCollection,
          where('userId', '==', patientId)
        );
      }

      const querySnapshot = await getDocs(q);
      const vitals = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        vitals.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp, newest first
      return vitals.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      console.error('Error getting patient vitals:', error);
      throw error;
    }
  },

  /**
   * Record a new vital sign for a patient
   * @param {string} patientId - The patient ID
   * @param {string} vitalType - The type of vital (heartRate, bloodPressure, etc.)
   * @param {Object} values - The vital values
   * @param {string} notes - Optional notes about the vital
   * @returns {Promise<Object>} - The newly created vital record
   */
  recordPatientVital: async (patientId, vitalType, values, notes = '') => {
    try {
      if (!patientId || !vitalType || !values) {
        throw new Error('Patient ID, vital type, and values are required');
      }

      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create the vital record
      const vitalRecord = {
        userId: patientId,
        vitalType,
        values,
        notes,
        recordMethod: 'manual',
        recordType: 'caregiver',
        recordedBy: currentUser.uid,
        timestamp: serverTimestamp()
      };

      // Add to Firestore
      const vitalsCollection = collection(db, 'vitals');
      const docRef = await addDoc(vitalsCollection, vitalRecord);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      return {
        id: docRef.id,
        ...newDocData,
        timestamp: new Date().toISOString() // Use current date as fallback
      };
    } catch (error) {
      console.error('Error recording patient vital:', error);
      throw error;
    }
  },

  /**
   * Record a new activity for a patient
   * @param {string} patientId - The patient ID
   * @param {string} activityType - The type of activity (medication, exercise, social, cognitive, etc.)
   * @param {string} description - Description of the activity
   * @param {number} duration - Duration of the activity in minutes
   * @param {boolean} completed - Whether the activity was completed
   * @param {string} notes - Optional notes about the activity
   * @returns {Promise<Object>} - The newly created activity record
   */
  recordPatientActivity: async (patientId, activityType, description, duration = 0, completed = true, notes = '') => {
    try {
      if (!patientId || !activityType || !description) {
        throw new Error('Patient ID, activity type, and description are required');
      }

      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create the activity record
      const activityRecord = {
        userId: patientId,
        type: activityType,
        description,
        duration,
        completed,
        notes,
        recordedBy: currentUser.uid,
        timestamp: serverTimestamp()
      };

      // Add to Firestore
      const activitiesCollection = collection(db, 'activities');
      const docRef = await addDoc(activitiesCollection, activityRecord);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      return {
        id: docRef.id,
        ...newDocData,
        timestamp: new Date().toISOString() // Use current date as fallback
      };
    } catch (error) {
      console.error('Error recording patient activity:', error);
      throw error;
    }
  },

  /**
   * Get patient activities from Firebase
   * @param {string} patientId - The patient ID
   * @param {string} activityType - Optional activity type to filter by
   * @param {number} limit - Optional limit for the number of records to return
   * @returns {Promise<Array>} - Array of activity records
   */
  getPatientActivities: async (patientId, activityType = null, limit = 50) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Query Firestore for activities
      const activitiesCollection = collection(db, 'activities');
      let q;

      if (activityType) {
        q = query(
          activitiesCollection,
          where('userId', '==', patientId),
          where('type', '==', activityType)
        );
      } else {
        q = query(
          activitiesCollection,
          where('userId', '==', patientId)
        );
      }

      const querySnapshot = await getDocs(q);
      const activities = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        activities.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp, newest first
      return activities
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting patient activities:', error);
      throw error;
    }
  }
};
