import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  Alert,
  Modal,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { useNavigation } from "@react-navigation/native";
import Icon from 'react-native-vector-icons/FontAwesome'; // Import FontAwesome icons
import Feather from 'react-native-vector-icons/Feather'; // Import Feather icons
import { useAuth } from '../../contexts/AuthContext';

// Function to generate a unique 8-character alphanumeric code
const generateUserCode = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < 8; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return code;
};

export default function SignUp() {
  const navigation = useNavigation();
  const { isRegistering, register } = useAuth(); // Get register function from auth context
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: '',
  });
  const [loading, setLoading] = useState(false);
  const [showSelectRole, setShowSelectRole] = useState(true);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (field === 'role' && value !== '') {
      setShowSelectRole(false);
    }
  };

  const validateForm = () => {
    if (!formData.firstName.trim()) {
      Alert.alert('Error', 'Please enter your first name');
      return false;
    }
    if (!formData.lastName.trim()) {
      Alert.alert('Error', 'Please enter your last name');
      return false;
    }
    if (!formData.email.trim()) {
      Alert.alert('Error', 'Please enter your email');
      return false;
    }
    if (!formData.password.trim()) {
      Alert.alert('Error', 'Please enter a password');
      return false;
    }
    if (formData.password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return false;
    }
    if (!formData.role) {
      Alert.alert('Error', 'Please select a role');
      return false;
    }
    return true;
  };

  const handleSignUp = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // First, save the user's data so we can show it in the success modal
      const userData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role
      };

      // Use the register function from AuthContext which handles signup without auto-login
      await register(
        formData.email,
        formData.password,
        `${formData.firstName} ${formData.lastName}`,
        formData.role
      );

      // When register completes, show success modal
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Registration error:', error);
      let errorMessage = 'An error occurred during registration';

      if (error.message) {
        errorMessage = error.message;
      } else {
        switch (error.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'This email is already registered';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email format';
            break;
          case 'auth/weak-password':
            errorMessage = 'Password is too weak';
            break;
          default:
            errorMessage = error.message || 'Registration failed';
        }
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Return a loading indicator if we're still in the registration process
  if (isRegistering && !showSuccessModal) {
    return (
      <View style={[styles.wrapper, { backgroundColor: 'rgba(16, 107, 0, 0.7)' }]}>
        <ActivityIndicator size="large" color="#fff" />
        <Text style={{ color: '#fff', marginTop: 20 }}>Creating your account...</Text>
      </View>
    );
  }

  return (
    <ImageBackground
      source={require("../../../assets/Backgrounds/pexels-pixabay-40568.jpg")}
      style={styles.wrapper}
      imageStyle={{ opacity: 0.9 }}
    >
      <View style={styles.overlay}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formCard}>
            <Text style={styles.formRegister}>Create Account</Text>
            <Text style={styles.formSubtitle}>Join our healthcare community</Text>

            <View style={styles.inputBox}>
              <Icon name="user" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="First Name"
                placeholderTextColor="#999"
                value={formData.firstName}
                onChangeText={(text) => handleChange('firstName', text)}
              />
            </View>

            <View style={styles.inputBox}>
              <Icon name="user" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="Last Name"
                placeholderTextColor="#999"
                value={formData.lastName}
                onChangeText={(text) => handleChange('lastName', text)}
              />
            </View>

            <View style={styles.inputBox}>
              <Icon name="envelope" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                placeholderTextColor="#999"
                keyboardType="email-address"
                autoCapitalize="none"
                value={formData.email}
                onChangeText={(text) => handleChange('email', text)}
              />
            </View>

            <View style={styles.inputBox}>
              <Icon name="lock" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor="#999"
                secureTextEntry={!showPassword}
                value={formData.password}
                onChangeText={(text) => handleChange('password', text)}
              />
              <TouchableOpacity
                style={styles.passwordIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Icon
                  name={showPassword ? "eye-slash" : "eye"}
                  size={20}
                  color="rgba(16, 107, 0, 1)"
                />
              </TouchableOpacity>
            </View>

            <View style={styles.inputBox}>
              <Icon name="id-badge" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={formData.role}
                  style={styles.picker}
                  onValueChange={(itemValue) => handleChange('role', itemValue)}
                  onFocus={() => setShowSelectRole(false)}
                  dropdownIconColor="rgba(16, 107, 0, 1)"
                >
                  {showSelectRole && <Picker.Item label="Select Role" value="" color="#999" />}
                  <Picker.Item label="Patient" value="patient" color="#333" />
                  <Picker.Item label="Doctor" value="doctor" color="#333" />
                  <Picker.Item label="Caregiver" value="caregiver" color="#333" />
                  <Picker.Item label="Supervisor" value="supervisor" color="#333" />
                </Picker>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.btn, loading && styles.btnDisabled]}
              onPress={handleSignUp}
              disabled={loading}
              activeOpacity={0.8}
            >
              <Text style={styles.btnText}>
                {loading ? 'Creating Account...' : 'Register'}
              </Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.loginLink}>
              <Text style={styles.loginText}>
                Already have an account?{" "}
              </Text>
              <TouchableOpacity onPress={() => navigation.navigate("Login")}>
                <Text style={styles.loginLinkText}>
                  Login
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.successIconContainer}>
              <Icon name="check-circle" size={60} color="#fff" style={styles.modalIcon} />
            </View>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Registration Successful!</Text>
            </View>
            <View style={styles.modalBody}>
              <Text style={styles.modalText}>Your account has been created successfully.</Text>
              <Text style={styles.modalSubText}>Please sign in with your email and password to access your account.</Text>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => {
                  setShowSuccessModal(false);
                  navigation.navigate("Login", { email: formData.email });
                }}
                activeOpacity={0.8}
              >
                <Text style={styles.modalButtonText}>Go to Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  overlay: {
    flex: 1,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: 30,
    width: '100%',
  },
  formCard: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
    alignItems: 'center',
  },
  formRegister: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: "center",
    color: "#333",
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 30,
  },
  inputBox: {
    position: "relative",
    width: "100%",
    height: 55,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    position: 'absolute',
    left: 15,
    zIndex: 1,
  },
  passwordIcon: {
    position: 'absolute',
    right: 15,
    padding: 5,
    zIndex: 1,
  },
  input: {
    flex: 1,
    height: "100%",
    backgroundColor: "rgba(240, 245, 250, 0.8)",
    borderColor: "#E1E8ED",
    borderWidth: 1,
    borderRadius: 12,
    fontSize: 16,
    color: "#333",
    paddingHorizontal: 20,
    paddingLeft: 45, // Add padding to make space for the icon
    paddingRight: 45, // Add padding to make space for the password icon
  },
  pickerContainer: {
    flex: 1,
    height: "100%",
    backgroundColor: "rgba(240, 245, 250, 0.8)",
    borderColor: "#E1E8ED",
    borderWidth: 1,
    borderRadius: 12,
    paddingLeft: 25,
    justifyContent: 'center',
  },
  picker: {
    width: "100%",
    height: 55,
    color: "#333",
  },
  btn: {
    width: "300",
    height: 45,
    backgroundColor: "#fff",
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  btnDisabled: {
    opacity: 0.7,
  },
  btnText: {
    color: "rgba(16, 107, 0, 1)",
    fontSize: 16,
    fontWeight: "bold",
  },
  divider: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 15,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E1E8ED',
  },
  dividerText: {
    color: '#999',
    paddingHorizontal: 10,
    fontSize: 14,
  },
  loginLink: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    color: "#666",
    textAlign: "center",
    fontSize: 15,
  },
  loginLinkText: {
    color: "rgba(16, 107, 0, 1)",
    fontWeight: "bold",
    fontSize: 15,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    width: '85%',
    alignItems: 'center',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  successIconContainer: {
    width: '100%',
    backgroundColor: 'rgba(16, 107, 0, 1)',
    paddingVertical: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalHeader: {
    marginTop: 20,
    marginBottom: 10,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    alignItems: 'center',
    padding: 20,
    paddingTop: 0,
  },
  modalIcon: {
    marginBottom: 5,
  },
  modalText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  modalSubText: {
    fontSize: 14,
    textAlign: 'center',
    color: '#666',
    paddingHorizontal: 10,
  },
  modalButton: {
    backgroundColor: 'rgba(16, 107, 0, 1)',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 10,
    marginTop: 25,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});