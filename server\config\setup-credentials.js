/**
 * <PERSON>ript to help set up credentials
 */
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const exampleFilePath = path.join(__dirname, 'serviceAccountKey.example.json');
const targetFilePath = path.join(__dirname, 'serviceAccountKey.json');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('NeuroCare Credentials Setup');
console.log('===========================');
console.log('This utility will help you set up your Firebase credentials.');
console.log('');

// Check if credentials file already exists
if (fs.existsSync(targetFilePath)) {
  console.log('Credentials file already exists at:');
  console.log(targetFilePath);
  console.log('');
  rl.question('Do you want to overwrite it? (y/N): ', (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('Setup canceled. Existing credentials file will be kept.');
      rl.close();
      return;
    }
    createCredentialsFile();
  });
} else {
  createCredentialsFile();
}

function createCredentialsFile() {
  if (!fs.existsSync(exampleFilePath)) {
    console.error('Example credentials file not found at:', exampleFilePath);
    console.error('Please make sure the repository is properly cloned.');
    rl.close();
    return;
  }

  // Read the example file
  const exampleContent = JSON.parse(fs.readFileSync(exampleFilePath, 'utf8'));
  
  console.log('Please enter your Firebase credentials:');
  
  getCredentialValues(exampleContent, (credentials) => {
    // Save to the target file
    fs.writeFileSync(targetFilePath, JSON.stringify(credentials, null, 2));
    console.log('');
    console.log('Credentials saved successfully to:');
    console.log(targetFilePath);
    console.log('');
    console.log('Remember to NEVER commit this file to version control!');
    rl.close();
  });
}

function getCredentialValues(template, callback, current = {}, keys = Object.keys(template)) {
  if (keys.length === 0) {
    callback(current);
    return;
  }
  
  const key = keys[0];
  const remainingKeys = keys.slice(1);
  
  if (key === 'type' && template[key] === 'service_account') {
    // For fixed values, don't prompt
    current[key] = template[key];
    getCredentialValues(template, callback, current, remainingKeys);
  } else if (key === 'auth_uri' || key === 'token_uri' || key === 'auth_provider_x509_cert_url' || key === 'universe_domain') {
    // For standard URLs, use defaults
    current[key] = template[key];
    getCredentialValues(template, callback, current, remainingKeys);
  } else {
    rl.question(`Enter ${key}: `, (value) => {
      current[key] = value;
      getCredentialValues(template, callback, current, remainingKeys);
    });
  }
}
