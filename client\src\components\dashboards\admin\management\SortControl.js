import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const SortControl = ({ options, value, onSelect, direction, onDirectionChange }) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Find the selected option label
  const selectedOption = options.find(option => option.value === value) || options[0];

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => setDropdownVisible(true)}
      >
        <Text style={styles.dropdownButtonText}>
          {selectedOption.label}
        </Text>
        <Ionicons name="chevron-down" size={16} color="#555" />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.directionButton}
        onPress={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}
      >
        <Ionicons
          name={direction === 'asc' ? 'arrow-up' : 'arrow-down'}
          size={20}
          color="#fff"
        />
      </TouchableOpacity>

      <Modal
        visible={dropdownVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setDropdownVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setDropdownVisible(false)}
        >
          <View style={[
            styles.dropdownMenu,
            { top: 170, left: 80 } // Adjust position to match the image
          ]}>
            {options.map(option => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.dropdownItem,
                  value === option.value && styles.selectedDropdownItem
                ]}
                onPress={() => {
                  onSelect(option.value);
                  setDropdownVisible(false);
                }}
              >
                <Text style={[
                  styles.dropdownItemText,
                  value === option.value && styles.selectedDropdownItemText
                ]}>
                  {option.label}
                </Text>
                {value === option.value && (
                  <Ionicons name="checkmark" size={18} color="#4CAF50" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E0E6ED',
    minWidth: 120,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    height: 32,
    marginRight: 8,
  },
  dropdownButtonText: {
    color: '#2C3E50',
    fontWeight: '500',
    fontSize: 14,
    marginRight: 8,
  },
  directionButton: {
    width: 32,
    height: 32,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3d9140'
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  dropdownMenu: {
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E0E6ED',
    width: 160,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F7FA'
  },
  selectedDropdownItem: {
    backgroundColor: '#F5F7FA'
  },
  dropdownItemText: {
    color: '#2C3E50',
    fontSize: 14
  },
  selectedDropdownItemText: {
    fontWeight: '500',
    color: '#2C3E50'
  }
});

export default SortControl;