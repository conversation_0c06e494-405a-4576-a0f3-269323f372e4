import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Image,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { usersAPI } from '../../../config/api';
import { showMessage } from 'react-native-flash-message';
import { PATIENT_COLORS, ROLE_COLORS } from '../../../config/theme';
import { useNavigation } from '@react-navigation/native';
import { db } from '../../../config/firebase';
import { doc, getDoc } from 'firebase/firestore';

const DoctorCard = ({ doctor, onRequestAppointment, onStartVideoCall }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [animation] = useState(new Animated.Value(1));

  // Extract first and last name from displayName
  const nameParts = doctor.displayName ? doctor.displayName.split(' ') : ['', ''];
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  // Create initials for avatar
  const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();

  // Handle press animation
  const handlePressIn = () => {
    Animated.spring(animation, {
      toValue: 0.97,
      friction: 5,
      tension: 300,
      useNativeDriver: true
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(animation, {
      toValue: 1,
      friction: 5,
      tension: 300,
      useNativeDriver: true
    }).start();
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  // Format address if available
  const formatAddress = () => {
    if (!doctor.address) return 'Address not available';

    // Handle both string and object address formats
    if (typeof doctor.address === 'string') {
      return doctor.address;
    }

    const { street, city, state, zipCode, country } = doctor.address;
    const parts = [];

    if (street) parts.push(street);
    if (city) parts.push(city);
    if (state) parts.push(state);
    if (zipCode && zipCode !== '...') parts.push(zipCode);
    else if (zipCode === '...') parts.push('Zip: ...');
    if (country) parts.push(country);

    return parts.join(', ') || 'Address not available';
  };

  return (
    <Animated.View
      style={[styles.doctorCard, { transform: [{ scale: animation }] }]}
    >
      <TouchableOpacity
        style={styles.cardContent}
        onPress={toggleDetails}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <View style={styles.cardHeader}>
          <View style={styles.avatarContainer}>
            {doctor.profileImage ? (
              <Image source={{ uri: doctor.profileImage }} style={styles.avatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: ROLE_COLORS.doctor.primary }]}>
                <Text style={styles.avatarText}>{initials}</Text>
              </View>
            )}
          </View>

          <View style={styles.doctorInfo}>
            <Text style={styles.doctorName}>{doctor.displayName ? `Dr. ${doctor.displayName}` : 'Doctor'}</Text>
            <Text style={styles.doctorSpecialty}>
              {doctor.specialty === '...' ? 'Specialty: ...' : doctor.specialty}
            </Text>

            {doctor.email && (
              <View style={styles.contactInfo}>
                <View style={styles.contactItem}>
                  <Ionicons name="mail" size={16} color={ROLE_COLORS.doctor.primary} />
                  <Text style={styles.contactText}>{doctor.email}</Text>
                </View>
              </View>
            )}
          </View>

          <Ionicons
            name={showDetails ? "chevron-up" : "chevron-down"}
            size={24}
            color={ROLE_COLORS.doctor.primary}
            style={styles.expandIcon}
          />
        </View>

        {showDetails && (
          <View style={styles.detailsContainer}>
            <View style={styles.detailSection}>
              <View style={styles.detailTitleContainer}>
                <Ionicons name="location" size={16} color={ROLE_COLORS.doctor.primary} />
                <Text style={styles.detailTitle}>Address</Text>
              </View>
              <Text style={styles.detailText}>{formatAddress()}</Text>
            </View>

            {doctor.phoneNumber && (
              <View style={styles.detailSection}>
                <View style={styles.detailTitleContainer}>
                  <Ionicons name="call" size={16} color={ROLE_COLORS.doctor.primary} />
                  <Text style={styles.detailTitle}>Phone</Text>
                </View>
                <Text style={styles.detailText}>{doctor.phoneNumber}</Text>
              </View>
            )}

            {doctor.hospital && (
              <View style={styles.detailSection}>
                <View style={styles.detailTitleContainer}>
                  <Ionicons name="business" size={16} color={ROLE_COLORS.doctor.primary} />
                  <Text style={styles.detailTitle}>Hospital</Text>
                </View>
                <Text style={styles.detailText}>{doctor.hospital}</Text>
              </View>
            )}

            {doctor.education && (
              <View style={styles.detailSection}>
                <View style={styles.detailTitleContainer}>
                  <Ionicons name="school" size={16} color={ROLE_COLORS.doctor.primary} />
                  <Text style={styles.detailTitle}>Education</Text>
                </View>
                <Text style={styles.detailText}>{doctor.education}</Text>
              </View>
            )}

            {doctor.experience && (
              <View style={styles.detailSection}>
                <View style={styles.detailTitleContainer}>
                  <Ionicons name="briefcase" size={16} color={ROLE_COLORS.doctor.primary} />
                  <Text style={styles.detailTitle}>Experience</Text>
                </View>
                <Text style={styles.detailText}>{doctor.experience} years</Text>
              </View>
            )}

            {doctor.languages && doctor.languages.length > 0 && (
              <View style={styles.detailSection}>
                <View style={styles.detailTitleContainer}>
                  <Ionicons name="globe" size={16} color={ROLE_COLORS.doctor.primary} />
                  <Text style={styles.detailTitle}>Languages</Text>
                </View>
                <Text style={styles.detailText}>{doctor.languages.join(', ')}</Text>
              </View>
            )}

            {!doctor.hospital && !doctor.education &&
             !doctor.experience && (!doctor.languages || doctor.languages.length === 0) && (
              <View style={styles.noDetailsContainer}>
                <Text style={styles.noDetailsText}>No additional details available</Text>
              </View>
            )}
          </View>
        )}
      </TouchableOpacity>

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.primaryActionButton]}
          onPress={() => onRequestAppointment(doctor)}
        >
          <Ionicons name="calendar" size={22} color="#fff" />
          <Text style={[styles.actionText, styles.primaryActionText]}>Request Appointment</Text>
        </TouchableOpacity>

        <View style={styles.secondaryActionsRow}>
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => onStartVideoCall(doctor)}
          >
            <Ionicons name="videocam" size={22} color={PATIENT_COLORS.primary} />
            <Text style={styles.actionText}>Video Call</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
          >
            <Ionicons name="chatbubble" size={22} color={PATIENT_COLORS.primary} />
            <Text style={styles.actionText}>Message</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

const MyDoctorsScreen = () => {
  const navigation = useNavigation();
  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctorsFromFirebase = async (doctorIds) => {
    try {
      const detailedDoctors = [];

      for (const doctorId of doctorIds) {
        // Get the doctor document from Firestore
        const doctorDoc = doc(db, 'users', doctorId);
        const doctorSnapshot = await getDoc(doctorDoc);

        if (doctorSnapshot.exists()) {
          const doctorData = doctorSnapshot.data();
          console.log('Raw doctor data:', doctorData);

          // Format the doctor data with only real data from Firebase
          const doctorInfo = {
            uid: doctorId
          };

          // Name fields
          if (doctorData.displayName) doctorInfo.displayName = doctorData.displayName;
          else if (doctorData.firstName || doctorData.lastName) {
            doctorInfo.displayName = `${doctorData.firstName || ''} ${doctorData.lastName || ''}`.trim();
          }

          if (doctorData.firstName) doctorInfo.firstName = doctorData.firstName;
          if (doctorData.lastName) doctorInfo.lastName = doctorData.lastName;

          // Contact information
          if (doctorData.email) doctorInfo.email = doctorData.email;
          // Check for phone number in different possible field names
          if (doctorData.phoneNumber) doctorInfo.phoneNumber = doctorData.phoneNumber;
          else if (doctorData.phone) doctorInfo.phoneNumber = doctorData.phone;
          else if (doctorData.tel) doctorInfo.phoneNumber = doctorData.tel;
          else if (doctorData.telephone) doctorInfo.phoneNumber = doctorData.telephone;
          else if (doctorData.mobile) doctorInfo.phoneNumber = doctorData.mobile;
          else if (doctorData.mobileNumber) doctorInfo.phoneNumber = doctorData.mobileNumber;

          // Specialty - add '...' if not available
          doctorInfo.specialty = doctorData.specialty || doctorData.speciality || '...';

          // Profile image
          if (doctorData.profileImage) doctorInfo.profileImage = doctorData.profileImage;

          // Address information - handle both string and object formats
          if (typeof doctorData.address === 'string') {
            doctorInfo.address = {
              street: doctorData.address
            };
          } else if (doctorData.address && typeof doctorData.address === 'object') {
            doctorInfo.address = doctorData.address;
          }

          // Add country, city if they exist directly in the doctor data
          if (doctorData.country) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.country = doctorData.country;
          }

          if (doctorData.city) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.city = doctorData.city;
          }

          // Add zipCode - set to '...' if not available
          if (doctorData.zipCode) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.zipCode = doctorData.zipCode;
          } else {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.zipCode = '...';
          }

          // Other professional information
          if (doctorData.hospital) doctorInfo.hospital = doctorData.hospital;
          if (doctorData.education) doctorInfo.education = doctorData.education;
          if (doctorData.experience) doctorInfo.experience = doctorData.experience;
          if (doctorData.languages && doctorData.languages.length > 0) doctorInfo.languages = doctorData.languages;
          if (doctorData.bio) doctorInfo.bio = doctorData.bio;

          detailedDoctors.push(doctorInfo);
        }
      }

      return detailedDoctors;
    } catch (error) {
      console.error('Error fetching detailed doctor information:', error);
      throw error;
    }
  };

  const fetchDoctors = async () => {
    try {
      setLoading(true);

      // First, get the linked doctor IDs from the API
      const linkedDoctors = await usersAPI.getLinkedUsers('doctors');

      if (!linkedDoctors || linkedDoctors.length === 0) {
        console.log('No linked doctors found');
        setDoctors([]);
        return;
      }

      // Extract doctor IDs
      const doctorIds = linkedDoctors.map(doctor => doctor.uid);

      // Log the doctor IDs for debugging
      console.log('Doctor IDs to fetch:', doctorIds);

      // Fetch detailed information for each doctor from Firebase
      const detailedDoctors = await fetchDoctorsFromFirebase(doctorIds);

      console.log(`Fetched ${detailedDoctors.length} doctors with detailed information`);
      console.log('Doctor details:', JSON.stringify(detailedDoctors, null, 2));

      // Log the raw data from Firebase for debugging
      for (const doctorId of doctorIds) {
        try {
          const doctorDoc = doc(db, 'users', doctorId);
          const doctorSnapshot = await getDoc(doctorDoc);
          if (doctorSnapshot.exists()) {
            console.log(`Raw Firebase data for doctor ${doctorId}:`, JSON.stringify(doctorSnapshot.data(), null, 2));
          }
        } catch (error) {
          console.error(`Error fetching raw data for doctor ${doctorId}:`, error);
        }
      }
      setDoctors(detailedDoctors);
    } catch (error) {
      console.error('Error fetching doctors:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load your doctors. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDoctors();
  };

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="medical" size={60} color="#ccc" style={styles.emptyIcon} />
      <Text style={styles.emptyText}>No doctors following you yet</Text>
      <Text style={styles.emptySubtext}>
        When doctors add you as their patient, they will appear here
      </Text>
    </View>
  );

  // Handle request appointment button press
  const handleRequestAppointment = (doctor) => {
    // Navigate to Appointments screen with parameter to open request modal
    navigation.navigate('Appointments', {
      openRequestModal: true,
      selectedDoctorId: doctor.uid
    });
  };

  // Handle start video call button press
  const handleStartVideoCall = (doctor) => {
    // Generate a unique room name
    const roomName = `neurocare_${doctor.uid}_${Date.now()}`;

    // Navigate to PatientVideoCall screen
    navigation.navigate('PatientVideoCall', {
      roomName: roomName,
      doctorInfo: doctor
    });
  };

  return (
    <View style={styles.container}>


      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={PATIENT_COLORS.primary} />
          <Text style={styles.loadingText}>Loading your doctors...</Text>
        </View>
      ) : (
        <FlatList
          data={doctors}
          keyExtractor={(item) => item.uid}
          renderItem={({ item }) => (
            <DoctorCard
              doctor={item}
              onRequestAppointment={handleRequestAppointment}
              onStartVideoCall={handleStartVideoCall}
            />
          )}
          contentContainerStyle={doctors.length === 0 ? styles.emptyListContent : styles.listContent}
          ListEmptyComponent={renderEmptyList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[PATIENT_COLORS.primary]}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingTop: 10,
  },

  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  doctorCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  cardContent: {
    padding: 0,
  },
  cardHeader: {
    flexDirection: 'row',
    padding: 18,
    alignItems: 'center',
    position: 'relative',
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
    backgroundColor: '#fcfcfc',
  },
  avatarContainer: {
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  avatar: {
    width: 75,
    height: 75,
    borderRadius: 38,
    borderWidth: 2,
    borderColor: '#fff',
  },
  avatarPlaceholder: {
    width: 75,
    height: 75,
    borderRadius: 38,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  avatarText: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#fff',
  },
  doctorInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  doctorName: {
    fontSize: 19,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
    letterSpacing: 0.3,
  },
  doctorSpecialty: {
    fontSize: 15,
    color: ROLE_COLORS.doctor.primary,
    marginBottom: 8,
    fontStyle: 'italic',
    opacity: 0.9,
  },
  expandIcon: {
    position: 'absolute',
    right: 16,
    top: 16,
  },
  contactInfo: {
    marginTop: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 6,
    paddingHorizontal: 10,
    alignSelf: 'flex-start',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  contactText: {
    fontSize: 14,
    color: '#555',
    marginLeft: 8,
    fontWeight: '500',
  },
  detailsContainer: {
    padding: 18,
    paddingTop: 10,
    backgroundColor: '#f9f9f9',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  detailSection: {
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  detailTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 6,
  },
  detailTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: ROLE_COLORS.doctor.primary,
    marginLeft: 8,
    letterSpacing: 0.3,
  },
  detailText: {
    fontSize: 15,
    color: '#444',
    lineHeight: 22,
    paddingHorizontal: 4,
    paddingTop: 2,
  },
  noDetailsContainer: {
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    marginTop: 5,
  },
  noDetailsText: {
    fontSize: 15,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'column',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    padding: 14,
    backgroundColor: '#fff',
  },
  secondaryActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 14,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  secondaryActionButton: {
    flex: 1,
    marginHorizontal: 4,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: PATIENT_COLORS.primary,
  },
  actionText: {
    marginLeft: 8,
    color: PATIENT_COLORS.primary,
    fontWeight: '600',
    fontSize: 15,
  },
  primaryActionButton: {
    backgroundColor: PATIENT_COLORS.primary,
    borderWidth: 0,
    elevation: 2,
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  primaryActionText: {
    color: '#fff',
    fontWeight: '700',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});

export default MyDoctorsScreen;
