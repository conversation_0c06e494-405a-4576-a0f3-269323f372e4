import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ScrollView, RefreshControl } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { getThemeForRole, ROLE_COLORS } from '../../../config/theme';

// Get caregiver colors for use in styles
const CAREGIVER_COLORS = ROLE_COLORS.caregiver;
import { useNavigation } from '@react-navigation/native';
import { Card, Avatar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../contexts/AuthContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import DashboardChart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '../../../config/firebase';
import { firebaseAppointmentsService } from '../../../services/firebaseAppointmentsService';

const menuItems = [
  { label: 'Dashboard', icon: 'home', screen: 'Dashboard' },
  { label: 'Patients', icon: 'people', screen: 'CaregiverPatients' },
  { label: 'Patient Navigation', icon: 'navigate', screen: 'CaregiverPatientNavigation' },
  { label: 'Appointments', icon: 'calendar', screen: 'CaregiverAppointments' },
  { label: 'Messages', icon: 'chatbubbles', screen: 'Chatroom' },
  { label: 'Medication', icon: 'medkit', screen: 'Medications' },
  { label: 'Reports', icon: 'document', screen: 'HealthRecords' },
  { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
  { label: 'Profile', icon: 'person', screen: 'Profile' },
  { label: 'Settings', icon: 'settings', screen: 'Settings' },
];

const CaregiverDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const theme = getThemeForRole('caregiver');
  const caregiverColors = ROLE_COLORS.caregiver;
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSection, setSelectedSection] = useState('overview');

  // State for patient data
  const [patientData, setPatientData] = useState({
    total: 0,
    new: 0,
    critical: 0,
    stable: 0,
    chartData: [0, 0, 0, 0, 0, 0, 0]
  });

  // State for appointment data
  const [appointmentData, setAppointmentData] = useState([]);

  // Function to fetch dashboard data from Firebase
  const fetchDashboardData = async () => {
    try {
      // Fetch patients data
      const patientsCollection = collection(db, 'users');
      const patientsQuery = query(
        patientsCollection,
        where('role', '==', 'patient')
      );
      const patientsSnapshot = await getDocs(patientsQuery);

      // Count total patients
      const totalPatients = patientsSnapshot.docs.length;

      // Count new patients (registered in the last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const newPatients = patientsSnapshot.docs.filter(doc => {
        const userData = doc.data();
        const createdAt = userData.createdAt?.toDate?.() || new Date(userData.createdAt);
        return createdAt > sevenDaysAgo;
      }).length;

      // Count critical and stable patients
      // This is a placeholder - in a real app, you would have a status field
      let criticalCount = 0;
      let stableCount = 0;

      patientsSnapshot.docs.forEach(doc => {
        const userData = doc.data();
        if (userData.status === 'critical') {
          criticalCount++;
        } else {
          stableCount++;
        }
      });

      // Generate patient chart data (patients per day for the last 7 days)
      // This is a placeholder - in a real app, you would query by date
      const patientChartData = Array(7).fill(0).map(() =>
        Math.floor(Math.random() * 10) + 5
      );

      // Update patient data state
      setPatientData({
        total: totalPatients,
        new: newPatients,
        critical: criticalCount,
        stable: stableCount || totalPatients, // If no status field, assume all are stable
        chartData: patientChartData
      });

      // Fetch appointments data
      try {
        const appointments = await firebaseAppointmentsService.getCaregiverPatientAppointments();

        // Format appointments for display
        const formattedAppointments = appointments.map(appointment => {
          const patientName = appointment.patient?.firstName
            ? `${appointment.patient.firstName} ${appointment.patient.lastName || ''}`
            : appointment.patientName || 'Patient';

          return {
            id: appointment.id,
            name: patientName,
            time: appointment.time || 'No time specified',
            status: appointment.status?.toLowerCase() || 'pending'
          };
        });

        setAppointmentData(formattedAppointments);
      } catch (error) {
        console.log('Error fetching appointments:', error);
        setAppointmentData([]);
      }

    } catch (error) {
      console.error('Error fetching caregiver dashboard data:', error);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Function to navigate to Patient list
  const handleViewAllPatients = () => {
    navigation.navigate('CaregiverPatients');
  };

  // Function to navigate to Add Patient screen
  const handleAddPatient = () => {
    navigation.navigate('CaregiverPatients');
  };

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  return (
    <DashboardLayout
      title="Caregiver Dashboard"
      roleName="Caregiver"
      menuItems={menuItems}
      userRole="caregiver"
      notifications={notifications}  // Pass notifications here
    >
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[CAREGIVER_COLORS.primary]} />
        }
      >
        <View style={styles.headerSection}>
          <LinearGradient
            colors={[CAREGIVER_COLORS.primaryLight, '#f5f5f5']}
            start={{ x: 0, y: 0 }}
            end={{ x: 2, y: 1 }}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <View>
                <Text style={styles.welcomeText}>Welcome, {user?.firstName || 'Caregiver'}</Text>
                <Text style={styles.dateText}>{new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        <View style={styles.contentSection}>

        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Patient Overview</Text>
          <TouchableOpacity onPress={handleViewAllPatients}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.cardsContainer}>
          <View style={styles.cardRow}>
            <DashboardCard
              title="Total Patients"
              value={patientData.total.toString()}
              icon="people"
              color={CAREGIVER_COLORS.primary}
              gradientStart="#E3F2FD"
              gradientEnd="#BBDEFB"
              width="48%"
            />
            <DashboardCard
              title="New Patients"
              value={patientData.new.toString()}
              icon="person-add"
              color={CAREGIVER_COLORS.primary}
              gradientStart="#E8F5E9"
              gradientEnd="#C8E6C9"
              width="48%"
            />
          </View>
          <View style={styles.cardRow}>
            <DashboardCard
              title="Critical Care"
              value={patientData.critical.toString()}
              icon="alert-circle"
              color={CAREGIVER_COLORS.primary}
              gradientStart="#FFEBEE"
              gradientEnd="#FFCDD2"
              width="48%"
            />
            <DashboardCard
              title="Stable Patients"
              value={patientData.stable.toString()}
              icon="checkmark-circle"
              color={CAREGIVER_COLORS.primary}
              gradientStart="#E1F5FE"
              gradientEnd="#B3E5FC"
              width="48%"
            />
          </View>
        </View>

        <View style={styles.addPatientContainer}>
          <TouchableOpacity
            style={styles.addPatientButton}
            onPress={handleAddPatient}
          >
            <Ionicons name="person-add" size={20} color="#fff" />
            <Text style={styles.addPatientText}>Add New Patient</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.qrCodeContainer}>
          <TouchableOpacity
            style={styles.qrCodeButton}
            onPress={() => navigation.navigate('UserQRCode')}
          >
            <Ionicons name="qr-code" size={20} color="#fff" />
            <Text style={styles.qrCodeText}>Show My QR Code</Text>
          </TouchableOpacity>
        </View>

        {patientData.chartData.some(value => value > 0) && (
          <Card style={styles.chartCard}>
            <Card.Title title="Patient Statistics" subtitle="Last 7 Days" />
            <Card.Content>
              <DashboardChart
                data={patientData.chartData}
                labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
                color={CAREGIVER_COLORS.primary}
                type="line"
                title="Patient Activity"
              />
            </Card.Content>
          </Card>
        )}

        <View style={styles.upcomingSection}>
          <Text style={styles.sectionTitle}>Upcoming Appointments</Text>
          <UpcomingList
            data={appointmentData}
            type="appointments"
            emptyText="No upcoming appointments"
            onViewAll={() => navigation.navigate('CaregiverAppointments')}
          />
        </View>

        <View style={styles.spacer} />
        </View>
      </ScrollView>
    </DashboardLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  headerSection: {
    width: '100%',
    marginBottom: 20,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 25,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contentSection: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  dateText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: CAREGIVER_COLORS.primary,
    fontWeight: '500',
  },
  cardsContainer: {
    marginBottom: 24,
    paddingHorizontal: 0,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  addPatientContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  addPatientButton: {
    backgroundColor: CAREGIVER_COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  addPatientText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  qrCodeContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  qrCodeButton: {
    backgroundColor: CAREGIVER_COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  qrCodeText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  chartCard: {
    marginBottom: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    overflow: 'hidden',
  },
  upcomingSection: {
    marginBottom: 16,
  },
  spacer: {
    height: 30,
  },
});

export default CaregiverDashboard;