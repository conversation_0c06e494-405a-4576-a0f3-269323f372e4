import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  Modal,
  KeyboardAvoidingView,
  Platform,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ROLE_COLORS } from '../config/theme';
import * as Location from 'expo-location';
import { db, auth } from '../config/firebase';
import { collection, addDoc, serverTimestamp, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Google Maps style - Standard style with slight modifications for better readability
const googleMapsStyle = [
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.business",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "labels.text",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "on"
      }
    ]
  },
  {
    "featureType": "transit",
    "stylers": [
      {
        "visibility": "on"
      }
    ]
  }
];

const SupervisorGuidanceScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { patient } = route.params || {};
  const supervisorColors = ROLE_COLORS.supervisor;

  // State variables
  const [loading, setLoading] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [destination, setDestination] = useState('');
  const [destinationCoords, setDestinationCoords] = useState(null);
  const [instructions, setInstructions] = useState('');
  const [savedLocations, setSavedLocations] = useState([]);
  const [showSavedLocations, setShowSavedLocations] = useState(false);
  const [routeType, setRouteType] = useState('walking'); // walking, driving, transit
  const [mapRegion, setMapRegion] = useState(null);
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeDistance, setRouteDistance] = useState(null);
  const [routeDuration, setRouteDuration] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Refs
  const mapRef = useRef(null);

  useEffect(() => {
    // Get current location when component mounts
    getCurrentLocation();
    // Load saved locations
    loadSavedLocations();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required for this feature.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      if (location) {
        const currentCoords = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };

        setCurrentLocation(currentCoords);

        // Set initial map region
        setMapRegion({
          ...currentCoords,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Error', 'Failed to get current location. Please try again.');
    }
  };

  const loadSavedLocations = async () => {
    try {
      // In a real app, you would fetch saved locations from Firebase
      // For now, we'll use some sample locations
      setSavedLocations([
        { id: '1', name: 'Home', address: '123 Main St', latitude: 40.7128, longitude: -74.0060 },
        { id: '2', name: 'Hospital', address: '456 Medical Ave', latitude: 40.7282, longitude: -73.9942 },
        { id: '3', name: 'Pharmacy', address: '789 Health Blvd', latitude: 40.7112, longitude: -74.0055 },
      ]);
    } catch (error) {
      console.error('Error loading saved locations:', error);
    }
  };

  const handleSelectLocation = (location) => {
    setDestination(location.address);
    setDestinationCoords({
      latitude: location.latitude,
      longitude: location.longitude,
    });
    setShowSavedLocations(false);

    // Update map region to include the selected location
    if (currentLocation) {
      fitMapToCoordinates([
        currentLocation,
        { latitude: location.latitude, longitude: location.longitude }
      ]);
    } else {
      setMapRegion({
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }

    // Calculate route
    calculateRoute(
      currentLocation,
      { latitude: location.latitude, longitude: location.longitude },
      routeType
    );
  };

  const searchPlaces = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    try {
      setLoading(true);

      // Use Expo Location's geocoding API to search for places
      const results = await Location.geocodeAsync(query);

      if (results && results.length > 0) {
        // For each result, get the address
        const detailedResults = await Promise.all(
          results.map(async (result) => {
            const address = await Location.reverseGeocodeAsync({
              latitude: result.latitude,
              longitude: result.longitude,
            });

            const addressObj = address[0] || {};
            const formattedAddress = [
              addressObj.name,
              addressObj.street,
              addressObj.city,
              addressObj.region,
              addressObj.country,
            ]
              .filter(Boolean)
              .join(', ');

            return {
              id: `${result.latitude}-${result.longitude}`,
              latitude: result.latitude,
              longitude: result.longitude,
              address: formattedAddress,
              name: addressObj.name || formattedAddress.split(',')[0],
            };
          })
        );

        setSearchResults(detailedResults);
        setShowSearchResults(true);
      } else {
        setSearchResults([]);
        setShowSearchResults(false);
        Alert.alert('No Results', 'No locations found for your search.');
      }
    } catch (error) {
      console.error('Error searching places:', error);
      Alert.alert('Error', 'Failed to search for locations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const calculateRoute = async (start, end, mode = 'walking') => {
    if (!start || !end) {
      console.log('Missing start or end coordinates');
      return;
    }

    try {
      setLoading(true);

      // In a production app, you would use a directions API like Google Directions API
      // For this demo, we'll create a simple straight line between points

      // Simulate route calculation
      const simulatedRoute = [
        { latitude: start.latitude, longitude: start.longitude },
        { latitude: (start.latitude + end.latitude) / 2, longitude: (start.longitude + end.longitude) / 2 },
        { latitude: end.latitude, longitude: end.longitude },
      ];

      setRouteCoordinates(simulatedRoute);

      // Calculate approximate distance (in km) using Haversine formula
      const distance = calculateDistance(start.latitude, start.longitude, end.latitude, end.longitude);
      setRouteDistance(distance);

      // Estimate duration based on mode and distance
      let duration;
      switch (mode) {
        case 'walking':
          // Assume walking speed of 5 km/h
          duration = (distance / 5) * 60; // minutes
          break;
        case 'driving':
          // Assume driving speed of 50 km/h
          duration = (distance / 50) * 60; // minutes
          break;
        case 'transit':
          // Assume transit speed of 30 km/h
          duration = (distance / 30) * 60; // minutes
          break;
        default:
          duration = (distance / 5) * 60; // minutes
      }

      setRouteDuration(Math.round(duration));

      // Fit map to show the entire route
      fitMapToCoordinates(simulatedRoute);
    } catch (error) {
      console.error('Error calculating route:', error);
      Alert.alert('Error', 'Failed to calculate route. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    return distance;
  };

  const deg2rad = (deg) => {
    return deg * (Math.PI / 180);
  };

  const fitMapToCoordinates = (coordinates) => {
    if (!mapRef.current || !coordinates || coordinates.length === 0) {
      return;
    }

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
      animated: true,
    });
  };

  const handleMapPress = (event) => {
    const { coordinate } = event.nativeEvent;

    // Set the pressed location as destination
    setDestinationCoords(coordinate);

    // Get address for the selected location
    Location.reverseGeocodeAsync(coordinate)
      .then((addresses) => {
        if (addresses && addresses.length > 0) {
          const address = addresses[0];
          const formattedAddress = [
            address.name,
            address.street,
            address.city,
            address.region,
            address.postalCode,
            address.country,
          ]
            .filter(Boolean)
            .join(', ');

          setDestination(formattedAddress);

          // Calculate route
          if (currentLocation) {
            calculateRoute(currentLocation, coordinate, routeType);
          }
        }
      })
      .catch((error) => {
        console.error('Error reverse geocoding:', error);
      });
  };

  const handleSendGuidance = async () => {
    if (!destination.trim()) {
      Alert.alert('Error', 'Please enter a destination');
      return;
    }

    if (!patient) {
      Alert.alert('Error', 'No patient selected');
      return;
    }

    if (!destinationCoords) {
      Alert.alert('Error', 'Please select a valid destination on the map');
      return;
    }

    setLoading(true);

    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create guidance object
      const guidanceData = {
        supervisorId: currentUser.uid,
        supervisorName: `${currentUser.displayName || 'Supervisor'}`,
        patientId: patient.uid,
        patientName: `${patient.firstName} ${patient.lastName}`,
        destination: destination,
        destinationCoords: destinationCoords,
        instructions: instructions.trim(),
        routeType: routeType,
        status: 'sent',
        createdAt: serverTimestamp(),
        startLocation: currentLocation ? {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
        } : null,
        // Add route information
        routeCoordinates: routeCoordinates,
        routeDistance: routeDistance,
        routeDuration: routeDuration,
        // Add metadata
        readByPatient: false,
        completedByPatient: false
      };

      // Save to Firebase
      const guidanceCollection = collection(db, 'patientGuidance');
      await addDoc(guidanceCollection, guidanceData);

      Alert.alert(
        'Success',
        `Guidance sent to ${patient.firstName}`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error sending guidance:', error);
      Alert.alert('Error', 'Failed to send guidance. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Effect to recalculate route when route type changes
  useEffect(() => {
    if (currentLocation && destinationCoords) {
      calculateRoute(currentLocation, destinationCoords, routeType);
    }
  }, [routeType]);

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            Send Guidance to {patient?.firstName || 'Patient'}
          </Text>
        </View>

        {/* Map Section */}
        <View style={styles.mapSection}>
          {mapRegion ? (
            <MapView
              ref={mapRef}
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              initialRegion={mapRegion}
              customMapStyle={googleMapsStyle}
              onPress={handleMapPress}
              showsUserLocation={false}
              showsMyLocationButton={false}
              showsCompass={true}
              showsScale={true}
              showsTraffic={false}
              showsBuildings={true}
              showsIndoors={true}
              zoomControlEnabled={true}
              rotateEnabled={true}
              scrollEnabled={true}
              pitchEnabled={true}
            >
              {currentLocation && (
                <Marker
                  coordinate={currentLocation}
                  title="Your Location"
                  description="Your current location"
                >
                  <View style={styles.currentLocationMarker}>
                    <View style={styles.currentLocationDot} />
                    <View style={styles.currentLocationHalo} />
                  </View>
                </Marker>
              )}

              {destinationCoords && (
                <Marker
                  coordinate={destinationCoords}
                  title={destination || "Destination"}
                  description="Selected destination"
                >
                  <View style={styles.destinationMarkerContainer}>
                    <View style={[styles.destinationMarker, { backgroundColor: supervisorColors.primary }]}>
                      <Ionicons name="location" size={18} color="#fff" />
                    </View>
                    <View style={[styles.destinationMarkerTail, { borderTopColor: supervisorColors.primary }]} />
                  </View>
                </Marker>
              )}

              {routeCoordinates.length > 0 && (
                <>
                  {/* Background line (shadow effect) */}
                  <Polyline
                    coordinates={routeCoordinates}
                    strokeWidth={8}
                    strokeColor="rgba(0, 0, 0, 0.2)"
                    lineCap="round"
                    lineJoin="round"
                  />
                  {/* Main route line */}
                  <Polyline
                    coordinates={routeCoordinates}
                    strokeWidth={4}
                    strokeColor="#4285F4"
                    lineCap="round"
                    lineJoin="round"
                  />
                </>
              )}
            </MapView>
          ) : (
            <View style={styles.loadingMapContainer}>
              <ActivityIndicator size="large" color={supervisorColors.primary} />
              <Text style={styles.loadingText}>Loading map...</Text>
            </View>
          )}

          {/* Map Controls */}
          <View style={styles.mapControls}>
            <TouchableOpacity
              style={styles.mapControlButton}
              onPress={() => {
                if (mapRef.current && mapRegion) {
                  mapRef.current.animateToRegion({
                    ...mapRegion,
                    latitudeDelta: mapRegion.latitudeDelta / 2,
                    longitudeDelta: mapRegion.longitudeDelta / 2,
                  }, 300);
                }
              }}
            >
              <Ionicons name="add" size={24} color="#333" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.mapControlButton}
              onPress={() => {
                if (mapRef.current && mapRegion) {
                  mapRef.current.animateToRegion({
                    ...mapRegion,
                    latitudeDelta: mapRegion.latitudeDelta * 2,
                    longitudeDelta: mapRegion.longitudeDelta * 2,
                  }, 300);
                }
              }}
            >
              <Ionicons name="remove" size={24} color="#333" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.mapControlButton}
              onPress={getCurrentLocation}
            >
              <Ionicons name="locate" size={24} color="#333" />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={styles.content}>
          {/* Search and Destination Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Destination</Text>
            <View style={styles.destinationInputContainer}>
              <TextInput
                style={styles.destinationInput}
                placeholder="Search for a location"
                value={searchQuery}
                onChangeText={(text) => {
                  setSearchQuery(text);
                  if (text.length > 2) {
                    searchPlaces(text);
                  } else {
                    setShowSearchResults(false);
                  }
                }}
              />
              <TouchableOpacity
                style={styles.searchButton}
                onPress={() => searchPlaces(searchQuery)}
              >
                <Ionicons name="search" size={24} color={supervisorColors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.savedLocationsButton}
                onPress={() => setShowSavedLocations(true)}
              >
                <Ionicons name="bookmark" size={24} color={supervisorColors.primary} />
              </TouchableOpacity>
            </View>

            {/* Search Results */}
            {showSearchResults && searchResults.length > 0 && (
              <View style={styles.searchResultsContainer}>
                <ScrollView style={styles.searchResults}>
                  {searchResults.map((result) => (
                    <TouchableOpacity
                      key={result.id}
                      style={styles.searchResultItem}
                      onPress={() => {
                        handleSelectLocation(result);
                        setSearchQuery('');
                        setShowSearchResults(false);
                      }}
                    >
                      <Ionicons name="location-outline" size={20} color={supervisorColors.primary} />
                      <View style={styles.searchResultInfo}>
                        <Text style={styles.searchResultName}>{result.name}</Text>
                        <Text style={styles.searchResultAddress} numberOfLines={1}>{result.address}</Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            )}

            {/* Selected Destination */}
            {destination && (
              <View style={styles.selectedDestinationContainer}>
                <Ionicons name="location" size={20} color={supervisorColors.primary} />
                <View style={styles.selectedDestinationInfo}>
                  <Text style={styles.selectedDestinationText}>{destination}</Text>
                </View>
              </View>
            )}
          </View>

          {/* Route Information */}
          {routeDistance && routeDuration && (
            <View style={styles.routeInfoContainer}>
              <View style={styles.routeInfoItem}>
                <Ionicons name="speedometer-outline" size={20} color={supervisorColors.primary} />
                <Text style={styles.routeInfoText}>{routeDistance.toFixed(2)} km</Text>
              </View>
              <View style={styles.routeInfoItem}>
                <Ionicons name="time-outline" size={20} color={supervisorColors.primary} />
                <Text style={styles.routeInfoText}>
                  {routeDuration > 60
                    ? `${Math.floor(routeDuration / 60)}h ${routeDuration % 60}min`
                    : `${routeDuration} min`}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Route Type</Text>
            <View style={styles.routeTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.routeTypeButton,
                  routeType === 'walking' && styles.routeTypeButtonActive
                ]}
                onPress={() => setRouteType('walking')}
              >
                <Ionicons
                  name="walk"
                  size={24}
                  color={routeType === 'walking' ? '#fff' : supervisorColors.primary}
                />
                <Text
                  style={[
                    styles.routeTypeText,
                    routeType === 'walking' && styles.routeTypeTextActive
                  ]}
                >
                  Walking
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.routeTypeButton,
                  routeType === 'driving' && styles.routeTypeButtonActive
                ]}
                onPress={() => setRouteType('driving')}
              >
                <Ionicons
                  name="car"
                  size={24}
                  color={routeType === 'driving' ? '#fff' : supervisorColors.primary}
                />
                <Text
                  style={[
                    styles.routeTypeText,
                    routeType === 'driving' && styles.routeTypeTextActive
                  ]}
                >
                  Driving
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.routeTypeButton,
                  routeType === 'transit' && styles.routeTypeButtonActive
                ]}
                onPress={() => setRouteType('transit')}
              >
                <Ionicons
                  name="bus"
                  size={24}
                  color={routeType === 'transit' ? '#fff' : supervisorColors.primary}
                />
                <Text
                  style={[
                    styles.routeTypeText,
                    routeType === 'transit' && styles.routeTypeTextActive
                  ]}
                >
                  Transit
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Additional Instructions</Text>
            <TextInput
              style={styles.instructionsInput}
              placeholder="Enter any additional instructions or notes for the patient"
              value={instructions}
              onChangeText={setInstructions}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <TouchableOpacity
            style={styles.sendButton}
            onPress={handleSendGuidance}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Ionicons name="send" size={20} color="#fff" />
                <Text style={styles.sendButtonText}>Send Guidance</Text>
              </>
            )}
          </TouchableOpacity>
        </ScrollView>

        {/* Saved Locations Modal */}
        <Modal
          visible={showSavedLocations}
          transparent
          animationType="slide"
          onRequestClose={() => setShowSavedLocations(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Saved Locations</Text>
                <TouchableOpacity
                  onPress={() => setShowSavedLocations(false)}
                >
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.locationsList}>
                {savedLocations.map((location) => (
                  <TouchableOpacity
                    key={location.id}
                    style={styles.locationItem}
                    onPress={() => handleSelectLocation(location)}
                  >
                    <Ionicons name="location" size={24} color={supervisorColors.primary} />
                    <View style={styles.locationInfo}>
                      <Text style={styles.locationName}>{location.name}</Text>
                      <Text style={styles.locationAddress}>{location.address}</Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    zIndex: 10,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  // Map Section
  mapSection: {
    height: height * 0.35,
    width: '100%',
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  loadingMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loadingText: {
    marginTop: 8,
    color: '#666',
    fontSize: 16,
  },
  mapControls: {
    position: 'absolute',
    right: 16,
    top: 16,
    backgroundColor: 'transparent',
    zIndex: 5,
  },
  mapControlButton: {
    backgroundColor: '#fff',
    borderRadius: 30,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  // Content Section
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  // Destination Input
  destinationInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  destinationInput: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchButton: {
    padding: 12,
    marginLeft: 4,
  },
  savedLocationsButton: {
    padding: 12,
    marginLeft: 4,
  },
  // Search Results
  searchResultsContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    maxHeight: 200,
    zIndex: 10,
  },
  searchResults: {
    maxHeight: 200,
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchResultInfo: {
    marginLeft: 12,
    flex: 1,
  },
  searchResultName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  searchResultAddress: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  // Selected Destination
  selectedDestinationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#d0e8ff',
  },
  selectedDestinationInfo: {
    marginLeft: 12,
    flex: 1,
  },
  selectedDestinationText: {
    fontSize: 14,
    color: '#333',
  },
  // Route Info
  routeInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  routeInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeInfoText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  // Route Type
  routeTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  routeTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: ROLE_COLORS.supervisor.primary,
  },
  routeTypeButtonActive: {
    backgroundColor: ROLE_COLORS.supervisor.primary,
  },
  routeTypeText: {
    marginLeft: 8,
    color: ROLE_COLORS.supervisor.primary,
    fontWeight: '600',
  },
  routeTypeTextActive: {
    color: '#fff',
  },
  // Instructions
  instructionsInput: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minHeight: 120,
  },
  // Send Button
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ROLE_COLORS.supervisor.primary,
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
    marginBottom: 24,
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  // Modal
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  locationsList: {
    marginBottom: 16,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  locationInfo: {
    marginLeft: 12,
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  locationAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  // Google Maps style markers
  currentLocationMarker: {
    width: 22,
    height: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentLocationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4285F4',
    borderWidth: 1,
    borderColor: '#fff',
  },
  currentLocationHalo: {
    position: 'absolute',
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: 'rgba(66, 133, 244, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(66, 133, 244, 0.3)',
  },
  destinationMarkerContainer: {
    alignItems: 'center',
  },
  destinationMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#DB4437',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 5,
  },
  destinationMarkerTail: {
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#DB4437',
    marginTop: -2,
  },
});

export default SupervisorGuidanceScreen;
