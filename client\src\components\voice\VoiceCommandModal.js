import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import {
  requestMicrophonePermission,
  startVoiceRecording,
  stopVoiceRecording
} from '../../utils/voiceNavigation';

const VoiceCommandModal = ({ visible, onClose }) => {
  const navigation = useNavigation();
  const [recording, setRecording] = useState(null);
  const [isListening, setIsListening] = useState(false);
  const [processingVoice, setProcessingVoice] = useState(false);
  const [recognizedCommand, setRecognizedCommand] = useState('');
  const [destination, setDestination] = useState('');
  const [error, setError] = useState('');
  
  // Animation for the recording indicator
  const [pulseAnim] = useState(new Animated.Value(1));
  
  // Start the pulse animation when listening
  useEffect(() => {
    if (isListening) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.3,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isListening, pulseAnim]);
  
  // Reset state when modal is opened
  useEffect(() => {
    if (visible) {
      resetState();
    }
  }, [visible]);
  
  // Reset all state values
  const resetState = () => {
    setRecording(null);
    setIsListening(false);
    setProcessingVoice(false);
    setRecognizedCommand('');
    setDestination('');
    setError('');
  };
  
  // Start listening for voice commands
  const startListening = async () => {
    try {
      setError('');
      
      // Request microphone permission
      const hasPermission = await requestMicrophonePermission();
      if (!hasPermission) {
        setError('Microphone permission is required for voice commands.');
        return;
      }
      
      // Start recording
      const newRecording = await startVoiceRecording();
      if (!newRecording) {
        setError('Failed to start recording. Please try again.');
        return;
      }
      
      setRecording(newRecording);
      setIsListening(true);
    } catch (error) {
      console.error('Error starting voice recording:', error);
      setError('An error occurred while starting voice recording.');
    }
  };
  
  // Stop listening and process the command
  const stopListening = async () => {
    if (!recording) {
      setIsListening(false);
      return;
    }
    
    setIsListening(false);
    setProcessingVoice(true);
    
    await stopVoiceRecording(recording, (result) => {
      setProcessingVoice(false);
      setRecording(null);
      
      if (result.success) {
        setRecognizedCommand(result.command);
        setDestination(result.destination);
      } else {
        setError(result.error || 'Failed to recognize command');
      }
    });
  };
  
  // Navigate to the destination and close the modal
  const handleNavigate = () => {
    if (destination) {
      navigation.navigate(destination);
      onClose();
    }
  };
  
  // Try again - reset state and start over
  const handleTryAgain = () => {
    resetState();
  };
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Voice Navigation</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            {error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={40} color="#EA4335" />
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity
                  style={styles.tryAgainButton}
                  onPress={handleTryAgain}
                >
                  <Text style={styles.tryAgainButtonText}>Try Again</Text>
                </TouchableOpacity>
              </View>
            ) : recognizedCommand ? (
              <View style={styles.resultContainer}>
                <Ionicons name="checkmark-circle" size={40} color="#4CAF50" />
                <Text style={styles.recognizedCommandTitle}>I heard:</Text>
                <Text style={styles.recognizedCommand}>"{recognizedCommand}"</Text>
                
                <Text style={styles.destinationTitle}>Navigating to:</Text>
                <Text style={styles.destination}>{destination}</Text>
                
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.secondaryButton]}
                    onPress={handleTryAgain}
                  >
                    <Text style={styles.secondaryButtonText}>Try Again</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.actionButton, styles.primaryButton]}
                    onPress={handleNavigate}
                  >
                    <Text style={styles.primaryButtonText}>Go</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : processingVoice ? (
              <View style={styles.processingContainer}>
                <ActivityIndicator size="large" color="#4285F4" />
                <Text style={styles.processingText}>Processing your command...</Text>
              </View>
            ) : (
              <View style={styles.listeningContainer}>
                <Text style={styles.instructions}>
                  {isListening
                    ? "I'm listening... Speak a navigation command."
                    : "Tap the microphone and say a command like:"}
                </Text>
                
                {!isListening && (
                  <View style={styles.examplesContainer}>
                    <Text style={styles.exampleCommand}>"Show my medications"</Text>
                    <Text style={styles.exampleCommand}>"Go to appointments"</Text>
                    <Text style={styles.exampleCommand}>"Open health records"</Text>
                  </View>
                )}
                
                <TouchableOpacity
                  style={[styles.micButton, isListening && styles.micButtonActive]}
                  onPress={isListening ? stopListening : startListening}
                >
                  {isListening ? (
                    <Animated.View
                      style={[
                        styles.pulseCircle,
                        {
                          transform: [{ scale: pulseAnim }],
                        },
                      ]}
                    />
                  ) : null}
                  <Ionicons
                    name={isListening ? "mic" : "mic-outline"}
                    size={36}
                    color={isListening ? "#fff" : "#4285F4"}
                  />
                </TouchableOpacity>
                
                {isListening && (
                  <Text style={styles.listeningText}>Listening... Tap to stop</Text>
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 20,
    minHeight: 300,
    justifyContent: 'center',
  },
  listeningContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  instructions: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#555',
  },
  examplesContainer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  exampleCommand: {
    fontSize: 16,
    color: '#4285F4',
    marginBottom: 8,
    fontStyle: 'italic',
  },
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#4285F4',
    marginBottom: 16,
  },
  micButtonActive: {
    backgroundColor: '#4285F4',
  },
  pulseCircle: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(66, 133, 244, 0.3)',
  },
  listeningText: {
    fontSize: 14,
    color: '#EA4335',
    marginTop: 8,
  },
  processingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  processingText: {
    fontSize: 16,
    color: '#4285F4',
    marginTop: 16,
    textAlign: 'center',
  },
  resultContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  recognizedCommandTitle: {
    fontSize: 16,
    color: '#555',
    marginTop: 16,
  },
  recognizedCommand: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4285F4',
    marginBottom: 24,
    textAlign: 'center',
  },
  destinationTitle: {
    fontSize: 16,
    color: '#555',
  },
  destination: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 24,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 16,
  },
  actionButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#4285F4',
  },
  primaryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  secondaryButtonText: {
    color: '#555',
    fontSize: 16,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EA4335',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  tryAgainButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  tryAgainButtonText: {
    color: '#555',
    fontSize: 16,
  },
});

export default VoiceCommandModal;
