import { auth, db } from '../config/firebase';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where
} from 'firebase/firestore';

/**
 * Service for managing doctor-patient relationships in Firebase
 */
export const firebaseDoctorPatientsService = {
  /**
   * Get all patients for the current doctor
   * @returns {Promise<Array>} - Array of patient records with their information
   */
  getDoctorPatients: async () => {
    try {
      // Get current user
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get users collection from Firestore
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      if (usersSnapshot.empty) {
        console.log('No users found in Firestore');
        return [];
      }

      // Filter users to get only patients
      const patients = [];
      usersSnapshot.forEach((doc) => {
        const userData = doc.data();
        if (userData.role && userData.role.toLowerCase() === 'patient') {
          // Check if this patient is linked to the current doctor
          const linkedDoctors = userData.linkedDoctors || [];
          if (linkedDoctors.includes(currentUser.uid)) {
            patients.push({
              id: doc.id,
              uid: doc.id,
              firstName: userData.firstName || '',
              lastName: userData.lastName || '',
              email: userData.email || '',
              phone: userData.phone || '',
              age: userData.age || '',
              gender: userData.gender || '',
              condition: userData.condition || '',
              lastVisit: userData.lastVisit || new Date().toISOString(),
              // Additional patient information
              dateOfBirth: userData.dateOfBirth || '',
              address: userData.address || '',
              city: userData.city || '',
              country: userData.country || '',
              postalCode: userData.postalCode || '',
              emergencyContacts: userData.emergencyContacts || [],
              bloodType: userData.bloodType || '',
              height: userData.height || '',
              weight: userData.weight || '',
              allergies: userData.allergies || [],
              medicalHistory: userData.medicalHistory || '',
              insuranceProvider: userData.insuranceProvider || '',
              insuranceNumber: userData.insuranceNumber || '',
              // Nested medical info if available
              medicalInfo: userData.medicalInfo || {
                primaryDiagnosis: '',
                secondaryDiagnoses: [],
                conditions: [],
                allergies: [],
                familyHistory: [],
                pastSurgeries: [],
                bloodType: '',
                height: '',
                weight: '',
                bmi: ''
              },
              // Additional metadata
              createdAt: userData.createdAt || '',
              updatedAt: userData.updatedAt || '',
              status: userData.status || 'active',
              profileCompleted: userData.profileCompleted || false
            });
          }
        }
      });

      return patients;
    } catch (error) {
      console.error('Error getting doctor patients from Firebase:', error);
      return [];
    }
  },

  /**
   * Get complete patient details from Firebase
   * @param {string} patientId - The patient ID
   * @returns {Promise<Object>} - Complete patient details
   */
  getPatientDetails: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Get patient document from Firestore
      const patientRef = doc(db, 'users', patientId);
      const patientSnap = await getDoc(patientRef);

      if (!patientSnap.exists()) {
        throw new Error('Patient not found');
      }

      const userData = patientSnap.data();
      return {
        id: patientSnap.id,
        uid: patientSnap.id,
        ...userData,
        // Ensure these fields exist even if they're not in the database
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        age: userData.age || '',
        gender: userData.gender || '',
        dateOfBirth: userData.dateOfBirth || '',
        address: userData.address || '',
        city: userData.city || '',
        country: userData.country || '',
        postalCode: userData.postalCode || '',
        emergencyContacts: userData.emergencyContacts || [],
        bloodType: userData.bloodType || '',
        height: userData.height || '',
        weight: userData.weight || '',
        allergies: userData.allergies || [],
        medicalHistory: userData.medicalHistory || '',
        insuranceProvider: userData.insuranceProvider || '',
        insuranceNumber: userData.insuranceNumber || '',
        medicalInfo: userData.medicalInfo || {
          primaryDiagnosis: '',
          secondaryDiagnoses: [],
          conditions: [],
          allergies: [],
          familyHistory: [],
          pastSurgeries: [],
          bloodType: '',
          height: '',
          weight: '',
          bmi: ''
        }
      };
    } catch (error) {
      console.error('Error getting patient details from Firebase:', error);
      return null;
    }
  },

  /**
   * Get patient vitals from Firebase
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of vital records
   */
  getPatientVitals: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for vitals
      const vitalsCollection = collection(db, 'vitals');
      const q = query(
        vitalsCollection,
        where('userId', '==', patientId)
      );

      const querySnapshot = await getDocs(q);
      const vitals = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        vitals.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp (newest first)
      vitals.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      return vitals;
    } catch (error) {
      console.error('Error getting patient vitals from Firebase:', error);
      return [];
    }
  },

  /**
   * Get patient symptoms from Firebase
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of symptom records
   */
  getPatientSymptoms: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for symptoms
      const symptomsCollection = collection(db, 'symptomLogs');
      const q = query(
        symptomsCollection,
        where('userId', '==', patientId)
      );

      const querySnapshot = await getDocs(q);
      const symptoms = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        symptoms.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp (newest first)
      symptoms.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      return symptoms;
    } catch (error) {
      console.error('Error getting patient symptoms from Firebase:', error);
      return [];
    }
  },

  /**
   * Get patient medications from Firebase
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication records
   */
  getPatientMedications: async (patientId) => {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      // Query Firestore for medications
      const medicationsCollection = collection(db, 'medications');
      const q = query(
        medicationsCollection,
        where('patientId', '==', patientId)
      );

      const querySnapshot = await getDocs(q);
      const medications = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        medications.push({
          id: doc.id,
          ...data
        });
      });

      return medications;
    } catch (error) {
      console.error('Error getting patient medications from Firebase:', error);
      return [];
    }
  }
};
