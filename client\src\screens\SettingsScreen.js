import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Appbar } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { getThemeForRole } from '../config/theme';
import Settings from '../components/settings/Settings';

const SettingsScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');

  return (
    <View style={styles.container}>
      <Appbar.Header style={{ backgroundColor: theme.colors.primary }}>
        <Appbar.BackAction color="white" onPress={() => navigation.goBack()} />
        <Appbar.Content title="Settings" color="white" />
      </Appbar.Header>
      <Settings />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default SettingsScreen;
