import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import {
  Text,
  Button,
  TextInput,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Portal,
  Modal,
  List,
  Divider,
} from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import axios from 'axios';
import { API_URL } from '../../../config/constants';

const AppointmentBooking = ({ doctorId, token, onBookingComplete }) => {
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [availableSlots, setAvailableSlots] = useState([]);
  const [reason, setReason] = useState('');
  const [type, setType] = useState('in-person');
  const [showTypeModal, setShowTypeModal] = useState(false);

  const appointmentTypes = [
    { id: 'in-person', label: 'In-Person Visit' },
    { id: 'video', label: 'Video Call' },
    { id: 'phone', label: 'Phone Call' },
  ];

  useEffect(() => {
    if (selectedDate) {
      fetchAvailableSlots();
    }
  }, [selectedDate]);

  const fetchAvailableSlots = async () => {
    try {
      setLoading(true);
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');
      const response = await axios.get(
        `${API_URL}/api/appointments/available-slots/${doctorId}?date=${formattedDate}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setAvailableSlots(response.data);
    } catch (error) {
      console.error('Error fetching available slots:', error);
      Alert.alert('Error', 'Failed to fetch available time slots');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (event, date) => {
    setShowDatePicker(false);
    if (date) {
      setSelectedDate(date);
    }
  };

  const handleTimeSelect = (time) => {
    setSelectedTime(new Date(`2000-01-01T${time}`));
    setShowTimePicker(false);
  };

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !type) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      const appointmentData = {
        doctorId,
        date: format(selectedDate, 'yyyy-MM-dd'),
        time: format(selectedTime, 'HH:mm'),
        reason,
        type,
      };

      const response = await axios.post(
        `${API_URL}/api/appointments/create`,
        appointmentData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      Alert.alert(
        'Success',
        'Appointment request sent successfully. The doctor will confirm your appointment soon.',
        [{ text: 'OK', onPress: onBookingComplete }]
      );
    } catch (error) {
      console.error('Error booking appointment:', error);
      Alert.alert(
        'Error',
        error.response?.data?.error || 'Failed to book appointment'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Book an Appointment</Title>

          <View style={styles.dateTimeContainer}>
            <Button
              mode="outlined"
              onPress={() => setShowDatePicker(true)}
              style={styles.dateButton}
            >
              {format(selectedDate, 'MMMM dd, yyyy')}
            </Button>

            <Button
              mode="outlined"
              onPress={() => setShowTimePicker(true)}
              style={styles.timeButton}
            >
              {format(selectedTime, 'hh:mm a')}
            </Button>
          </View>

          {showDatePicker && (
            <DateTimePicker
              value={selectedDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}

          {showTimePicker && (
            <Portal>
              <Modal
                visible={showTimePicker}
                onDismiss={() => setShowTimePicker(false)}
                contentContainerStyle={styles.modal}
              >
                <Title>Select Time</Title>
                <ScrollView style={styles.timeSlotsContainer}>
                  {availableSlots.map((slot, index) => (
                    <React.Fragment key={slot}>
                      <List.Item
                        title={slot}
                        onPress={() => handleTimeSelect(slot)}
                      />
                      {index < availableSlots.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </ScrollView>
              </Modal>
            </Portal>
          )}

          <Button
            mode="outlined"
            onPress={() => setShowTypeModal(true)}
            style={styles.typeButton}
          >
            {appointmentTypes.find(t => t.id === type)?.label || 'Select Type'}
          </Button>

          <Portal>
            <Modal
              visible={showTypeModal}
              onDismiss={() => setShowTypeModal(false)}
              contentContainerStyle={styles.modal}
            >
              <Title>Select Appointment Type</Title>
              <ScrollView>
                {appointmentTypes.map((appointmentType) => (
                  <React.Fragment key={appointmentType.id}>
                    <List.Item
                      title={appointmentType.label}
                      onPress={() => {
                        setType(appointmentType.id);
                        setShowTypeModal(false);
                      }}
                    />
                    <Divider />
                  </React.Fragment>
                ))}
              </ScrollView>
            </Modal>
          </Portal>

          <TextInput
            label="Reason for Visit"
            value={reason}
            onChangeText={setReason}
            multiline
            numberOfLines={3}
            style={styles.input}
          />

          <Button
            mode="contained"
            onPress={handleBooking}
            style={styles.bookButton}
            loading={loading}
            disabled={loading}
          >
            Book Appointment
          </Button>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  dateButton: {
    flex: 1,
    marginRight: 8,
  },
  timeButton: {
    flex: 1,
    marginLeft: 8,
  },
  typeButton: {
    marginVertical: 16,
  },
  input: {
    marginVertical: 16,
  },
  bookButton: {
    marginTop: 16,
  },
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  timeSlotsContainer: {
    maxHeight: 300,
  },
});

export default AppointmentBooking;