import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { showMessage } from 'react-native-flash-message';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import SharedProfileForm from './SharedProfileForm';
import { authAPI } from '../../config/api';

const ProfileCompletion = () => {
  const { user, updateUserProfile } = useAuth();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (formData) => {
    // Validate required fields
    if (!formData.firstName || !formData.lastName || !formData.phone || !formData.dateOfBirth || !formData.gender) {
      showMessage({
        message: 'Missing Information',
        description: 'Please fill in all required fields',
        type: 'danger',
        backgroundColor: 'rgba(16, 107, 0, 1)',
      });
      return;
    }

    setLoading(true);
    try {
      // Show a progress message
      showMessage({
        message: 'Saving...',
        description: 'Completing your profile',
        type: 'info',
        backgroundColor: 'rgba(16, 107, 0, 1)',
        duration: 2000,
      });

      // Add profileCompleted flag to the data
      const profileData = {
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString()
      };

      // First update Firestore (this is most important)
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, profileData, { merge: true });

      // Update local user state right after Firestore success
      await updateUserProfile(profileData);

      // Then try to update the backend API with a shorter local timeout
      try {
        await Promise.race([
          authAPI.updateProfileWithFallback(profileData),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Local API timeout')), 3000))
        ]);
      } catch (apiError) {
        console.warn('API update timed out, but Firestore updated successfully:', apiError);
        // Don't throw error here, we already updated Firestore
      }

      showMessage({
        message: 'Profile Completed',
        description: 'Your profile has been successfully completed',
        type: 'success',
        backgroundColor: 'rgba(16, 107, 0, 1)',
      });

      // Navigate to dashboard
      navigation.reset({
        index: 0,
        routes: [{ name: 'Dashboard' }],
      });
    } catch (error) {
      showMessage({
        message: 'Error',
        description: 'Failed to update profile. Please try again.',
        type: 'danger',
        backgroundColor: 'rgba(16, 107, 0, 1)',
      });
      console.error('Profile update error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <SharedProfileForm
        initialData={{
          firstName: user?.displayName?.split(' ')[0] || '',
          lastName: user?.displayName?.split(' ').slice(1).join(' ') || '',
          email: user?.email || '',
        }}
        onSubmit={handleSubmit}
        isLoading={loading}
        isRequired={true}
        buttonText="Complete Profile"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default ProfileCompletion;