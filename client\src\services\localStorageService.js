import AsyncStorage from '@react-native-async-storage/async-storage';
import { localVitalsService } from './localVitalsService';

// Keys for AsyncStorage
const MEDICATIONS_STORAGE_KEY = '@neurocare:medications';
const MEDICATION_REMINDERS_KEY = '@neurocare:medication_reminders';

/**
 * Service for managing local storage of medications data
 */
export const localMedicationsService = {
  /**
   * Save a medication record locally
   * @param {Object} medicationData - The medication data to save
   * @returns {Promise<Object>} - The saved medication record with ID
   */
  saveMedication: async (medicationData) => {
    try {
      // Get existing medications
      const existingMedsStr = await AsyncStorage.getItem(MEDICATIONS_STORAGE_KEY);
      const existingMeds = existingMedsStr ? JSON.parse(existingMedsStr) : [];

      // Create new record with ID and timestamp
      const newRecord = {
        id: Date.now().toString(),
        ...medicationData,
        createdAt: new Date().toISOString(),
      };

      // Add to existing records
      const updatedMeds = [newRecord, ...existingMeds];

      // Save back to storage
      await AsyncStorage.setItem(MEDICATIONS_STORAGE_KEY, JSON.stringify(updatedMeds));

      return newRecord;
    } catch (error) {
      console.error('Error saving medication record locally:', error);
      throw new Error('Failed to save medication record');
    }
  },

  /**
   * Get all medication records for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication records
   */
  getPatientMedications: async (patientId) => {
    try {
      // Get all medications
      const medsStr = await AsyncStorage.getItem(MEDICATIONS_STORAGE_KEY);
      const allMeds = medsStr ? JSON.parse(medsStr) : [];

      // Filter by patient ID
      let filteredMeds = allMeds.filter(record => record.patientId === patientId);

      return filteredMeds;
    } catch (error) {
      console.error('Error getting medication records locally:', error);
      return [];
    }
  },

  /**
   * Save a medication reminder
   * @param {Object} reminderData - The reminder data to save
   * @returns {Promise<Object>} - The saved reminder with ID
   */
  saveReminder: async (reminderData) => {
    try {
      // Get existing reminders
      const existingRemindersStr = await AsyncStorage.getItem(MEDICATION_REMINDERS_KEY);
      const existingReminders = existingRemindersStr ? JSON.parse(existingRemindersStr) : [];

      // Create new reminder with ID
      const newReminder = {
        id: Date.now().toString(),
        ...reminderData,
        createdAt: new Date().toISOString(),
        status: reminderData.status || 'scheduled' // scheduled, completed, missed
      };

      // Add to existing records
      const updatedReminders = [newReminder, ...existingReminders];

      // Save back to storage
      await AsyncStorage.setItem(MEDICATION_REMINDERS_KEY, JSON.stringify(updatedReminders));

      return newReminder;
    } catch (error) {
      console.error('Error saving medication reminder locally:', error);
      throw new Error('Failed to save medication reminder');
    }
  },

  /**
   * Get all medication reminders for a patient
   * @param {string} patientId - The patient ID
   * @returns {Promise<Array>} - Array of medication reminders
   */
  getPatientReminders: async (patientId) => {
    try {
      // Get all reminders
      const remindersStr = await AsyncStorage.getItem(MEDICATION_REMINDERS_KEY);
      const allReminders = remindersStr ? JSON.parse(remindersStr) : [];

      // Filter by patient ID
      let filteredReminders = allReminders.filter(record => record.patientId === patientId);

      // Sort by scheduled time (upcoming first)
      filteredReminders.sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime));

      return filteredReminders;
    } catch (error) {
      console.error('Error getting medication reminders locally:', error);
      return [];
    }
  },

  /**
   * Update a medication reminder status
   * @param {string} reminderId - The reminder ID to update
   * @param {string} status - The new status (scheduled, completed, missed)
   * @returns {Promise<Object>} - The updated reminder
   */
  updateReminderStatus: async (reminderId, status) => {
    try {
      // Get all reminders
      const remindersStr = await AsyncStorage.getItem(MEDICATION_REMINDERS_KEY);
      const allReminders = remindersStr ? JSON.parse(remindersStr) : [];

      // Find the reminder to update
      const reminderIndex = allReminders.findIndex(r => r.id === reminderId);

      if (reminderIndex === -1) {
        throw new Error('Reminder not found');
      }

      // Update the status
      allReminders[reminderIndex] = {
        ...allReminders[reminderIndex],
        status,
        updatedAt: new Date().toISOString()
      };

      // Save back to storage
      await AsyncStorage.setItem(MEDICATION_REMINDERS_KEY, JSON.stringify(allReminders));

      return allReminders[reminderIndex];
    } catch (error) {
      console.error('Error updating medication reminder status:', error);
      throw new Error('Failed to update reminder status');
    }
  },


};

// Prescriptions are now managed by firebasePrescriptionsService

// Export the localVitalsService
export { localVitalsService };