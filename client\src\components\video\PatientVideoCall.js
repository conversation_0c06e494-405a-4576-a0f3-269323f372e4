import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Platform,
  ActivityIndicator,
  BackHandler
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { PATIENT_COLORS } from '../../config/theme';

const PatientVideoCall = ({ doctorInfo, onCallEnd, callId, roomName }) => {
  const { user } = useAuth();
  const webViewRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [isMicOn, setIsMicOn] = useState(true);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [callStartTime, setCallStartTime] = useState(null);
  const [callDuration, setCallDuration] = useState(0);
  const [jitsiUrl, setJitsiUrl] = useState('');

  // Handle back button press
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (roomName) {
        handleEndCall();
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [roomName]);

  // Initialize video call
  useEffect(() => {
    if (roomName) {
      setCallStartTime(new Date());

      // Create Jitsi Meet URL
      const url = `https://meet.jit.si/${roomName}#userInfo.displayName="${encodeURIComponent(`${user.firstName} ${user.lastName}`)}"&config.prejoinPageEnabled=false`;
      setJitsiUrl(url);
    } else {
      onCallEnd();
      Alert.alert('Error', 'Invalid call information');
    }

    // Set up timer for call duration
    const durationTimer = setInterval(() => {
      if (callStartTime) {
        const now = new Date();
        const durationInSeconds = Math.floor((now - callStartTime) / 1000);
        setCallDuration(durationInSeconds);
      }
    }, 1000);

    return () => {
      clearInterval(durationTimer);
    };
  }, [roomName]);

  // Format call duration as MM:SS
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle call end
  const handleEndCall = async () => {
    try {
      if (callId) {
        await axios.post(`/api/video/${callId}/end`);
      }

      if (webViewRef.current) {
        // Execute JavaScript to hang up the call
        webViewRef.current.injectJavaScript(`
          try {
            const api = window.api;
            if (api) {
              api.executeCommand('hangup');
            }
          } catch (e) {
            console.error(e);
          }
          true;
        `);
      }

      onCallEnd();
    } catch (error) {
      console.error('Error ending call:', error);
      onCallEnd(); // End call anyway
    }
  };

  // Toggle microphone
  const toggleMic = () => {
    if (webViewRef.current) {
      webViewRef.current.injectJavaScript(`
        try {
          const api = window.api;
          if (api) {
            api.executeCommand('toggleAudio');
          }
        } catch (e) {
          console.error(e);
        }
        true;
      `);
      setIsMicOn(!isMicOn);
    }
  };

  // Toggle camera
  const toggleCamera = () => {
    if (webViewRef.current) {
      webViewRef.current.injectJavaScript(`
        try {
          const api = window.api;
          if (api) {
            api.executeCommand('toggleVideo');
          }
        } catch (e) {
          console.error(e);
        }
        true;
      `);
      setIsCameraOn(!isCameraOn);
    }
  };

  // JavaScript to inject into the WebView to get the Jitsi Meet API
  const jitsiApiScript = `
    window.onload = () => {
      const domain = 'meet.jit.si';
      const options = {
        roomName: '${roomName}',
        width: '100%',
        height: '100%',
        parentNode: document.body,
        userInfo: {
          displayName: '${user.firstName} ${user.lastName}'
        },
        configOverwrite: {
          prejoinPageEnabled: false,
          startWithAudioMuted: false,
          startWithVideoMuted: false
        },
        interfaceConfigOverwrite: {
          TOOLBAR_BUTTONS: [
            'microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen',
            'fodeviceselection', 'hangup', 'profile', 'chat', 'recording',
            'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',
            'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
            'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone',
            'security'
          ],
        }
      };
      window.api = new JitsiMeetExternalAPI(domain, options);
      window.api.addEventListeners({
        videoConferenceJoined: () => {
          window.ReactNativeWebView.postMessage(JSON.stringify({type: 'videoConferenceJoined'}));
        },
        videoConferenceLeft: () => {
          window.ReactNativeWebView.postMessage(JSON.stringify({type: 'videoConferenceLeft'}));
        },
        audioMuteStatusChanged: (muted) => {
          window.ReactNativeWebView.postMessage(JSON.stringify({type: 'audioMuteStatusChanged', muted}));
        },
        videoMuteStatusChanged: (muted) => {
          window.ReactNativeWebView.postMessage(JSON.stringify({type: 'videoMuteStatusChanged', muted}));
        }
      });
    };
    true;
  `;

  // Handle WebView messages
  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      switch (data.type) {
        case 'videoConferenceJoined':
          console.log('Conference joined');
          setLoading(false);
          break;
        case 'videoConferenceLeft':
          console.log('Conference left');
          onCallEnd();
          break;
        case 'audioMuteStatusChanged':
          setIsMicOn(!data.muted);
          break;
        case 'videoMuteStatusChanged':
          setIsCameraOn(!data.muted);
          break;
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  if (loading || !jitsiUrl) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={PATIENT_COLORS.primary} />
        <Text style={styles.loadingText}>Joining video call...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.callInfoBar}>
        <Text style={styles.callDuration}>Duration: {formatDuration(callDuration)}</Text>
        <Text style={styles.doctorName}>
          Dr. {doctorInfo?.lastName || 'Doctor'}
        </Text>
      </View>

      <View style={styles.videoContainer}>
        <WebView
          ref={webViewRef}
          source={{ uri: jitsiUrl }}
          style={styles.jitsiView}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
          userAgent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36"
          injectedJavaScript={jitsiApiScript}
          onMessage={handleWebViewMessage}
          onError={(error) => {
            console.error('WebView error:', error);
            Alert.alert('Error', 'Failed to load video call interface');
          }}
        />
      </View>

      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.controlButton, !isMicOn && styles.controlButtonOff]}
          onPress={toggleMic}
        >
          <Ionicons
            name={isMicOn ? "mic" : "mic-off"}
            size={24}
            color="#fff"
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, !isCameraOn && styles.controlButtonOff]}
          onPress={toggleCamera}
        >
          <Ionicons
            name={isCameraOn ? "videocam" : "videocam-off"}
            size={24}
            color="#fff"
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, styles.endCallButton]}
          onPress={handleEndCall}
        >
          <Ionicons name="call" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  callInfoBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  callDuration: {
    color: '#fff',
    fontSize: 14,
  },
  doctorName: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  videoContainer: {
    flex: 1,
  },
  jitsiView: {
    flex: 1,
    height: '100%',
    width: '100%',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: PATIENT_COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonOff: {
    backgroundColor: '#555',
  },
  endCallButton: {
    backgroundColor: '#E53935',
    transform: [{ rotate: '135deg' }],
  },
});

export default PatientVideoCall;
