import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';

const { width } = Dimensions.get('window');

// Helper function to safely handle color opacity
const withOpacity = (color, opacity = 1) => {
  if (!color) return `rgba(0, 0, 0, ${opacity})`;
  
  // If it's an rgba color
  if (color.startsWith('rgba')) {
    return color.replace(/[^,]+(?=\))/, opacity);
  }
  
  // If it's an rgb color
  if (color.startsWith('rgb(')) {
    return color.replace('rgb(', `rgba(`).replace(')', `, ${opacity})`);
  }
  
  // If it's a hex color
  if (color.startsWith('#')) {
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  return color;
};

const DashboardChart = ({
  type = 'line',
  data = [],
  labels = [],
  title,
  subtitle,
  height = 220,
  width: chartWidth = width - 40,
  backgroundColor = '#fff',
  chartConfig = {},
  color = 'rgba(29, 85, 168, 1)',
  style,
}) => {
  // Ensure data is valid
  const safeData = Array.isArray(data) ? data : [];
  const safeLabels = Array.isArray(labels) ? labels : [];
  
  // Make sure we have at least one data point
  if (safeData.length === 0) {
    safeData.push(0);
  }
  
  // Make sure we have labels for each data point
  while (safeLabels.length < safeData.length) {
    safeLabels.push(`Item ${safeLabels.length + 1}`);
  }

  const defaultChartConfig = {
    backgroundColor: backgroundColor,
    backgroundGradientFrom: backgroundColor,
    backgroundGradientTo: backgroundColor,
    decimalPlaces: 0,
    color: (opacity = 1) => withOpacity(color, opacity),
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: color,
    },
    ...chartConfig,
  };

  // Format data for LineChart and BarChart
  const formattedData = {
    labels: safeLabels,
    datasets: [
      {
        data: safeData,
        color: (opacity = 1) => withOpacity(color, opacity),
        strokeWidth: 2
      }
    ],
    legend: ['']
  };

  const renderChart = () => {
    try {
      switch (type) {
        case 'line':
          return (
            <LineChart
              data={formattedData}
              width={chartWidth}
              height={height}
              chartConfig={defaultChartConfig}
              bezier
              style={styles.chart}
            />
          );
        case 'bar':
          return (
            <BarChart
              data={formattedData}
              width={chartWidth}
              height={height}
              chartConfig={defaultChartConfig}
              style={styles.chart}
              fromZero
            />
          );
        case 'pie':
          return (
            <PieChart
              data={safeData.map((value, index) => ({
                name: safeLabels[index] || `Item ${index}`,
                value: value || 0,
                color: `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 1)`,
                legendFontColor: '#7F7F7F',
                legendFontSize: 12
              }))}
              width={chartWidth}
              height={height}
              chartConfig={defaultChartConfig}
              accessor="value"
              backgroundColor="transparent"
              paddingLeft="10"
              style={styles.chart}
            />
          );
        default:
          return <Text>Unsupported chart type</Text>;
      }
    } catch (error) {
      console.error('Chart rendering error:', error);
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Unable to display chart</Text>
        </View>
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {title && <Text style={styles.title}>{title}</Text>}
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#212121',
  },
  subtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 16,
  },
  chartContainer: {
    alignItems: 'center',
  },
  chart: {
    borderRadius: 12,
    marginVertical: 8,
  },
  errorContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#757575',
    fontSize: 14,
  }
});

export default DashboardChart;