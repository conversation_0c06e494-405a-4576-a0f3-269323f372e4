import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Modal,
  TextInput,
  Alert,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ROLE_COLORS } from '../../../config/theme';
import PatientVitalsViewer from './PatientVitalsViewer';
import PatientSymptomsViewer from './PatientSymptomsViewer';
import PatientMedic<PERSON>Viewer from './PatientMedicationsViewer';
import PatientMedicalNotes from './PatientMedicalNotes';
import PatientDetailCard from './PatientDetailCard';
import { localMedicalNotesService } from '../../../services/localMedicalNotesService';
import { firebaseDoctorPatientsService } from '../../../services/firebaseDoctorPatientsService';

const PatientHealthViewer = () => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [patientDetails, setPatientDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('vitals');
  const [activeCategory, setActiveCategory] = useState('vitals');
  const [showNotes, setShowNotes] = useState(false);
  const [notesModalVisible, setNotesModalVisible] = useState(false);
  const [medicalNote, setMedicalNote] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const doctorColors = ROLE_COLORS.doctor;

  // Fetch doctor's patients from Firebase
  useEffect(() => {
    fetchDoctorPatients();
  }, []);

  const fetchDoctorPatients = async () => {
    setLoading(true);
    try {
      const doctorPatients = await firebaseDoctorPatientsService.getDoctorPatients();
      setPatients(doctorPatients);
    } catch (error) {
      console.error('Error fetching doctor patients:', error);
      Alert.alert('Error', 'Failed to load patients. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPatient = async (patient) => {
    setSelectedPatient(patient);
    setActiveTab('vitals');
    setActiveCategory('vitals');

    // Fetch detailed patient information
    setLoadingDetails(true);
    try {
      const details = await firebaseDoctorPatientsService.getPatientDetails(patient.id);
      setPatientDetails(details);
    } catch (error) {
      console.error('Error fetching patient details:', error);
      // If we can't get details, use the basic patient info
      setPatientDetails(patient);
    } finally {
      setLoadingDetails(false);
    }
  };

  const handleAddNotes = () => {
    setMedicalNote('');
    setNotesModalVisible(true);
  };

  const toggleNotes = () => {
    setShowNotes(!showNotes);
  };

  const handleSaveNotes = async () => {
    if (!medicalNote.trim()) {
      Alert.alert('Error', 'Please enter some notes before saving');
      return;
    }

    setSubmitting(true);
    try {
      const noteData = {
        patientId: selectedPatient.id,
        content: medicalNote,
        type: activeTab === 'vitals' ? 'vitals' : 'symptoms',
        category: activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms',
        doctorId: 'doctor-user', // In a real app, this would be the logged-in doctor's ID
        patientName: `${selectedPatient.firstName} ${selectedPatient.lastName}`,
        timestamp: new Date().toISOString()
      };

      await localMedicalNotesService.saveMedicalNote(noteData);
      Alert.alert(
        'Success',
        `Medical notes for ${activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms'} saved successfully`
      );
      setNotesModalVisible(false);
    } catch (error) {
      console.error('Error saving medical notes:', error);
      Alert.alert('Error', 'Failed to save medical notes');
    } finally {
      setSubmitting(false);
    }
  };

  // Render a patient card
  const renderPatientCard = ({ item }) => (
    <TouchableOpacity
      style={styles.patientCard}
      onPress={() => handleSelectPatient(item)}
    >
      <View style={styles.patientAvatarContainer}>
        <Text style={styles.patientAvatarText}>
          {item.firstName.charAt(0)}{item.lastName.charAt(0)}
        </Text>
      </View>
      <View style={styles.patientInfo}>
        <Text style={styles.patientName}>{item.firstName} {item.lastName}</Text>
        <Text style={styles.patientEmail}>{item.email}</Text>
        <Text style={styles.patientPhone}>{item.phone || 'No phone number'}</Text>
      </View>
      <View style={styles.patientActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
          onPress={() => {
            handleSelectPatient(item);
            setActiveCategory('vitals');
          }}
        >
          <Ionicons name="pulse" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Vital Signs</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#2196F3' }]}
          onPress={() => {
            handleSelectPatient(item);
            setActiveCategory('symptoms');
          }}
        >
          <Ionicons name="document-text" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Symptoms</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF9800' }]}
          onPress={() => {
            handleSelectPatient(item);
            setActiveCategory('medications');
          }}
        >
          <Ionicons name="medkit" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Medicines</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  // Render the patient list
  const renderPatientList = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      );
    }

    if (patients.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="people" size={48} color="#ccc" />
          <Text style={styles.emptyText}>No patients found</Text>
          <Text style={styles.emptySubtext}>You don't have any patients linked to your account yet.</Text>
        </View>
      );
    }

    return (
      <FlatList
        data={patients}
        renderItem={renderPatientCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.patientList}
        nestedScrollEnabled={true}
      />
    );
  };

  // Render the patient detail view
  const renderPatientDetail = () => {
    if (!selectedPatient) {
      return null;
    }

    return (
      <View style={styles.contentContainer}>
        <View style={styles.patientHeader}>
          <View style={styles.patientHeaderInfo}>
            <View style={styles.patientDetailAvatar}>
              <Text style={styles.patientDetailAvatarText}>
                {selectedPatient.firstName.charAt(0)}{selectedPatient.lastName.charAt(0)}
              </Text>
            </View>
            <View>
              <Text style={styles.patientDetailName}>{selectedPatient.firstName} {selectedPatient.lastName}</Text>
              <Text style={styles.patientDetailInfo}>{selectedPatient.email}</Text>
              <Text style={styles.patientDetailInfo}>{selectedPatient.phone || 'No phone number'}</Text>
            </View>
          </View>
        </View>

        {/* Patient Details Card */}
        {loadingDetails ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={doctorColors.primary} />
            <Text style={styles.loadingText}>Loading patient details...</Text>
          </View>
        ) : (
          <PatientDetailCard patient={patientDetails || selectedPatient} />
        )}

        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'vitals' && styles.activeTab]}
            onPress={() => setActiveTab('vitals')}
          >
            <Ionicons
              name="pulse"
              size={20}
              color={activeTab === 'vitals' ? '#fff' : doctorColors.primary}
            />
            <Text style={[styles.tabText, activeTab === 'vitals' && styles.activeTabText]}>
              Vital Signs
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'symptoms' && styles.activeTab]}
            onPress={() => setActiveTab('symptoms')}
          >
            <Ionicons
              name="document-text"
              size={20}
              color={activeTab === 'symptoms' ? '#fff' : doctorColors.primary}
            />
            <Text style={[styles.tabText, activeTab === 'symptoms' && styles.activeTabText]}>
              Symptoms
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'medications' && styles.activeTab]}
            onPress={() => setActiveTab('medications')}
          >
            <Ionicons
              name="medkit"
              size={20}
              color={activeTab === 'medications' ? '#fff' : doctorColors.primary}
            />
            <Text style={[styles.tabText, activeTab === 'medications' && styles.activeTabText]}>
              Medicines
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.contentActions}>
          <TouchableOpacity
            style={styles.toggleNotesButton}
            onPress={toggleNotes}
          >
            <Ionicons
              name={showNotes ? "pulse" : "document-text"}
              size={20}
              color={doctorColors.primary}
            />
            <Text style={styles.toggleNotesText}>
              {showNotes ? 'Show Health Data' : 'Show Notes'}
            </Text>
          </TouchableOpacity>
        </View>

        {!showNotes ? (
          // Show health data based on active tab
          activeTab === 'vitals' ? (
            <>
              <PatientVitalsViewer
                patientId={selectedPatient.id}
                patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
              />
              <View style={styles.addNotesButtonContainer}>
                <TouchableOpacity
                  style={styles.addNotesButton}
                  onPress={handleAddNotes}
                >
                  <Ionicons name="add-circle" size={20} color="#fff" />
                  <Text style={styles.addNotesButtonText}>Add Notes for Vitals</Text>
                </TouchableOpacity>
              </View>
            </>
          ) : activeTab === 'symptoms' ? (
            <>
              <PatientSymptomsViewer
                patientId={selectedPatient.id}
                patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
              />
              <View style={styles.addNotesButtonContainer}>
                <TouchableOpacity
                  style={styles.addNotesButton}
                  onPress={handleAddNotes}
                >
                  <Ionicons name="add-circle" size={20} color="#fff" />
                  <Text style={styles.addNotesButtonText}>Add Notes for Symptoms</Text>
                </TouchableOpacity>
              </View>
            </>
          ) : (
            // Medications tab
            <PatientMedicationsViewer
              patientId={selectedPatient.id}
              patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
            />
          )
        ) : (
          // Show notes specific to the active tab
          <PatientMedicalNotes
            patientId={selectedPatient.id}
            patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
            noteType={activeTab === 'vitals' ? 'vitals' : 'symptoms'}
          />
        )}
      </View>
    );
  };

  // Main content renderer
  const renderContent = () => {
    if (selectedPatient) {
      return renderPatientDetail();
    } else {
      return renderPatientList();
    }
  };

  // Render the notes modal
  const renderNotesModal = () => {
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={notesModalVisible}
        onRequestClose={() => setNotesModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                Add Medical Notes for {activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setNotesModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.modalSubtitle}>
                Patient: {selectedPatient?.firstName} {selectedPatient?.lastName}
              </Text>
              <Text style={styles.modalSubtitle}>
                Section: <Text style={styles.highlightedText}>{activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms'}</Text>
              </Text>

              <TextInput
                style={styles.notesInput}
                value={medicalNote}
                onChangeText={setMedicalNote}
                placeholder="Enter medical notes here..."
                placeholderTextColor="#999"
                multiline
                numberOfLines={10}
              />

              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveNotes}
                disabled={submitting}
              >
                <Text style={styles.saveButtonText}>
                  {submitting ? 'Saving...' : 'Save Notes'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {selectedPatient && (
        <View style={styles.backButtonContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setSelectedPatient(null)}
          >
            <Ionicons name="arrow-back" size={24} color={doctorColors.primary} />
            <Text style={styles.backButtonText}>Back to Patients</Text>
          </TouchableOpacity>
        </View>
      )}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Patient Health Data</Text>
        <Text style={styles.headerSubtitle}>
          {selectedPatient
            ? `Viewing health data for ${selectedPatient.firstName} ${selectedPatient.lastName}`
            : 'View vital signs, symptoms and medications'}
        </Text>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContentContainer}
        showsVerticalScrollIndicator={true}
      >
        {renderContent()}
      </ScrollView>
      {renderNotesModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  backButtonContainer: {
    backgroundColor: '#fff',
    paddingTop: 15,
    paddingLeft: 15,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    maxWidth: '80%',
  },
  patientList: {
    padding: 16,
  },
  patientCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  patientAvatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ROLE_COLORS.doctor.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  patientAvatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  patientInfo: {
    marginBottom: 16,
  },
  patientName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  patientEmail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  patientPhone: {
    fontSize: 14,
    color: '#666',
  },
  patientActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    flex: 1,
    marginHorizontal: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  patientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  patientHeaderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  patientDetailAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ROLE_COLORS.doctor.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  patientDetailAvatarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  patientDetailName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  patientDetailInfo: {
    fontSize: 12,
    color: '#666',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f0f7ff',
    borderWidth: 1,
    borderColor: ROLE_COLORS.doctor.primary,
    alignSelf: 'flex-start',
  },
  backButtonText: {
    color: ROLE_COLORS.doctor.primary,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  contentContainer: {
    flex: 1,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 16,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  activeTab: {
    backgroundColor: ROLE_COLORS.doctor.primary,
    borderColor: ROLE_COLORS.doctor.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginLeft: 8,
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  addNotesButtonContainer: {
    padding: 16,
    alignItems: 'center',
  },
  contentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  toggleNotesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f7ff',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ROLE_COLORS.doctor.primary,
  },
  toggleNotesText: {
    color: ROLE_COLORS.doctor.primary,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  addNotesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ROLE_COLORS.doctor.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    elevation: 2,
  },
  addNotesButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  comingSoonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
  },
  comingSoonSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 6,
  },
  modalContent: {
    padding: 16,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  highlightedText: {
    color: ROLE_COLORS.doctor.primary,
    fontWeight: 'bold',
  },
  notesInput: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#eaeaea',
    color: '#333',
    fontSize: 16,
    minHeight: 150,
    textAlignVertical: 'top',
    marginTop: 8,
    marginBottom: 16,
  },
  saveButton: {
    backgroundColor: ROLE_COLORS.doctor.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
  },
});

export default PatientHealthViewer;
