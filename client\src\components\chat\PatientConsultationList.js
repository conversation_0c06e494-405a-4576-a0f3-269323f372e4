import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  TextInput,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { ROLE_COLORS, COLORS } from '../../config/theme';
import { auth, db } from '../../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';

const PatientConsultationList = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch patients from Firebase
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);

        // Get current user (doctor)
        const currentUser = auth.currentUser;
        if (!currentUser) {
          console.error('No authenticated user found');
          setLoading(false);
          return;
        }

        // Get users collection from Firestore
        const usersCollection = collection(db, 'users');
        const usersSnapshot = await getDocs(usersCollection);

        if (usersSnapshot.empty) {
          console.log('No users found in Firestore');
          setPatients([]);
          setLoading(false);
          return;
        }

        // Filter users to get only patients linked to this doctor
        const patientsList = [];
        usersSnapshot.forEach((doc) => {
          const userData = doc.data();
          if (userData.role && userData.role.toLowerCase() === 'patient') {
            // Check if this patient is linked to the current doctor
            const linkedDoctors = userData.linkedDoctors || [];
            if (linkedDoctors.includes(currentUser.uid)) {
              patientsList.push({
                id: doc.id,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                age: userData.age || 0,
                condition: userData.condition || 'Not specified',
                lastConsultation: userData.lastVisit || new Date().toISOString().split('T')[0],
                status: Math.random() > 0.5 ? 'online' : 'offline', // Simulate online status randomly
                profileImage: userData.profileImage || null
              });
            }
          }
        });

        setPatients(patientsList);
      } catch (error) {
        console.error('Error fetching patients from Firebase:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Start consultation with a patient
  const startConsultation = (patient) => {
    // Ajouter un petit délai pour l'effet visuel
    setTimeout(() => {
      navigation.navigate('Chatroom', { patientInfo: patient });
    }, 150);
  };

  // Render patient item
  const renderPatientItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.patientCard}
        onPress={() => startConsultation(item)}
      >
        <View style={styles.patientInfo}>
          <View style={styles.avatarContainer}>
            {item.profileImage ? (
              <Image source={{ uri: item.profileImage }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Text style={styles.avatarText}>
                  {item.firstName.charAt(0)}{item.lastName.charAt(0)}
                </Text>
              </View>
            )}
            <View style={[
              styles.statusIndicator,
              item.status === 'online' ? styles.statusOnline : styles.statusOffline
            ]} />
          </View>

          <View style={styles.patientDetails}>
            <Text style={styles.patientName}>
              {item.firstName} {item.lastName}
            </Text>
            <Text style={styles.patientCondition}>
              {item.condition}
            </Text>
            <Text style={styles.patientAge}>
              Age: {item.age}
            </Text>
          </View>
        </View>

        <View style={styles.consultationInfo}>
          <Text style={styles.lastConsultation}>
            Last: {new Date(item.lastConsultation).toLocaleDateString()}
          </Text>

          <TouchableOpacity
            style={[
              styles.consultButton,
              item.status === 'online' ? styles.consultButtonActive : styles.consultButtonDisabled
            ]}
            onPress={() => startConsultation(item)}
            disabled={item.status !== 'online'}
            activeOpacity={0.6} // Effet visuel de clic
          >
            <Ionicons name="videocam" size={18} color="#fff" />
            <Text style={styles.consultButtonText}>
              {item.status === 'online' ? 'Consult Now' : 'Offline'}
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <LinearGradient
      colors={[ROLE_COLORS.doctor.primaryLight, '#f5f5f5']}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 0.2 }}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Patient Consultations</Text>
        <Text style={styles.subtitle}>Start a video consultation with your patients</Text>
      </View>

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#757575" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search patients..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity style={[styles.filterButton, styles.activeFilter]}>
          <Text style={styles.activeFilterText}>All Patients</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterText}>Online</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterText}>Recent</Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ROLE_COLORS.doctor.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredPatients}
          renderItem={renderPatientItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="people" size={64} color="#ccc" />
              <Text style={styles.emptyText}>No patients found</Text>
              <Text style={styles.emptySubText}>
                Patients linked to you will appear here
              </Text>
            </View>
          }
        />
      )}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#757575',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 16,
    marginVertical: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#212121',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginVertical: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  activeFilter: {
    backgroundColor: ROLE_COLORS.doctor.primary,
  },
  filterText: {
    color: '#757575',
    fontWeight: '500',
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  patientCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    elevation: 2,
  },
  patientInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ROLE_COLORS.doctor.primaryLighter,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ROLE_COLORS.doctor.primary,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#fff',
  },
  statusOnline: {
    backgroundColor: '#4CAF50',
  },
  statusOffline: {
    backgroundColor: '#9e9e9e',
  },
  patientDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 2,
  },
  patientCondition: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 2,
  },
  patientAge: {
    fontSize: 12,
    color: '#9e9e9e',
  },
  consultationInfo: {
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  lastConsultation: {
    fontSize: 12,
    color: '#9e9e9e',
    marginBottom: 8,
  },
  consultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  consultButtonActive: {
    backgroundColor: ROLE_COLORS.doctor.primary,
  },
  consultButtonDisabled: {
    backgroundColor: '#9e9e9e',
  },
  consultButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 6,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 300,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 10,
  },
  emptySubText: {
    fontSize: 14,
    color: '#9e9e9e',
    textAlign: 'center',
    marginTop: 5,
  },
});

export default PatientConsultationList;
