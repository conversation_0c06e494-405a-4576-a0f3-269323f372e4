import AsyncStorage from '@react-native-async-storage/async-storage';

// Key for AsyncStorage
const SYMPTOMS_STORAGE_KEY = '@neurocare:symptoms';

/**
 * Service for managing local storage of symptoms data
 */
export const localSymptomsService = {
  /**
   * Save a symptom log locally
   * @param {Object} symptomData - The symptom data to save
   * @returns {Promise<Object>} - The saved symptom log with ID
   */
  saveSymptomLog: async (symptomData) => {
    try {
      // Get existing symptom logs
      const existingLogsStr = await AsyncStorage.getItem(SYMPTOMS_STORAGE_KEY);
      const existingLogs = existingLogsStr ? JSON.parse(existingLogsStr) : [];

      // Create new log with ID and timestamp
      const newLog = {
        id: Date.now().toString(),
        ...symptomData,
        timestamp: new Date().toISOString(),
      };

      // Add to existing logs
      const updatedLogs = [newLog, ...existingLogs];

      // Save back to storage
      await AsyncStorage.setItem(SYMPTOMS_STORAGE_KEY, JSON.stringify(updatedLogs));

      return newLog;
    } catch (error) {
      console.error('Error saving symptom log locally:', error);
      throw new Error('Failed to save symptom log');
    }
  },

  /**
   * Get all symptom logs for a patient
   * @param {string} patientId - The patient ID
   * @param {number} [days] - Optional filter by number of days back
   * @param {number} [limit] - Optional limit on number of records
   * @returns {Promise<Array>} - Array of symptom logs
   */
  getPatientSymptomLogs: async (patientId, days = null, limit = null) => {
    try {
      // Get all symptom logs
      const logsStr = await AsyncStorage.getItem(SYMPTOMS_STORAGE_KEY);
      const allLogs = logsStr ? JSON.parse(logsStr) : [];

      // Filter by patient ID
      let filteredLogs = allLogs.filter(log => log.patientId === patientId);

      // Filter by days if specified
      if (days && typeof days === 'number') {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= cutoffDate);
      }

      // Sort by timestamp (newest first)
      filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Apply limit if specified
      if (limit && typeof limit === 'number') {
        filteredLogs = filteredLogs.slice(0, limit);
      }

      return filteredLogs;
    } catch (error) {
      console.error('Error getting symptom logs locally:', error);
      return [];
    }
  },

  /**
   * Get all patients with symptom logs
   * @returns {Promise<Array>} - Array of patient IDs with symptom logs
   */
  getPatientsWithSymptomLogs: async () => {
    try {
      // Get all symptom logs
      const logsStr = await AsyncStorage.getItem(SYMPTOMS_STORAGE_KEY);
      const allLogs = logsStr ? JSON.parse(logsStr) : [];

      // Get unique patient IDs
      const patientIds = [...new Set(allLogs.map(log => log.patientId))];

      return patientIds;
    } catch (error) {
      console.error('Error getting patients with symptom logs:', error);
      return [];
    }
  },

  /**
   * Get symptom summary for a patient
   * @param {string} patientId - The patient ID
   * @param {number} [days=30] - Number of days to include in summary
   * @returns {Promise<Object>} - Summary of symptoms
   */
  getPatientSymptomSummary: async (patientId, days = 30) => {
    try {
      // Get symptom logs for the specified period
      const logs = await localSymptomsService.getPatientSymptomLogs(patientId, days);

      if (logs.length === 0) {
        return {
          mostCommonSymptoms: [],
          averageMood: null,
          symptomFrequency: {},
          symptomSeverity: {}
        };
      }

      // Extract all symptoms
      const allSymptoms = logs.flatMap(log => log.symptoms || []);

      // Count symptom occurrences
      const symptomCounts = {};
      allSymptoms.forEach(symptom => {
        const type = symptom.type;
        symptomCounts[type] = (symptomCounts[type] || 0) + 1;
      });

      // Calculate average severity by symptom type
      const symptomSeverities = {};
      allSymptoms.forEach(symptom => {
        const type = symptom.type;
        if (!symptomSeverities[type]) {
          symptomSeverities[type] = {
            total: 0,
            count: 0
          };
        }
        symptomSeverities[type].total += symptom.severity;
        symptomSeverities[type].count += 1;
      });

      // Calculate average mood if available
      let averageMood = null;
      const moodLogs = logs.filter(log => log.mood);
      if (moodLogs.length > 0) {
        // This is simplified - in a real app, you'd map moods to numeric values
        const moodMap = {
          'Excellent': 5,
          'Good': 4,
          'Okay': 3,
          'Fair': 2,
          'Poor': 1,
          'Distressed': 0
        };

        let moodSum = 0;
        let moodCount = 0;

        moodLogs.forEach(log => {
          if (moodMap[log.mood] !== undefined) {
            moodSum += moodMap[log.mood];
            moodCount++;
          }
        });

        if (moodCount > 0) {
          const moodAvg = moodSum / moodCount;
          // Convert back to string representation
          const moodValues = Object.keys(moodMap);
          const moodScores = Object.values(moodMap);

          // Find closest mood
          let closestMood = moodValues[0];
          let closestDistance = Math.abs(moodScores[0] - moodAvg);

          for (let i = 1; i < moodValues.length; i++) {
            const distance = Math.abs(moodScores[i] - moodAvg);
            if (distance < closestDistance) {
              closestDistance = distance;
              closestMood = moodValues[i];
            }
          }

          averageMood = closestMood;
        }
      }

      // Get most common symptoms (top 3)
      const mostCommonSymptoms = Object.entries(symptomCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([type, count]) => ({
          type,
          count,
          averageSeverity: symptomSeverities[type].total / symptomSeverities[type].count
        }));

      // Calculate average severity for each symptom
      const symptomSeverity = {};
      Object.keys(symptomSeverities).forEach(type => {
        symptomSeverity[type] = symptomSeverities[type].total / symptomSeverities[type].count;
      });

      return {
        mostCommonSymptoms,
        averageMood,
        symptomFrequency: symptomCounts,
        symptomSeverity
      };
    } catch (error) {
      console.error('Error getting symptom summary:', error);
      return {
        mostCommonSymptoms: [],
        averageMood: null,
        symptomFrequency: {},
        symptomSeverity: {}
      };
    }
  },

  /**
   * Clear all symptom logs (for testing)
   */
  clearAllSymptomLogs: async () => {
    try {
      await AsyncStorage.removeItem(SYMPTOMS_STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing symptom logs:', error);
    }
  }
};


