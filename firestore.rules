rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user is accessing their own data
    function isUser(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Allow authenticated users to read and write their own appointments
    match /appointments/{appointmentId} {
      allow read: if request.auth != null && (
        // Patient or doctor can read their own appointments
        resource.data.patientId == request.auth.uid ||
        resource.data.doctorId == request.auth.uid ||

        // Caregivers can read appointments of their linked patients
        (
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'caregiver' &&
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          resource.data.patientId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients
        ) ||

        // Supervisors can read appointments of their linked patients
        (
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor' &&
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          resource.data.patientId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients
        )
      );

      allow create: if request.auth != null &&
                     request.resource.data.patientId == request.auth.uid;

      allow update, delete: if request.auth != null && (
        resource.data.patientId == request.auth.uid ||
        resource.data.doctorId == request.auth.uid
      );
    }

    // Allow users to read and write their own data
    match /users/{userId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == userId;

      // Allow users to update their own data
      // Also allow supervisors to update linkedCaregivers and linkedPatients arrays
      allow update: if request.auth != null && (
        request.auth.uid == userId ||
        (
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor' &&
          (
            request.resource.data.diff(resource.data).affectedKeys().hasOnly(['linkedCaregivers']) ||
            request.resource.data.diff(resource.data).affectedKeys().hasOnly(['linkedPatients'])
          )
        )
      );

      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    // Symptoms collection
    match /symptoms/{symptomId} {
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Symptom Logs collection
    match /symptomLogs/{logId} {
      // Allow users to read and write their own symptom logs
      allow read, write: if isAuthenticated() && request.resource.data.userId == request.auth.uid;

      // Allow users to read their own symptom logs
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;

      // Allow doctors to read patient symptom logs
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';

      // Allow supervisors to read symptom logs of their linked patients
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor' &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        resource.data.userId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients;
    }

    // Reminders collection
    match /reminders/{reminderId} {
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Caregiver relationships
    match /caregivers/{relationshipId} {
      allow read: if isAuthenticated() &&
        (resource.data.patientId == request.auth.uid || resource.data.caregiverId == request.auth.uid);
      allow create: if isAuthenticated() &&
        (request.resource.data.patientId == request.auth.uid || request.resource.data.caregiverId == request.auth.uid);
      allow update: if isAuthenticated() &&
        (resource.data.patientId == request.auth.uid || resource.data.caregiverId == request.auth.uid);
      allow delete: if isAuthenticated() &&
        (resource.data.patientId == request.auth.uid || resource.data.caregiverId == request.auth.uid);
    }

    // Caregiver relationships managed by supervisors
    match /caregiverRelationships/{relationshipId} {
      // Allow supervisors to create, read, update, and delete caregiver relationships
      allow create, read, update, delete: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor';

      // Allow caregivers to read relationships they're part of
      allow read: if isAuthenticated() &&
        resource.data.caregiverId == request.auth.uid;

      // Allow patients to read relationships they're part of
      allow read: if isAuthenticated() &&
        resource.data.patientId == request.auth.uid;
    }

    // Vitals collection
    match /vitals/{vitalId} {
      // Allow users to read and write their own vitals
      allow read, write: if isAuthenticated() && request.resource.data.userId == request.auth.uid;

      // Allow users to read their own vitals
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;

      // Allow doctors to read patient vitals
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';

      // Allow caregivers to read and write vitals for their linked patients
      allow read, write: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'caregiver' &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        (
          // Check if the patient is in the caregiver's linkedPatients array
          resource.data.userId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients ||
          // For create operations, check the request resource
          request.resource.data.userId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients
        );

      // Allow supervisors to read vitals of their linked patients
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor' &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        resource.data.userId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients;
    }

    // Medications collection
    match /medications/{medicationId} {
      allow read: if isAuthenticated() && resource.data.patientId == request.auth.uid;
      allow create: if isAuthenticated() && request.resource.data.patientId == request.auth.uid;
      allow update: if isAuthenticated() && resource.data.patientId == request.auth.uid;
      allow delete: if isAuthenticated() && resource.data.patientId == request.auth.uid;

      // Allow doctors to read patient medications
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';

      // Allow supervisors to read medications of their linked patients
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor' &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        resource.data.patientId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients;
    }

    // Medication Reminders collection
    match /medicationReminders/{reminderId} {
      allow read: if isAuthenticated() && resource.data.patientId == request.auth.uid;
      allow create: if isAuthenticated() && request.resource.data.patientId == request.auth.uid;
      allow update: if isAuthenticated() && resource.data.patientId == request.auth.uid;
      allow delete: if isAuthenticated() && resource.data.patientId == request.auth.uid;

      // Allow doctors to read patient medication reminders
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';

      // Allow supervisors to read medication reminders of their linked patients
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'supervisor' &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        resource.data.patientId in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.linkedPatients;
    }

    // Prescriptions collection
    match /prescriptions/{prescriptionId} {
      // Allow doctors to create prescriptions
      allow create: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';

      // Allow doctors to read all prescriptions
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';

      // Allow doctors to update and delete their own prescriptions
      allow update, delete: if isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor' &&
        resource.data.doctorId == request.auth.uid;

      // Allow patients to read their own prescriptions
      allow read: if isAuthenticated() && resource.data.patientId == request.auth.uid;
    }

    // Default deny all
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
