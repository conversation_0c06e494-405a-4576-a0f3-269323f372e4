import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const LocationsMapButton = () => {
  const navigation = useNavigation();

  const handlePress = () => {
    navigation.navigate('MapScreen');
  };

  return (
    <TouchableOpacity
      style={styles.button}
      onPress={handlePress}
    >
      <View style={styles.buttonContent}>
        <Ionicons name="map" size={16} color="#fff" />
        <Text style={styles.buttonText}>My Locations</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#4DB6AC',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    maxWidth: 140, // Limit width to prevent overflow
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 4,
    fontSize: 12,
  },
});

export default LocationsMapButton;
