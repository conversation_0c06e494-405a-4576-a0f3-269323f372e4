import React, { useEffect, useRef, useContext } from 'react';
import { Alert, Platform } from 'react-native';
import { useNavigation, useNavigationState } from '@react-navigation/native';
import axios from 'axios';
import { API_URL } from '../../config/constants';
import { AuthContext } from '../../contexts/AuthContext';
import { initializeMessaging, registerDeviceForNotifications } from '../../config/messaging';

const NotificationHandler = () => {
  const { user, accessToken } = useContext(AuthContext);
  const navigation = useNavigation();
  const notificationListener = useRef();
  const responseListener = useRef();

  useEffect(() => {
    // Don't proceed if navigation or user context isn't ready
    if (!navigation || !user || !accessToken) return;

    // Setup Firebase messaging
    const setupNotifications = async () => {
      try {
        // Native platforms only
        if (Platform.OS !== 'android' && Platform.OS !== 'ios') return;

        // Initialize messaging using our configuration
        const { token, enabled } = await initializeMessaging();
        
        if (!enabled || !token) {
          console.log('Firebase messaging not enabled or token not available');
          return;
        }
        
        // Register the token with our server
        try {
          await axios.post(
            `${API_URL}/users/fcm-token`,
            { fcmToken: token },
            {
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken}`,
              },
            }
          );
          console.log('FCM token updated successfully');
        } catch (error) {
          console.error('Error updating FCM token:', error);
        }

        // Only try to use messaging if we're on a native platform
        try {
          // Dynamically import messaging to prevent errors when module is not available
          const messaging = require('@react-native-firebase/messaging').default;
          
          // Handle notifications when app is in foreground
          notificationListener.current = messaging().onMessage(async remoteMessage => {
            const { type, appointmentId, status } = remoteMessage.data || {};
            
            if (type === 'appointment_update') {
              Alert.alert(
                'Appointment Update',
                `Your appointment has been ${status}`,
                [
                  {
                    text: 'View',
                    onPress: () => handleAppointmentNavigation(appointmentId),
                  },
                  {
                    text: 'OK',
                    style: 'cancel',
                  },
                ]
              );
            } else if (type === 'appointment_request') {
              Alert.alert(
                'New Appointment Request',
                'A patient has requested an appointment',
                [
                  {
                    text: 'View',
                    onPress: () => handleAppointmentNavigation(appointmentId),
                  },
                  {
                    text: 'OK',
                    style: 'cancel',
                  },
                ]
              );
            } else if (type === 'appointment_cancelled') {
              Alert.alert(
                'Appointment Cancelled',
                'An appointment has been cancelled',
                [
                  {
                    text: 'View',
                    onPress: () => handleAppointmentNavigation(appointmentId),
                  },
                  {
                    text: 'OK',
                    style: 'cancel',
                  },
                ]
              );
            }
          });

          // Handle notification when app is in background
          responseListener.current = messaging().onNotificationOpenedApp(remoteMessage => {
            const { type, appointmentId } = remoteMessage.data || {};
            if (type && appointmentId) {
              handleAppointmentNavigation(appointmentId);
            }
          });

          // Check if app was opened from a notification
          messaging()
            .getInitialNotification()
            .then(remoteMessage => {
              if (remoteMessage) {
                const { type, appointmentId } = remoteMessage.data || {};
                if (type && appointmentId) {
                  handleAppointmentNavigation(appointmentId);
                }
              }
            });
        } catch (error) {
          console.log('Error setting up notification handlers:', error.message);
        }
      } catch (error) {
        console.log('Error setting up notifications:', error.message);
      }
    };

    setupNotifications();

    return () => {
      // Clean up listeners
      try {
        if (Platform.OS === 'android' || Platform.OS === 'ios') {
          try {
            const messaging = require('@react-native-firebase/messaging').default;
            
            if (notificationListener.current) {
              messaging().off('message', notificationListener.current);
            }
            if (responseListener.current) {
              messaging().off('notification_opened_app', responseListener.current);
            }
          } catch (error) {
            console.log('Messaging module not available for cleanup:', error.message);
          }
        }
      } catch (error) {
        console.log('Error cleaning up notification listeners:', error.message);
      }
    };
  }, [user, accessToken, navigation]);

  // Safe navigation function that checks if navigation is available
  const handleAppointmentNavigation = (appointmentId) => {
    if (!appointmentId || !user || !navigation) return;

    try {
      if (user.role === 'doctor') {
        navigation.navigate('DoctorDashboard', {
          screen: 'AppointmentManagement',
          params: { appointmentId },
        });
      } else if (user.role === 'patient') {
        navigation.navigate('PatientDashboard', {
          screen: 'AppointmentList',
          params: { appointmentId },
        });
      }
    } catch (error) {
      console.log('Navigation error:', error.message);
    }
  };

  return null;
};

export default NotificationHandler; 