import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { showMessage } from 'react-native-flash-message';
import SharedProfileForm from '../components/profile/SharedProfileForm';
import { COLORS } from '../config/theme';

const ProfileScreen = () => {
  const { user, updateUserProfile } = useAuth();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState({});

  useEffect(() => {
    if (user) {
      // Extract relevant user data to populate the form
      const { firstName, lastName, email, phone, address, city, dateOfBirth, gender, countryCode, dialCode } = user;
      setInitialData({
        firstName: firstName || '',
        lastName: lastName || '',
        email: email || '',
        phone: phone || '',
        address: address || '',
        city: city || '',
        dateOfBirth: dateOfBirth || '',
        gender: gender || '',
        countryCode: countryCode || 'US',
        dialCode: dialCode || '+1',
      });
    }
  }, [user]);

  const handleSubmit = async (formData) => {
    try {
      setLoading(true);
      
      // Update the user profile
      await updateUserProfile({
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString(),
      });

      // Show success message
      showMessage({
        message: 'Success',
        description: 'Your profile has been updated successfully',
        type: 'success',
        backgroundColor: COLORS.success,
      });

      // Navigate back to dashboard - REMOVED duplicate navigation here
      // The setTimeout below will handle navigation after showing the message
      
      // Wait for the message to be visible before navigating
      setTimeout(() => {
        navigation.navigate('Dashboard');
      }, 1000);
    } catch (error) {
      console.error('Error updating profile:', error);
      showMessage({
        message: 'Error',
        description: error.message || 'Failed to update profile. Please try again.',
        type: 'danger',
        backgroundColor: COLORS.error,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SharedProfileForm
        initialData={initialData}
        onSubmit={handleSubmit}
        loading={loading}
        buttonText="Update Profile"
        requiredFields={['firstName', 'lastName', 'email']}
        nonEditableFields={['firstName', 'lastName', 'email']} // Add this prop
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
});

export default ProfileScreen;