import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { Surface } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { getThemeForRole, COLORS } from '../../config/theme';
import { showMessage } from 'react-native-flash-message';
import Icon from 'react-native-vector-icons/FontAwesome';
import { getAuth, EmailAuthProvider, reauthenticateWithCredential, updatePassword } from 'firebase/auth';

const ChangePassword = ({ navigation }) => {
  const { user, changePassword } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');
  const primaryColor = theme.colors.primary;

  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.currentPassword) {
      showMessage({
        message: 'Error',
        description: 'Please enter your current password',
        type: 'danger',
      });
      return false;
    }

    if (!formData.newPassword) {
      showMessage({
        message: 'Error',
        description: 'Please enter a new password',
        type: 'danger',
      });
      return false;
    }

    if (formData.newPassword.length < 6) {
      showMessage({
        message: 'Error',
        description: 'New password must be at least 6 characters long',
        type: 'danger',
      });
      return false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      showMessage({
        message: 'Error',
        description: 'New password and confirm password do not match',
        type: 'danger',
      });
      return false;
    }

    if (formData.currentPassword === formData.newPassword) {
      showMessage({
        message: 'Error',
        description: 'New password must be different from current password',
        type: 'danger',
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // Get the current Firebase auth instance
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        throw new Error('You must be logged in to change your password.');
      }

      console.log("Starting password change process for user:", user.email);

      try {
        // Create credential
        const credential = EmailAuthProvider.credential(
          user.email,
          formData.currentPassword
        );

        console.log("Credential created, attempting reauthentication");

        // Reauthenticate
        await reauthenticateWithCredential(user, credential);

        console.log("Reauthentication successful, updating password");

        // Update password
        await updatePassword(user, formData.newPassword);

        console.log("Password updated successfully");

        // Show success message
        Alert.alert(
          'Success',
          'Your password has been changed successfully',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } catch (authError) {
        console.error("Authentication error:", authError);

        if (authError.code === 'auth/wrong-password') {
          throw new Error('Current password is incorrect.');
        } else {
          throw new Error('Authentication failed: ' + (authError.message || 'Please try again.'));
        }
      }
    } catch (error) {
      showMessage({
        message: 'Error',
        description: error.message || 'Failed to change password. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Surface style={styles.content} elevation={0}>
        <View style={styles.headerContainer}>
          <Ionicons name="lock-closed" size={40} color={primaryColor} />
          <Text style={[styles.headerText, { color: primaryColor }]}>Change Password</Text>
          <Text style={styles.subHeaderText}>
            Update your password to keep your account secure
          </Text>
        </View>

        <View style={styles.formContainer}>
          {/* Current Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Current Password</Text>
            <View style={styles.inputBox}>
              <Icon name="lock" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="Enter current password"
                placeholderTextColor="#999"
                secureTextEntry={!showCurrentPassword}
                value={formData.currentPassword}
                onChangeText={(text) => handleChange('currentPassword', text)}
              />
              <TouchableOpacity
                style={styles.passwordIcon}
                onPress={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                <Icon
                  name={showCurrentPassword ? "eye-slash" : "eye"}
                  size={20}
                  color="rgba(16, 107, 0, 1)"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* New Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>New Password</Text>
            <View style={styles.inputBox}>
              <Icon name="lock" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="Enter new password"
                placeholderTextColor="#999"
                secureTextEntry={!showNewPassword}
                value={formData.newPassword}
                onChangeText={(text) => handleChange('newPassword', text)}
              />
              <TouchableOpacity
                style={styles.passwordIcon}
                onPress={() => setShowNewPassword(!showNewPassword)}
              >
                <Icon
                  name={showNewPassword ? "eye-slash" : "eye"}
                  size={20}
                  color="rgba(16, 107, 0, 1)"
                />
              </TouchableOpacity>
            </View>
            <Text style={styles.passwordHint}>Password must be at least 6 characters long</Text>
          </View>

          {/* Confirm Password */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Confirm New Password</Text>
            <View style={styles.inputBox}>
              <Icon name="lock" size={20} color="rgba(16, 107, 0, 1)" style={styles.icon} />
              <TextInput
                style={styles.input}
                placeholder="Confirm new password"
                placeholderTextColor="#999"
                secureTextEntry={!showConfirmPassword}
                value={formData.confirmPassword}
                onChangeText={(text) => handleChange('confirmPassword', text)}
              />
              <TouchableOpacity
                style={styles.passwordIcon}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Icon
                  name={showConfirmPassword ? "eye-slash" : "eye"}
                  size={20}
                  color="rgba(16, 107, 0, 1)"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.button, loading && styles.buttonDisabled, { backgroundColor: primaryColor }]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.buttonText}>Update Password</Text>
            )}
          </TouchableOpacity>

          {/* Cancel Button */}
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
            disabled={loading}
          >
            <Text style={[styles.cancelButtonText, { color: primaryColor }]}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </Surface>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    padding: 16,
    backgroundColor: 'transparent',
  },
  headerContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  headerText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  subHeaderText: {
    fontSize: 16,
    color: COLORS.textMedium,
    textAlign: 'center',
  },
  formContainer: {
    marginTop: 16,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: COLORS.textDark,
  },
  inputBox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
  },
  icon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    color: COLORS.textDark,
  },
  passwordIcon: {
    padding: 10,
  },
  passwordHint: {
    fontSize: 12,
    color: COLORS.textMedium,
    marginTop: 4,
  },
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ChangePassword;
