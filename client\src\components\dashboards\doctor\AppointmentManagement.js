import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Portal,
  Modal,
  List,
  Divider,
  Chip,
  Pagination,
} from 'react-native-paper';
import { format } from 'date-fns';
import { firebaseAppointmentsService } from '../../../services/firebaseAppointmentsService';

const ITEMS_PER_PAGE = 10;

const AppointmentManagement = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  useEffect(() => {
    fetchAppointments();
  }, [page]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);

      // Use Firebase service to get doctor appointments
      const options = {
        includePatientDetails: true,
        dateOrder: 'desc',  // Most recent appointments first
        timeOrder: 'asc'    // Earlier times first for same day
      };

      // Get appointments from Firebase
      const fetchedAppointments = await firebaseAppointmentsService.getDoctorAppointments(options);
      console.log(`Fetched ${fetchedAppointments.length} total appointments`);

      // Apply pagination manually
      const startIndex = page * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const paginatedAppointments = fetchedAppointments.slice(startIndex, endIndex);

      setAppointments(paginatedAppointments);

      // Calculate total pages
      const totalItems = fetchedAppointments.length;
      setTotalPages(Math.ceil(totalItems / ITEMS_PER_PAGE));

      console.log(`Showing appointments ${startIndex+1} to ${Math.min(endIndex, totalItems)} of ${totalItems}`);
    } catch (error) {
      console.error('Error fetching appointments from Firebase:', error);
      Alert.alert('Error', 'Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchAppointments();
    setRefreshing(false);
  };

  const handleUpdateStatus = async (appointmentId, newStatus) => {
    try {
      setLoading(true);

      // Update appointment status in Firebase
      await firebaseAppointmentsService.updateAppointment(appointmentId, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });

      // Refresh the appointments list
      await fetchAppointments();

      Alert.alert('Success', 'Appointment status updated successfully');
    } catch (error) {
      console.error('Error updating appointment status in Firebase:', error);
      Alert.alert('Error', 'Failed to update appointment status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA500';
      case 'confirmed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const renderAppointmentCard = (appointment) => (
    <Card
      key={appointment.id}
      style={styles.card}
      onPress={() => {
        setSelectedAppointment(appointment);
        setShowDetailsModal(true);
      }}
    >
      <Card.Content>
        <View style={styles.cardHeader}>
          <Title>{format(new Date(appointment.date), 'MMMM dd, yyyy')}</Title>
          <Chip
            style={[styles.statusChip, { backgroundColor: getStatusColor(appointment.status) }]}
          >
            {appointment.status.toUpperCase()}
          </Chip>
        </View>
        <Paragraph>Time: {appointment.time || 'Not specified'}</Paragraph>
        <Paragraph>Type: {appointment.type || 'In-person'}</Paragraph>
        <Paragraph>Patient: {appointment.patient?.name || appointment.patientId || 'Unknown Patient'}</Paragraph>
      </Card.Content>
    </Card>
  );

  const renderDetailsModal = () => (
    <Portal>
      <Modal
        visible={showDetailsModal}
        onDismiss={() => setShowDetailsModal(false)}
        contentContainerStyle={styles.modal}
      >
        {selectedAppointment && (
          <ScrollView>
            <Title>Appointment Details</Title>
            <Divider style={styles.divider} />

            <List.Item
              title="Date"
              description={format(new Date(selectedAppointment.date), 'MMMM dd, yyyy')}
            />
            <List.Item
              title="Time"
              description={selectedAppointment.time}
            />
            <List.Item
              title="Type"
              description={selectedAppointment.type}
            />
            <List.Item
              title="Patient"
              description={selectedAppointment.patient?.name || selectedAppointment.patientId || 'Unknown Patient'}
            />
            <List.Item
              title="Reason"
              description={selectedAppointment.reason || 'Not specified'}
            />

            <Divider style={styles.divider} />

            <View style={styles.actionButtons}>
              {selectedAppointment.status === 'pending' && (
                <>
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateStatus(selectedAppointment.id, 'confirmed')}
                    style={[styles.actionButton, styles.confirmButton]}
                  >
                    Confirm
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateStatus(selectedAppointment.id, 'cancelled')}
                    style={[styles.actionButton, styles.cancelButton]}
                  >
                    Cancel
                  </Button>
                </>
              )}
              {selectedAppointment.status === 'confirmed' && (
                <Button
                  mode="contained"
                  onPress={() => handleUpdateStatus(selectedAppointment.id, 'completed')}
                  style={[styles.actionButton, styles.completeButton]}
                >
                  Mark as Completed
                </Button>
              )}
            </View>
          </ScrollView>
        )}
      </Modal>
    </Portal>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {appointments.length === 0 ? (
          <Text style={styles.noAppointments}>No appointments found</Text>
        ) : (
          <>
            {appointments.map(renderAppointmentCard)}
            <Pagination
              page={page}
              numberOfPages={totalPages}
              onPageChange={setPage}
              style={styles.pagination}
            />
          </>
        )}
      </ScrollView>
      {renderDetailsModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusChip: {
    height: 24,
  },
  modal: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  divider: {
    marginVertical: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  completeButton: {
    backgroundColor: '#2196F3',
  },
  noAppointments: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  },
  pagination: {
    marginVertical: 16,
  },
});

export default AppointmentManagement;