const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');

// Initialize Firestore
const db = admin.firestore();

// Create a chat with specific doctor and patient
router.post('/create-chat', verifyToken, async (req, res) => {
    try {
        const { doctorId, patientId, chatId } = req.body;
        const userId = req.user.uid;

        // Validate required fields
        if (!doctorId || !patientId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Verify the current user is the doctor
        if (userId !== doctorId) {
            return res.status(403).json({ error: 'Not authorized' });
        }

        // Create the participants array
        const participantIds = [doctorId, patientId];

        // Create chat room data
        const chatRoom = {
            participantIds,
            type: 'direct', // Direct chat between doctor and patient
            createdBy: userId,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            lastMessage: null,
            lastMessageAt: null,
            // Store the custom chatId if provided
            customChatId: chatId || null
        };

        // Add to Firestore
        const chatRef = await db.collection('chatRooms').add(chatRoom);

        res.status(201).json({
            success: true,
            chatId: chatRef.id
        });
    } catch (error) {
        console.error('Error creating chat:', error);
        res.status(500).json({ error: 'Failed to create chat' });
    }
});

// Create a chat room
router.post('/chat', verifyToken, async (req, res) => {
    try {
        const { participantIds, type } = req.body;
        const userId = req.user.uid;

        // Ensure the current user is included in participants
        if (!participantIds.includes(userId)) {
            participantIds.push(userId);
        }

        const chatRoom = {
            participantIds,
            type, // 'direct' or 'group'
            createdBy: userId,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            lastMessage: null,
            lastMessageAt: null
        };

        const chatRef = await db.collection('chatRooms').add(chatRoom);

        res.status(201).json({
            success: true,
            chatId: chatRef.id
        });
    } catch (error) {
        console.error('Error creating chat room:', error);
        res.status(500).json({ error: 'Failed to create chat room' });
    }
});

// Send a message
router.post('/chat/:chatId/message', verifyToken, async (req, res) => {
    try {
        const { chatId } = req.params;
        const { content, type } = req.body;
        const userId = req.user.uid;

        // Verify chat room exists and user is a participant
        const chatRoom = await db.collection('chatRooms').doc(chatId).get();
        if (!chatRoom.exists) {
            return res.status(404).json({ error: 'Chat room not found' });
        }

        const chatData = chatRoom.data();
        if (!chatData.participantIds.includes(userId)) {
            return res.status(403).json({ error: 'Not authorized' });
        }

        const message = {
            chatId,
            senderId: userId,
            content,
            type, // 'text', 'image', etc.
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            read: false
        };

        // Add message to messages collection
        const messageRef = await db.collection('messages').add(message);

        // Update chat room with last message
        await chatRoom.ref.update({
            lastMessage: content,
            lastMessageAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.status(201).json({
            success: true,
            messageId: messageRef.id
        });
    } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({ error: 'Failed to send message' });
    }
});

// Get chat messages
router.get('/chat/:chatId/messages', verifyToken, async (req, res) => {
    try {
        const { chatId } = req.params;
        const userId = req.user.uid;
        const { limit = 50, before } = req.query;

        // Verify chat room exists and user is a participant
        const chatRoom = await db.collection('chatRooms').doc(chatId).get();
        if (!chatRoom.exists) {
            return res.status(404).json({ error: 'Chat room not found' });
        }

        if (!chatRoom.data().participantIds.includes(userId)) {
            return res.status(403).json({ error: 'Not authorized' });
        }

        let query = db.collection('messages')
            .where('chatId', '==', chatId)
            .orderBy('timestamp', 'desc')
            .limit(parseInt(limit));

        if (before) {
            const beforeDoc = await db.collection('messages').doc(before).get();
            query = query.startAfter(beforeDoc);
        }

        const messagesSnapshot = await query.get();
        const messages = [];
        messagesSnapshot.forEach(doc => {
            messages.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(messages);
    } catch (error) {
        console.error('Error fetching messages:', error);
        res.status(500).json({ error: 'Failed to fetch messages' });
    }
});

// Get user's chat rooms
router.get('/chats', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;

        const chatsSnapshot = await db.collection('chatRooms')
            .where('participantIds', 'array-contains', userId)
            .orderBy('lastMessageAt', 'desc')
            .get();

        const chats = [];
        chatsSnapshot.forEach(doc => {
            chats.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(chats);
    } catch (error) {
        console.error('Error fetching chats:', error);
        res.status(500).json({ error: 'Failed to fetch chats' });
    }
});

module.exports = router;
