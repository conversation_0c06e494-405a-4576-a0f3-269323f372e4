import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../config/theme';

const NotificationItem = ({ notification, onPress, onDismiss }) => {
  const [fadeAnim] = useState(new Animated.Value(1));

  const handleDismiss = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onDismiss(notification.id);
    });
  };

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'profile':
        return 'person';
      case 'message':
        return 'mail';
      case 'appointment':
        return 'calendar';
      case 'medication':
        return 'medkit';
      case 'alert':
        return 'alert-circle';
      default:
        return 'notifications';
    }
  };

  return (
    <Animated.View style={[styles.notificationItem, { opacity: fadeAnim }]}>
      <TouchableOpacity 
        style={styles.notificationContent}
        onPress={() => onPress(notification)}
        activeOpacity={0.7}
      >
        <View style={styles.notificationInner}>
          <View style={[styles.iconContainer, { backgroundColor: notification.color || COLORS.primary }]}>
            <Ionicons name={getIcon()} size={20} color="#fff" />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.notificationTitle}>{notification.title}</Text>
            <Text style={styles.notificationMessage}>{notification.message}</Text>
            {notification.time && (
              <Text style={styles.notificationTime}>{notification.time}</Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleDismiss} style={styles.dismissButton}>
        <Ionicons name="close" size={18} color={COLORS.textMedium} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const NotificationsList = ({ notifications = [], onPress, onDismiss, onDismissAll }) => {
  if (notifications.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Notifications</Text>
        {notifications.length > 0 && (
          <TouchableOpacity onPress={onDismissAll} style={styles.clearAllButton}>
            <Text style={styles.clearAllText}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <NotificationItem 
            notification={item} 
            onPress={onPress}
            onDismiss={onDismiss} 
          />
        )}
        style={styles.list}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 0, // Remove border radius
    margin: 0, // Remove all margins
    width: '100%', // Ensure full width
    shadowColor: 'transparent', // Remove shadow
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0, // Remove elevation
    overflow: 'visible', // Change to visible
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.textDark,
  },
  clearAllButton: {
    padding: 4,
  },
  clearAllText: {
    fontSize: 14,
    color: COLORS.primary,
  },
  list: {
    maxHeight: '100%',
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  notificationContent: {
    flex: 1,
  },
  notificationInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.textDark,
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 13,
    color: COLORS.textMedium,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: COLORS.textLight,
  },
  dismissButton: {
    padding: 4,
    justifyContent: 'center',
  },
});

export default NotificationsList;
