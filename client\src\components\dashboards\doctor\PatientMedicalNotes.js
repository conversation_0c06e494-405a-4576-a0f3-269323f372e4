import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  FlatList,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ROLE_COLORS } from '../../../config/theme';
import { localMedicalNotesService } from '../../../services/localMedicalNotesService';

const PatientMedicalNotes = ({ patientId, patientName, noteType }) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [notes, setNotes] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedNote, setSelectedNote] = useState(null);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientNotes();
  }, [patientId, noteType]);

  const fetchPatientNotes = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch notes for the selected patient
      const notesData = await localMedicalNotesService.getPatientMedicalNotes(patientId);

      // Filter notes by type if specified
      const filteredNotes = noteType
        ? notesData.filter(note => note.type === noteType)
        : notesData;

      setNotes(filteredNotes || []);
    } catch (error) {
      console.error('Error fetching patient medical notes:', error);
    } finally {
      setLoading(false);
    }
  };

  const openNoteDetails = (note) => {
    setSelectedNote(note);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Render the notes list
  const renderNotesList = () => {
    if (notes.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text-outline" size={48} color={doctorColors.primary} />
          <Text style={styles.emptyText}>No medical notes found for this patient</Text>
        </View>
      );
    }

    return (
      <FlatList
        data={notes}
        keyExtractor={(item) => item.id}
        nestedScrollEnabled={true}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.noteCard}
            onPress={() => openNoteDetails(item)}
          >
            <View style={styles.noteHeader}>
              <View style={styles.categoryContainer}>
                <Ionicons
                  name={item.type === 'vitals' ? 'pulse' : 'document-text'}
                  size={16}
                  color={doctorColors.primary}
                />
                <Text style={styles.categoryText}>{item.category}</Text>
              </View>
              <Text style={styles.dateText}>{formatDate(item.timestamp)}</Text>
            </View>

            <Text style={styles.noteContent} numberOfLines={2}>
              {item.content}
            </Text>

            <Ionicons name="chevron-forward" size={24} color="#ccc" style={styles.chevron} />
          </TouchableOpacity>
        )}
      />
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedNote) return null;

    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Medical Note Details</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent} nestedScrollEnabled={true}>
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>
                  {selectedNote.category}
                </Text>
                <Text style={styles.modalDateText}>
                  {formatDate(selectedNote.timestamp)}
                </Text>
              </View>

              <View style={styles.modalSection}>
                <Text style={styles.modalContentText}>
                  {selectedNote.content}
                </Text>
              </View>

              <View style={styles.modalMetadata}>
                <Text style={styles.modalMetadataText}>
                  Note ID: {selectedNote.id}
                </Text>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {patientName ? `${patientName}'s Medical Notes` : 'Patient Medical Notes'}
        </Text>
        <Text style={styles.headerSubtitle}>
          {noteType === 'vitals' ? 'Notes for Vital Signs' :
           noteType === 'symptoms' ? 'Notes for Symptoms' :
           'All Medical Notes'}
        </Text>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
          <Text style={styles.loadingText}>Loading medical notes...</Text>
        </View>
      ) : (
        <View style={styles.content}>
          {renderNotesList()}
        </View>
      )}

      {renderDetailModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  noteCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: ROLE_COLORS.doctor.primary,
    marginLeft: 4,
  },
  dateText: {
    fontSize: 12,
    color: '#666',
  },
  noteContent: {
    fontSize: 14,
    color: '#333',
    marginRight: 20,
  },
  chevron: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    backgroundColor: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 6,
  },
  modalContent: {
    padding: 16,
  },
  modalSection: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  modalDateText: {
    fontSize: 14,
    color: '#666',
  },
  modalContentText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  modalMetadata: {
    padding: 8,
  },
  modalMetadataText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
});

export default PatientMedicalNotes;
