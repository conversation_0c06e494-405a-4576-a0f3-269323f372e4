import React, { useState, useEffect, useRef } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, View, TouchableOpacity, Image, StatusBar, ImageBackground, LogBox, Dimensions, Animated } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Bell, Activity, Mic, MapPin, Video, Camera, Shield, BarChart2, Heart, Users, Zap, Star, Smartphone, Calendar, MessageCircle, Mail, Phone, HelpCircle, ExternalLink, Facebook, Twitter, Instagram, Globe } from 'react-native-feather';
import { LinearGradient } from 'expo-linear-gradient';
import SplashScreen from '../splashScreen/SplashScreen';

// Get screen dimensions for responsive design
const { width: screenWidth } = Dimensions.get('window');

// Define responsive breakpoints
const isSmallDevice = screenWidth < 375;
const isMediumDevice = screenWidth >= 375 && screenWidth < 768;
const isLargeDevice = screenWidth >= 768;

// Create responsive sizing utility
const getResponsiveSize = (small, medium, large) => {
  if (isSmallDevice) return small;
  if (isMediumDevice) return medium;
  return large;
};

// Ignore specific warnings that might cause issues
LogBox.ignoreLogs([
  'ViewPropTypes will be removed',
  'ColorPropType will be removed',
]);

const features = [
  {
    id: 1,
    title: "Reminders",
    description: "Medication and appointment reminders",
    icon: Bell,
    color: "rgba(255, 100, 50, 1)", // Orange plus vibrant
  },
  {
    id: 2,
    title: "Symptom Tracking",
    description: "Record your daily symptoms",
    icon: Activity,
    color: "rgba(0, 200, 150, 1)", // Turquoise plus vibrant
  },
  {
    id: 3,
    title: "Voice Commands",
    description: "Interact with the app by voice",
    icon: Mic,
    color: "rgba(100, 120, 255, 1)", // Bleu plus vibrant
  },
  {
    id: 4,
    title: "GPS Guidance",
    description: "Navigation assistance and alerts",
    icon: MapPin,
    color: "rgba(255, 170, 0, 1)", // Jaune plus vibrant
  },
  {
    id: 5,
    title: "Communication",
    description: "Voice and video chat with your doctor",
    icon: Video,
    color: "rgba(130, 210, 50, 1)", // Vert plus vibrant
  },
  {
    id: 6,
    title: "Recognition",
    description: "Identifies objects and people",
    icon: Camera,
    color: "rgba(200, 80, 220, 1)", // Violet plus vibrant
  },
  {
    id: 7,
    title: "Safety Alerts",
    description: "Fall detection and unusual behavior alerts",
    icon: Shield,
    color: "rgba(255, 60, 120, 1)", // Rose plus vibrant
  },
  {
    id: 8,
    title: "Dashboard",
    description: "Track daily routines",
    icon: BarChart2,
    color: "rgba(30, 180, 255, 1)", // Bleu ciel plus vibrant
  },
];

// Données pour la section "How It Works"
const howItWorksFeatures = [
  {
    id: 1,
    title: "Profile Setup",
    description: "Create your account, set up your profile with medical information, and get your personal QR code for easy identification.",
    icon: Smartphone,
    color: "rgba(255, 100, 50, 0.9)",
    borderColor: "rgba(255, 100, 50, 1)",
    gradientStart: "#FFF3E0",
    gradientEnd: "#FFE0B2"
  },
  {
    id: 2,
    title: "Health Tracking",
    description: "Record vital signs with voice commands, track symptoms, and monitor your health metrics with detailed history and trends.",
    icon: Activity,
    color: "rgba(0, 200, 150, 0.9)",
    borderColor: "rgba(0, 200, 150, 1)",
    gradientStart: "#E0F2F1",
    gradientEnd: "#B2DFDB"
  },
  {
    id: 3,
    title: "Medication Reminders",
    description: "Set up medication schedules with custom frequencies, receive timely reminders, and track your medication adherence.",
    icon: Bell,
    color: "rgba(255, 170, 0, 0.9)",
    borderColor: "rgba(255, 170, 0, 1)",
    gradientStart: "#FFF8E1",
    gradientEnd: "#FFECB3"
  },
  {
    id: 4,
    title: "Appointments",
    description: "Request appointments with your doctors, reschedule when needed, and receive notifications for upcoming visits.",
    icon: Calendar,
    color: "rgba(100, 120, 255, 0.9)",
    borderColor: "rgba(100, 120, 255, 1)",
    gradientStart: "#E8EAF6",
    gradientEnd: "#C5CAE9"
  },
  {
    id: 5,
    title: "Doctor Communication",
    description: "Connect with your healthcare team through secure messaging and video consultations for remote care.",
    icon: MessageCircle,
    color: "rgba(170, 86, 255, 0.9)",
    borderColor: "rgba(170, 86, 255, 1)",
    gradientStart: "#F3E5F5",
    gradientEnd: "#E1BEE7"
  },
  {
    id: 6,
    title: "Location & Safety",
    description: "Use GPS guidance for navigation, set up safety alerts for falls or unusual behavior, and manage your important locations.",
    icon: MapPin,
    color: "rgba(16, 107, 0, 0.9)",
    borderColor: "rgba(16, 107, 0, 1)",
    gradientStart: "#E8F5E9",
    gradientEnd: "#C8E6C9"
  },
  {
    id: 7,
    title: "Voice Commands",
    description: "Navigate the app, record vitals, add medications, and perform other tasks using simple voice commands for hands-free operation.",
    icon: Mic,
    color: "rgba(0, 169, 255, 0.9)",
    borderColor: "rgba(0, 169, 255, 1)",
    gradientStart: "#E1F5FE",
    gradientEnd: "#B3E5FC"
  },
  {
    id: 8,
    title: "Recognition",
    description: "Use camera recognition to identify objects and people, scan prescriptions, and get assistance with daily activities.",
    icon: Camera,
    color: "rgba(200, 80, 220, 0.9)",
    borderColor: "rgba(200, 80, 220, 1)",
    gradientStart: "#F3E5F5",
    gradientEnd: "#E1BEE7"
  }
];

export default function HomeScreen() {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);

  // Référence pour le ScrollView des cartes "How It Works"
  const howItWorksScrollViewRef = useRef(null);

  // Références pour les animations des cartes de fonctionnalités
  const featureCardScales = features.map(() => useRef(new Animated.Value(1)).current);
  const howItWorksCardScales = howItWorksFeatures.map(() => useRef(new Animated.Value(1)).current);

  // Fonction pour faire défiler vers une carte spécifique
  const scrollToCard = (index) => {
    if (howItWorksScrollViewRef.current) {
      const cardWidth = isSmallDevice ? screenWidth * 0.8 :
                       isMediumDevice ? screenWidth * 0.7 :
                       screenWidth * 0.31;
      const offset = index * (cardWidth + 16); // 16 est la marge entre les cartes
      howItWorksScrollViewRef.current.scrollTo({ x: offset, animated: true });
      setCurrentCardIndex(index);
    }
  };

  // Fonctions pour animer les cartes lors des interactions
  const handlePressIn = (index, isHowItWorks = false) => {
    const targetArray = isHowItWorks ? howItWorksCardScales : featureCardScales;
    Animated.spring(targetArray[index], {
      toValue: 0.95,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = (index, isHowItWorks = false) => {
    const targetArray = isHowItWorks ? howItWorksCardScales : featureCardScales;
    Animated.spring(targetArray[index], {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  // Add useEffect to track state transitions
  useEffect(() => {
    console.log('HomeScreen mounted');

    // Safety timeout to prevent infinite loading
    const safetyTimer = setTimeout(() => {
      if (isLoading) {
        console.log('Safety timeout triggered - forcing load completion');
        setIsLoading(false);
      }
    }, 5000); // Force completion after 5 seconds if splash screen gets stuck

    return () => {
      console.log('HomeScreen unmounting');
      clearTimeout(safetyTimer);
    };
  }, [isLoading]);

  const handleSplashFinish = () => {
    console.log('Splash screen finished, transitioning to main content');
    try {
      // Small delay to ensure smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 100);
    } catch (error) {
      console.error('Error during splash transition:', error);
      setIsError(true);
      setIsLoading(false);
    }
  };

  // Handle error state
  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Something went wrong loading the app.</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setIsError(false);
            setIsLoading(true);
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isLoading) {
    return <SplashScreen onFinish={handleSplashFinish} />;
  }

  return (
    <ImageBackground
      source={require('../../../assets/Backgrounds/pexels-pixabay-40568.jpg')}
      style={styles.wrapper}
      imageStyle={{ opacity: 0.9 }}
    >
      <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />


        <View style={styles.header}>
          <View style={styles.applogo}>
            <Image
              source={require('../../../assets/LogoNeuroCare/Neuro_Care_rounded_3.png')}
              style={styles.logo}
            />
            <View style={styles.appNameText}>
              <Text style={styles.appName}>Neuro Care</Text>
              <Text style={styles.tagline}>Your neurological care companion</Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* 1. WELCOME SECTION */}
          <View style={styles.welcomeCard}>
            <Image
              source={require('../../../assets/LogoNeuroCare/Neuro_Care_rounded_3.png')}
              style={styles.welcomeImage}
            />
            <Text style={styles.welcomeTitle}>Welcome</Text>
            <Text style={styles.welcomeText}>
              🧠 Neuro Care helps you manage your daily life with simplicity and security.
            </Text>
            <View style={styles.welcomeButtonsContainer}>
              <TouchableOpacity
                style={styles.getStartedButton}
                onPress={() => navigation.navigate('Signup')}
              >
                <Text style={styles.getStartedButtonText}>Get started</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.getAppButton}
                onPress={() => alert('App download link will be available soon!')}
              >
                <Text style={styles.getAppButtonText}>Learn more</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* 2. HOW IT WORKS SECTION */}
          <Text style={styles.sectionTitle}>How It Works</Text>
          <View style={styles.howItWorksSection}>
            <View style={styles.howItWorksHeader}>
              <Text style={styles.howItWorksSubtitle}>Explore all the features Neuro Care offers to manage your health</Text>
            </View>

            <View style={styles.paginationIndicator}>
              {howItWorksFeatures.map((_, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.paginationDotContainer}
                  onPress={() => scrollToCard(index)}
                >
                  <View
                    style={[
                      styles.paginationDot,
                      currentCardIndex === index && styles.paginationDotActive,
                      { backgroundColor: currentCardIndex === index ? '#FFFFFF' : 'rgba(255, 255, 255, 0.4)' }
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </View>

            <ScrollView
              ref={howItWorksScrollViewRef}
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={[
                styles.howItWorksContainer,
                { paddingRight: 20 }
              ]}
              onMomentumScrollEnd={(event) => {
                const cardWidth = isSmallDevice ? screenWidth * 0.8 :
                                 isMediumDevice ? screenWidth * 0.7 :
                                 screenWidth * 0.31;
                const offsetX = event.nativeEvent.contentOffset.x;
                const index = Math.round(offsetX / (cardWidth + 16));
                setCurrentCardIndex(index);
              }}
              pagingEnabled={false}
              decelerationRate="fast"
            >
            {howItWorksFeatures.map((feature, index) => (
              <Animated.View
                key={feature.id}
                style={[
                  styles.stepCard,
                  {
                    width: isSmallDevice ? screenWidth * 0.8 :
                           isMediumDevice ? screenWidth * 0.7 :
                           screenWidth * 0.31,
                    marginRight: 16,
                    marginLeft: index === 0 ? 16 : 0,
                    borderLeftWidth: 4,
                    borderLeftColor: feature.borderColor,
                    backgroundColor: '#FFFFFF',
                    transform: [{ scale: howItWorksCardScales[index] }],
                  }
                ]}
              >
                <View style={styles.stepCardGradient}>
                  <LinearGradient
                    colors={[feature.gradientStart, feature.gradientEnd]}
                    style={styles.cardGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  />
                </View>

                <View style={[styles.stepIconContainer, { backgroundColor: feature.color }]}>
                  <feature.icon
                    width={getResponsiveSize(22, 24, 28)}
                    height={getResponsiveSize(22, 24, 28)}
                    color="#FFFFFF"
                  />
                </View>

                <TouchableOpacity
                  style={styles.stepCardContent}
                  activeOpacity={0.8}
                  onPress={() => alert(`Learn more about ${feature.title}`)}
                  onPressIn={() => handlePressIn(index, true)}
                  onPressOut={() => handlePressOut(index, true)}
                >
                  <Text style={styles.stepTitle}>{feature.title}</Text>
                  <Text style={styles.stepDescription}>{feature.description}</Text>
                </TouchableOpacity>
              </Animated.View>
            ))}
            </ScrollView>
          </View>

          {/* 3. OUR FEATURES SECTION */}
          <Text style={styles.sectionTitle}>Our Features</Text>
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <Animated.View
                key={feature.id}
                style={[
                  styles.featureCardContainer,
                  {
                    transform: [{ scale: featureCardScales[index] }],
                    width: isSmallDevice ? '100%' : isMediumDevice ? '47%' : '30%',
                  }
                ]}
              >
                <TouchableOpacity
                  style={styles.featureCard}
                  activeOpacity={0.7}
                  onPress={() => alert(`Learn more about ${feature.title}`)}
                  onPressIn={() => handlePressIn(index)}
                  onPressOut={() => handlePressOut(index)}
                >
                  <View style={{ alignItems: 'center' }}>
                    <View
                      style={[
                        styles.iconContainer,
                        { backgroundColor: feature.color }
                      ]}
                    >
                      <feature.icon
                        width={getResponsiveSize(20, 24, 28)}
                        height={getResponsiveSize(20, 24, 28)}
                        color="#FFFFFF"
                      />
                    </View>
                    <Text style={styles.featureTitle}>{feature.title}</Text>
                    <Text style={styles.featureDescription}>{feature.description}</Text>
                  </View>

                  <View style={{ alignItems: 'center' }}>
                    <TouchableOpacity
                      style={[styles.learnMoreButton, { borderColor: feature.color }]}
                      onPress={() => alert(`Learn more about ${feature.title}`)}
                      activeOpacity={0.6}
                    >
                      <Text style={[styles.learnMoreButtonText, { color: feature.color }]}>Learn More</Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>

          {/* 4. ABOUT US SECTION */}
          <Text style={styles.sectionTitle}>About Us</Text>
          <View style={styles.aboutUsCard}>
            <View style={styles.aboutUsSection}>
              <View style={[styles.aboutUsIcon, { backgroundColor: 'rgba(255, 100, 50, 1)' }]}>
                <Heart width={24} height={24} color="#FFFFFF" />
              </View>
              <View style={styles.aboutUsContent}>
                <Text style={styles.aboutUsTitle}>Our Mission</Text>
                <Text style={styles.aboutUsText}>
                  Neuro Care is dedicated to improving the quality of life for individuals with neurological conditions through innovative technology solutions.
                </Text>
              </View>
            </View>

            <View style={styles.aboutUsSection}>
              <View style={[styles.aboutUsIcon, { backgroundColor: 'rgba(0, 200, 150, 1)' }]}>
                <Users width={24} height={24} color="#FFFFFF" />
              </View>
              <View style={styles.aboutUsContent}>
                <Text style={styles.aboutUsTitle}>Who We Are</Text>
                <Text style={styles.aboutUsText}>
                  We are a team of healthcare professionals, engineers, and designers passionate about creating accessible tools that empower patients and their caregivers.
                </Text>
              </View>
            </View>

            <View style={styles.aboutUsSection}>
              <View style={[styles.aboutUsIcon, { backgroundColor: 'rgba(100, 120, 255, 1)' }]}>
                <Zap width={24} height={24} color="#FFFFFF" />
              </View>
              <View style={styles.aboutUsContent}>
                <Text style={styles.aboutUsTitle}>Our Approach</Text>
                <Text style={styles.aboutUsText}>
                  By combining cutting-edge technology with user-friendly design, we create solutions that address the unique challenges faced by those with neurological conditions.
                </Text>
              </View>
            </View>
          </View>

          {/* 5. TESTIMONIALS SECTION */}
          <Text style={styles.sectionTitle}>Testimonials</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.testimonialContainer}
          >
            <View style={styles.testimonialCard}>
              <View style={styles.testimonialHeader}>
                <View style={[styles.testimonialAvatar, { backgroundColor: 'rgba(255, 100, 50, 0.9)' }]}>
                  <Text style={styles.testimonialInitials}>JD</Text>
                </View>
                <View style={styles.testimonialUser}>
                  <Text style={styles.testimonialName}>John Doe</Text>
                  <Text style={styles.testimonialRole}>Patient</Text>
                </View>
              </View>
              <View style={styles.ratingContainer}>
                {[1, 2, 3, 4, 5].map((_, index) => (
                  <Star key={index} width={16} height={16} color="#FFD700" fill="#FFD700" />
                ))}
              </View>
              <Text style={styles.testimonialText}>
                "Neuro Care has transformed how I manage my condition. The medication reminders and symptom tracking features have been invaluable in my daily routine."
              </Text>
            </View>

            <View style={styles.testimonialCard}>
              <View style={styles.testimonialHeader}>
                <View style={[styles.testimonialAvatar, { backgroundColor: 'rgba(0, 200, 150, 0.9)' }]}>
                  <Text style={styles.testimonialInitials}>MS</Text>
                </View>
                <View style={styles.testimonialUser}>
                  <Text style={styles.testimonialName}>Maria Smith</Text>
                  <Text style={styles.testimonialRole}>Caregiver</Text>
                </View>
              </View>
              <View style={styles.ratingContainer}>
                {[1, 2, 3, 4, 5].map((_, index) => (
                  <Star key={index} width={16} height={16} color="#FFD700" fill="#FFD700" />
                ))}
              </View>
              <Text style={styles.testimonialText}>
                "As a caregiver, this app has made coordinating care so much easier. The communication features help me stay connected with healthcare providers."
              </Text>
            </View>

            <View style={styles.testimonialCard}>
              <View style={styles.testimonialHeader}>
                <View style={[styles.testimonialAvatar, { backgroundColor: 'rgba(100, 120, 255, 0.9)' }]}>
                  <Text style={styles.testimonialInitials}>RJ</Text>
                </View>
                <View style={styles.testimonialUser}>
                  <Text style={styles.testimonialName}>Dr. Robert Johnson</Text>
                  <Text style={styles.testimonialRole}>Neurologist</Text>
                </View>
              </View>
              <View style={styles.ratingContainer}>
                {[1, 2, 3, 4, 5].map((_, index) => (
                  <Star key={index} width={16} height={16} color="#FFD700" fill={index < 4 ? "#FFD700" : "transparent"} />
                ))}
              </View>
              <Text style={styles.testimonialText}>
                "Neuro Care has improved how I monitor my patients' progress. The data visualization tools help me make more informed treatment decisions."
              </Text>
            </View>
          </ScrollView>

          {/* 6. CONTACT & SUPPORT SECTION */}
          <Text style={styles.sectionTitle}>Contact & Support</Text>
          <View style={styles.contactCard}>
            <View style={[
              styles.contactMainSection,
              { flexDirection: isLargeDevice ? 'row' : 'column' }
            ]}>
              <View style={[
                styles.contactColumn,
                { width: isLargeDevice ? '48%' : '100%' }
              ]}>
                <Text style={styles.contactSectionTitle}>Get in Touch</Text>

                <View style={styles.contactItemRow}>
                  <View style={[styles.contactIconContainer, { backgroundColor: 'rgba(255, 100, 50, 0.9)' }]}>
                    <Mail
                      width={getResponsiveSize(20, 22, 24)}
                      height={getResponsiveSize(20, 22, 24)}
                      color="#FFFFFF"
                    />
                  </View>
                  <View style={styles.contactTextContainer}>
                    <Text style={[styles.contactTitle, { fontSize: getResponsiveSize(16, 18, 20) }]}>Email Support</Text>
                    <Text style={styles.contactInfo}><EMAIL></Text>
                    <TouchableOpacity style={styles.contactButton}>
                      <Text style={styles.contactButtonText}>Send Email</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.contactItemRow}>
                  <View style={[styles.contactIconContainer, { backgroundColor: 'rgba(0, 200, 150, 0.9)' }]}>
                    <Phone
                      width={getResponsiveSize(20, 22, 24)}
                      height={getResponsiveSize(20, 22, 24)}
                      color="#FFFFFF"
                    />
                  </View>
                  <View style={styles.contactTextContainer}>
                    <Text style={[styles.contactTitle, { fontSize: getResponsiveSize(16, 18, 20) }]}>Phone Support</Text>
                    <Text style={styles.contactInfo}>+****************</Text>
                    <Text style={styles.contactHours}>Mon-Fri: 9AM - 5PM EST</Text>
                    <TouchableOpacity style={styles.contactButton}>
                      <Text style={styles.contactButtonText}>Call Now</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              <View style={[
                styles.contactDivider,
                {
                  width: isLargeDevice ? 1 : '100%',
                  height: isLargeDevice ? 'auto' : 1,
                  marginVertical: isLargeDevice ? 0 : 20,
                  marginHorizontal: isLargeDevice ? 20 : 0
                }
              ]} />

              <View style={[
                styles.contactColumn,
                { width: isLargeDevice ? '48%' : '100%' }
              ]}>
                <Text style={styles.contactSectionTitle}>Visit Us</Text>

                <View style={styles.contactItemRow}>
                  <View style={[styles.contactIconContainer, { backgroundColor: 'rgba(100, 120, 255, 0.9)' }]}>
                    <MapPin
                      width={getResponsiveSize(20, 22, 24)}
                      height={getResponsiveSize(20, 22, 24)}
                      color="#FFFFFF"
                    />
                  </View>
                  <View style={styles.contactTextContainer}>
                    <Text style={[styles.contactTitle, { fontSize: getResponsiveSize(16, 18, 20) }]}>Our Office</Text>
                    <Text style={styles.contactInfo}>123 Neuro Street, Suite 456</Text>
                    <Text style={styles.contactInfo}>Health City, CA 98765</Text>
                    <TouchableOpacity style={styles.contactButton}>
                      <Text style={styles.contactButtonText}>Get Directions</Text>
                      <ExternalLink
                        width={getResponsiveSize(14, 16, 18)}
                        height={getResponsiveSize(14, 16, 18)}
                        color="#FFFFFF"
                        style={styles.buttonIcon}
                      />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.contactItemRow}>
                  <View style={[styles.contactIconContainer, { backgroundColor: 'rgba(255, 170, 0, 0.9)' }]}>
                    <HelpCircle
                      width={getResponsiveSize(20, 22, 24)}
                      height={getResponsiveSize(20, 22, 24)}
                      color="#FFFFFF"
                    />
                  </View>
                  <View style={styles.contactTextContainer}>
                    <Text style={[styles.contactTitle, { fontSize: getResponsiveSize(16, 18, 20) }]}>Help Center</Text>
                    <Text style={styles.contactInfo}>Find answers to common questions</Text>
                    <TouchableOpacity style={styles.contactButton}>
                      <Text style={styles.contactButtonText}>Visit FAQ</Text>
                      <ExternalLink
                        width={getResponsiveSize(14, 16, 18)}
                        height={getResponsiveSize(14, 16, 18)}
                        color="#FFFFFF"
                        style={styles.buttonIcon}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.socialSection}>
              <Text style={styles.contactSectionTitle}>Connect With Us</Text>
              <View style={styles.socialContainer}>
                <TouchableOpacity style={[styles.socialButton, { backgroundColor: '#3b5998' }]}>
                  <Facebook width={20} height={20} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={[styles.socialButton, { backgroundColor: '#1DA1F2' }]}>
                  <Twitter width={20} height={20} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={[styles.socialButton, { backgroundColor: '#E1306C' }]}>
                  <Instagram width={20} height={20} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={[styles.socialButton, { backgroundColor: '#0077B5' }]}>
                  <Globe width={20} height={20} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.emergencyContainer}>
              <Text style={styles.emergencyTitle}>In case of emergency</Text>
              <Text style={styles.emergencyText}>
                This app is not intended for emergency situations. If you are experiencing a medical emergency, please call your local emergency number (911 in the US) or go to your nearest emergency room.
              </Text>
            </View>
          </View>

        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  contactCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: getResponsiveSize(20, 24, 28),
    padding: getResponsiveSize(16, 20, 24),
    marginBottom: 30,
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  contactMainSection: {
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  contactColumn: {
    marginBottom: isLargeDevice ? 0 : 20,
  },
  contactSectionTitle: {
    fontSize: getResponsiveSize(18, 20, 22),
    fontWeight: '700',
    color: '#222222',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    paddingBottom: 8,
  },
  contactItemRow: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-start',
  },
  contactIconContainer: {
    width: getResponsiveSize(45, 50, 55),
    height: getResponsiveSize(45, 50, 55),
    borderRadius: getResponsiveSize(22.5, 25, 27.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  contactTextContainer: {
    flex: 1,
  },
  contactTitle: {
    fontWeight: '700',
    color: '#222222',
    marginBottom: 4,
  },
  contactInfo: {
    fontSize: getResponsiveSize(13, 15, 16),
    color: '#444444',
    marginBottom: 4,
  },
  contactHours: {
    fontSize: getResponsiveSize(11, 13, 14),
    color: '#666666',
    marginBottom: 8,
  },
  contactButton: {
    backgroundColor: 'rgba(16, 107, 0, 1)',
    borderRadius: 20,
    paddingVertical: getResponsiveSize(6, 8, 10),
    paddingHorizontal: getResponsiveSize(12, 16, 20),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
    marginTop: 4,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  contactButtonText: {
    color: '#FFFFFF',
    fontSize: getResponsiveSize(12, 14, 16),
    fontWeight: '600',
  },
  buttonIcon: {
    marginLeft: 5,
  },
  contactDivider: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  socialSection: {
    marginBottom: 24,
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 8,
    flexWrap: 'wrap',
  },
  socialButton: {
    width: getResponsiveSize(36, 40, 44),
    height: getResponsiveSize(36, 40, 44),
    borderRadius: getResponsiveSize(18, 20, 22),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: isSmallDevice ? 10 : 0,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  emergencyContainer: {
    backgroundColor: 'rgba(255, 240, 240, 0.8)',
    borderRadius: 12,
    padding: getResponsiveSize(12, 16, 20),
    borderWidth: 1,
    borderColor: 'rgba(255, 100, 100, 0.3)',
  },
  emergencyTitle: {
    fontSize: getResponsiveSize(14, 16, 18),
    fontWeight: '700',
    color: '#D32F2F',
    marginBottom: 8,
  },
  emergencyText: {
    fontSize: getResponsiveSize(12, 14, 16),
    color: '#333333',
    lineHeight: getResponsiveSize(18, 20, 22),
  },
  howItWorksSection: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 24,
    padding: getResponsiveSize(16, 20, 24),
    marginBottom: 30,
    marginHorizontal: getResponsiveSize(8, 12, 16),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
  },
  howItWorksHeader: {
    marginBottom: 15,
  },
  howItWorksSubtitle: {
    fontSize: getResponsiveSize(14, 16, 18),
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
    marginHorizontal: 20,
    fontStyle: 'italic',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  paginationIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  paginationDotContainer: {
    padding: 8, // Agrandit la zone tactile
    marginHorizontal: 2,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
  },
  howItWorksContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 10,
    flexWrap: isLargeDevice ? 'wrap' : 'nowrap',
    paddingHorizontal: isLargeDevice ? getResponsiveSize(8, 12, 16) : 0,
  },
  stepCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 24,
    padding: getResponsiveSize(16, 20, 24),
    marginBottom: 0,
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
    backfaceVisibility: 'hidden',
    transform: [{ perspective: 1000 }],
    height: getResponsiveSize(300, 320, 340),
  },
  stepCardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.2,
    zIndex: 0,
    borderRadius: 24,
    overflow: 'hidden',
  },
  cardGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  stepCardContent: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 10,
    paddingHorizontal: 8,
    zIndex: 1,
  },

  stepIconContainer: {
    width: getResponsiveSize(60, 70, 80),
    height: getResponsiveSize(60, 70, 80),
    borderRadius: getResponsiveSize(30, 35, 40),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 15,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.35,
    shadowRadius: 8,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    transform: [{ rotate: '-5deg' }],
    zIndex: 1,
  },
  stepTitle: {
    fontSize: getResponsiveSize(18, 20, 22),
    fontWeight: '800',
    color: '#222222',
    marginBottom: 12,
    textAlign: 'center',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  stepDescription: {
    fontSize: getResponsiveSize(13, 14, 16),
    color: '#444444',
    lineHeight: getResponsiveSize(18, 20, 24),
    textAlign: 'center',
    letterSpacing: 0.3,
    paddingHorizontal: 5,
  },
  testimonialContainer: {
    paddingRight: 24,
    paddingBottom: 10,
  },
  testimonialCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 24,
    padding: getResponsiveSize(20, 24, 28),
    marginRight: 16,
    width: isSmallDevice ? screenWidth * 0.8 : isMediumDevice ? 280 : 300,
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    transform: [{ perspective: 1000 }],
    backfaceVisibility: 'hidden',
    position: 'relative',
    overflow: 'hidden',
  },
  testimonialHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  testimonialAvatar: {
    width: getResponsiveSize(50, 60, 65),
    height: getResponsiveSize(50, 60, 65),
    borderRadius: getResponsiveSize(25, 30, 32.5),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
  testimonialInitials: {
    color: '#FFFFFF',
    fontSize: getResponsiveSize(16, 18, 20),
    fontWeight: '700',
  },
  testimonialUser: {
    flex: 1,
  },
  testimonialName: {
    fontSize: getResponsiveSize(16, 18, 20),
    fontWeight: '800',
    color: '#222222',
    marginBottom: 4,
    letterSpacing: 0.3,
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  testimonialRole: {
    fontSize: getResponsiveSize(13, 15, 17),
    color: '#666666',
    letterSpacing: 0.2,
  },
  ratingContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  testimonialText: {
    fontSize: getResponsiveSize(14, 16, 18),
    color: '#444444',
    lineHeight: getResponsiveSize(22, 24, 26),
    fontStyle: 'italic',
    letterSpacing: 0.3,
    paddingHorizontal: 5,
    paddingTop: 5,
    textAlign: 'center',
    borderLeftWidth: 3,
    borderLeftColor: 'rgba(16, 107, 0, 0.3)',
    paddingLeft: 12,
    marginLeft: 5,
  },
  aboutUsCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: getResponsiveSize(20, 24, 28),
    padding: getResponsiveSize(16, 20, 24),
    marginBottom: 30,
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  aboutUsSection: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-start',
  },
  aboutUsIcon: {
    width: getResponsiveSize(40, 48, 56),
    height: getResponsiveSize(40, 48, 56),
    borderRadius: getResponsiveSize(20, 24, 28),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  aboutUsContent: {
    flex: 1,
  },
  aboutUsTitle: {
    fontSize: getResponsiveSize(18, 20, 22),
    fontWeight: "700",
    color: "#222222",
    marginBottom: 8,
    letterSpacing: 0.3,
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  aboutUsText: {
    fontSize: getResponsiveSize(14, 16, 18),
    color: "#444444",
    lineHeight: getResponsiveSize(20, 24, 28),
    letterSpacing: 0.2,
    marginBottom: 8,
  },
  wrapper: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: 'rgba(16, 107, 0, 0.9)', // Vert plus vibrant et plus opaque
  },
  header: {
    paddingHorizontal: getResponsiveSize(12, 16, 20),
    paddingVertical: getResponsiveSize(8, 12, 16),
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
    flexDirection: 'row',
    justifyContent: 'space-between',
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
  },
  applogo:{
    flexDirection: 'row',
    paddingTop: getResponsiveSize(4, 6, 8),
  },
  appNameText:{
    paddingLeft: 10,
    paddingTop: getResponsiveSize(8, 12, 14),
  },
  logo: {
    width: getResponsiveSize(60, 70, 80),
    height: getResponsiveSize(60, 70, 80),
  },
  appName: {
    fontSize: getResponsiveSize(16, 18, 20),
    fontWeight: "700",
    color: "#333333",
    marginBottom: 2,
    paddingTop: 5,
    letterSpacing: 0.5,
  },
  tagline: {
    fontSize: getResponsiveSize(9, 10, 12),
    color: "#666666",
    textAlign: "center",
    letterSpacing: 0.2,
  },
  loginButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingVertical: getResponsiveSize(6, 8, 10),
    paddingHorizontal: getResponsiveSize(16, 20, 24),
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    borderWidth: 1,
    borderColor: 'rgba(0, 150, 50, 1)',
  },
  loginButtonText: {
    color: 'rgba(0, 150, 50, 1)',
    fontSize: getResponsiveSize(14, 16, 18),
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: getResponsiveSize(16, 20, 24),
    paddingBottom: 40,
  },
  welcomeCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: getResponsiveSize(20, 24, 28),
    padding: getResponsiveSize(16, 20, 24),
    alignItems: "center",
    marginBottom: 30,
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  welcomeImage: {
    width: getResponsiveSize(180, 220, 260),
    height: getResponsiveSize(180, 220, 260),
    borderRadius: getResponsiveSize(90, 110, 130),
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: getResponsiveSize(24, 30, 36),
    fontWeight: "700",
    color: "#222222",
    marginBottom: 16,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  welcomeText: {
    fontSize: getResponsiveSize(15, 17, 19),
    color: "#444444",
    textAlign: "center",
    lineHeight: getResponsiveSize(22, 24, 28),
    letterSpacing: 0.3,
    marginBottom: 20,
  },
  welcomeButtonsContainer: {
    flexDirection: isSmallDevice ? 'column' : 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginTop: 5,
  },
  getStartedButton: {
    backgroundColor: 'rgba(0, 150, 50, 1)',
    borderRadius: 24,
    paddingVertical: getResponsiveSize(10, 12, 14),
    paddingHorizontal: getResponsiveSize(16, 20, 24),
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    marginRight: isSmallDevice ? 0 : 15,
    marginBottom: isSmallDevice ? 10 : 0,
    alignSelf: isSmallDevice ? 'stretch' : 'auto',
    alignItems: 'center',
  },
  getStartedButtonText: {
    color: '#FFFFFF',
    fontSize: getResponsiveSize(14, 16, 18),
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  getAppButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingVertical: getResponsiveSize(10, 12, 14),
    paddingHorizontal: getResponsiveSize(16, 20, 24),
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    borderWidth: 1,
    borderColor: 'rgba(0, 150, 50, 1)',
    alignSelf: isSmallDevice ? 'stretch' : 'auto',
    alignItems: 'center',
  },
  getAppButtonText: {
    color: 'rgba(0, 150, 50, 1)',
    fontSize: getResponsiveSize(14, 16, 18),
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  sectionTitle: {
    fontSize: getResponsiveSize(22, 26, 30),
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: 20,
    marginTop: 10,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
    marginLeft: getResponsiveSize(8, 12, 16),
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  featureCardContainer: {
    marginBottom: 20,
    height: getResponsiveSize(220, 240, 260),
  },
  featureCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 24,
    padding: getResponsiveSize(16, 18, 20),
    width: "100%",
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    position: 'relative',
    overflow: 'hidden',
    height: getResponsiveSize(220, 240, 260),
    justifyContent: 'space-between',
    transform: [{ perspective: 1000 }],
    backfaceVisibility: 'hidden',
  },
  learnMoreButton: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: getResponsiveSize(3, 4, 6),
    paddingHorizontal: getResponsiveSize(6, 8, 12),
    alignSelf: 'flex-start',
    marginTop: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  learnMoreButtonText: {
    fontSize: getResponsiveSize(9, 10, 12),
    fontWeight: '600',
  },
  iconContainer: {
    width: getResponsiveSize(50, 60, 70),
    height: getResponsiveSize(50, 60, 70),
    borderRadius: getResponsiveSize(25, 30, 35),
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 15,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.35,
    shadowRadius: 8,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    transform: [{ rotate: '-3deg' }],
  },
  featureTitle: {
    fontSize: getResponsiveSize(16, 18, 20),
    fontWeight: "800",
    color: "#222222",
    marginBottom: 8,
    letterSpacing: 0.5,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  featureDescription: {
    fontSize: getResponsiveSize(12, 13, 15),
    color: "#444444",
    lineHeight: getResponsiveSize(18, 20, 22),
    letterSpacing: 0.3,
    textAlign: 'center',
    paddingHorizontal: 5,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: getResponsiveSize(16, 20, 24),
  },
  errorText: {
    fontSize: getResponsiveSize(16, 18, 20),
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: 'rgba(16, 107, 0, 1)',
    borderRadius: 24,
    paddingVertical: getResponsiveSize(10, 12, 14),
    paddingHorizontal: getResponsiveSize(20, 24, 28),
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: getResponsiveSize(14, 16, 18),
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});