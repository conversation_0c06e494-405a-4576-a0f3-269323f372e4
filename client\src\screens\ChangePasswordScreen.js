import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Appbar } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { getThemeForRole } from '../config/theme';
import ChangePassword from '../components/settings/ChangePassword';

const ChangePasswordScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');

  return (
    <View style={styles.container}>
      <Appbar.Header style={{ backgroundColor: theme.colors.primary }}>
        <Appbar.BackAction color="white" onPress={() => navigation.goBack()} />
        <Appbar.Content title="Change Password" color="white" />
      </Appbar.Header>
      <ChangePassword navigation={navigation} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default ChangePasswordScreen;
