const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const db = admin.firestore();

// Middleware to verify authentication
const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Invalid token' });
    }
};

// Create a new appointment
router.post('/create', authenticateUser, async (req, res) => {
    try {
        const { doctorId, date, time, reason, type, duration } = req.body;
        const patientId = req.user.uid;

        // Validate required fields
        if (!doctorId || !date || !time) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Check if the doctor exists
        const doctorDoc = await db.collection('users').doc(doctorId).get();
        if (!doctorDoc.exists) {
            return res.status(404).json({ error: 'Doctor not found' });
        }

        // Check if the doctor is available at the requested time
        const appointmentDateTime = new Date(`${date}T${time}`);
        const existingAppointments = await db.collection('appointments')
            .where('doctorId', '==', doctorId)
            .where('date', '==', date)
            .where('time', '==', time)
            .where('status', 'in', ['scheduled', 'confirmed'])
            .get();

        if (!existingAppointments.empty) {
            return res.status(409).json({ error: 'Doctor is not available at this time' });
        }

        // Create the appointment
        const appointmentData = {
            patientId,
            doctorId,
            date,
            time,
            reason: reason || '',
            type: type || 'in-person',
            duration: duration || 30, // Default 30 minutes
            status: 'pending',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };

        const appointmentRef = await db.collection('appointments').add(appointmentData);

        // Get the created appointment with its ID
        const createdAppointment = {
            id: appointmentRef.id,
            ...appointmentData,
        };

        // Notify the doctor about the new appointment request
        const doctorData = doctorDoc.data();
        if (doctorData.fcmToken) {
            await admin.messaging().send({
                token: doctorData.fcmToken,
                notification: {
                    title: 'New Appointment Request',
                    body: 'A patient has requested an appointment',
                },
                data: {
                    type: 'appointment_request',
                    appointmentId: appointmentRef.id,
                    patientId,
                },
            });
        }

        res.status(201).json(createdAppointment);
    } catch (error) {
        console.error('Create appointment error:', error);
        res.status(500).json({ error: 'Failed to create appointment' });
    }
});

// Get appointments for a patient
router.get('/patient', authenticateUser, async (req, res) => {
    try {
        const patientId = req.user.uid;
        const { status, startDate, endDate, page = 0, limit = 10 } = req.query;

        let query = db.collection('appointments')
            .where('patientId', '==', patientId)
            .orderBy('date', 'desc')
            .orderBy('time', 'desc');

        if (status) {
            query = query.where('status', '==', status);
        }

        if (startDate && endDate) {
            query = query.where('date', '>=', startDate)
                        .where('date', '<=', endDate);
        }

        // Get total count for pagination
        const totalSnapshot = await query.count().get();
        const total = totalSnapshot.data().count;

        // Apply pagination
        query = query.limit(limit).offset(page * limit);

        const appointmentsSnapshot = await query.get();
        const appointments = [];

        for (const doc of appointmentsSnapshot.docs) {
            const appointment = { id: doc.id, ...doc.data() };

            // Get doctor details
            const doctorDoc = await db.collection('users').doc(appointment.doctorId).get();
            if (doctorDoc.exists) {
                appointment.doctor = doctorDoc.data();
            }

            appointments.push(appointment);
        }

        res.json({
            appointments,
            total,
            page: parseInt(page),
            totalPages: Math.ceil(total / limit),
        });
    } catch (error) {
        console.error('Get patient appointments error:', error);
        res.status(500).json({ error: 'Failed to fetch appointments' });
    }
});

// Get appointments for a doctor
router.get('/doctor', authenticateUser, async (req, res) => {
    try {
        const doctorId = req.user.uid;
        const { status, date, page = 0, limit = 10 } = req.query;

        let query = db.collection('appointments')
            .where('doctorId', '==', doctorId)
            .orderBy('date', 'desc')
            .orderBy('time', 'desc');

        if (status) {
            query = query.where('status', '==', status);
        }

        if (date) {
            query = query.where('date', '==', date);
        }

        // Get total count for pagination
        const totalSnapshot = await query.count().get();
        const total = totalSnapshot.data().count;

        // Apply pagination
        query = query.limit(limit).offset(page * limit);

        const appointmentsSnapshot = await query.get();
        const appointments = [];

        for (const doc of appointmentsSnapshot.docs) {
            const appointment = { id: doc.id, ...doc.data() };

            // Get patient details
            const patientDoc = await db.collection('users').doc(appointment.patientId).get();
            if (patientDoc.exists) {
                appointment.patient = patientDoc.data();
            }

            appointments.push(appointment);
        }

        res.json({
            appointments,
            total,
            page: parseInt(page),
            totalPages: Math.ceil(total / limit),
        });
    } catch (error) {
        console.error('Get doctor appointments error:', error);
        res.status(500).json({ error: 'Failed to fetch appointments' });
    }
});

// Update appointment status
router.put('/:appointmentId/status', authenticateUser, async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { status, notes, date, time, previousDate, previousTime } = req.body;
        const userId = req.user.uid;

        if (!status) {
            return res.status(400).json({ error: 'Status is required' });
        }

        const appointmentDoc = await db.collection('appointments').doc(appointmentId).get();
        if (!appointmentDoc.exists) {
            return res.status(404).json({ error: 'Appointment not found' });
        }

        const appointment = appointmentDoc.data();

        // Check if the user is authorized to update this appointment
        if (appointment.doctorId !== userId && appointment.patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to update this appointment' });
        }

        // Update the appointment
        const updateData = {
            status,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };

        // If this is a reschedule, update date and time
        if (date && time) {
            updateData.date = date;
            updateData.time = time;

            // Store previous date and time if provided
            if (previousDate && previousTime) {
                updateData.previousDate = previousDate;
                updateData.previousTime = previousTime;
                updateData.rescheduledAt = admin.firestore.FieldValue.serverTimestamp();
                updateData.rescheduledBy = userId;
            }
        }

        if (notes) {
            updateData.notes = notes;
        }

        await db.collection('appointments').doc(appointmentId).update(updateData);

        // Get updated appointment data
        const updatedAppointment = {
            id: appointmentId,
            ...appointment,
            ...updateData,
        };

        // Notify the other party about the status change
        const notificationRecipientId = appointment.doctorId === userId ? appointment.patientId : appointment.doctorId;
        const recipientDoc = await db.collection('users').doc(notificationRecipientId).get();

        if (recipientDoc.exists) {
            const recipientData = recipientDoc.data();
            if (recipientData.fcmToken) {
                await admin.messaging().send({
                    token: recipientData.fcmToken,
                    notification: {
                        title: 'Appointment Update',
                        body: `Your appointment has been ${status}`,
                    },
                    data: {
                        type: 'appointment_update',
                        appointmentId,
                        status,
                    },
                });
            }
        }

        res.json(updatedAppointment);
    } catch (error) {
        console.error('Update appointment error:', error);
        res.status(500).json({ error: 'Failed to update appointment' });
    }
});

// Cancel appointment
router.post('/:appointmentId/cancel', authenticateUser, async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { reason } = req.body;
        const userId = req.user.uid;

        const appointmentDoc = await db.collection('appointments').doc(appointmentId).get();
        if (!appointmentDoc.exists) {
            return res.status(404).json({ error: 'Appointment not found' });
        }

        const appointment = appointmentDoc.data();

        // Check if the user is authorized to cancel this appointment
        if (appointment.doctorId !== userId && appointment.patientId !== userId) {
            return res.status(403).json({ error: 'Not authorized to cancel this appointment' });
        }

        // Update the appointment
        const updateData = {
            status: 'cancelled',
            cancellationReason: reason || '',
            cancelledBy: userId,
            cancelledAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };

        await db.collection('appointments').doc(appointmentId).update(updateData);

        // Notify the other party about the cancellation
        const notificationRecipientId = appointment.doctorId === userId ? appointment.patientId : appointment.doctorId;

        // Get recipient's FCM token
        const recipientDoc = await db.collection('users').doc(notificationRecipientId).get();

        if (recipientDoc.exists) {
            const recipientData = recipientDoc.data();
            if (recipientData.fcmToken) {
                try {
                    await admin.messaging().send({
                        token: recipientData.fcmToken,
                        notification: {
                            title: 'Appointment Cancelled',
                            body: 'Your appointment has been cancelled',
                        },
                        data: {
                            type: 'appointment_cancelled',
                            appointmentId,
                            reason: reason || '',
                        },
                    });
                } catch (notificationError) {
                    console.error('Failed to send notification:', notificationError);
                    // Continue execution even if notification fails
                }
            }
        }

        res.json({
            id: appointmentId,
            ...appointment,
            ...updateData,
        });
    } catch (error) {
        console.error('Cancel appointment error:', error);
        res.status(500).json({ error: 'Failed to cancel appointment' });
    }
});

// Get available time slots for a doctor on a specific date
router.get('/available-slots/:doctorId', authenticateUser, async (req, res) => {
    try {
        const { doctorId } = req.params;
        const { date } = req.query;

        if (!date) {
            return res.status(400).json({ error: 'Date is required' });
        }

        // Get doctor's working hours
        const doctorDoc = await db.collection('users').doc(doctorId).get();
        if (!doctorDoc.exists) {
            return res.status(404).json({ error: 'Doctor not found' });
        }

        const doctor = doctorDoc.data();
        const workingHours = doctor.workingHours || {
            start: '09:00',
            end: '17:00',
        };

        // Get booked appointments for the date
        const appointmentsSnapshot = await db.collection('appointments')
            .where('doctorId', '==', doctorId)
            .where('date', '==', date)
            .where('status', 'in', ['scheduled', 'confirmed'])
            .get();

        const bookedSlots = [];
        appointmentsSnapshot.forEach(doc => {
            bookedSlots.push(doc.data().time);
        });

        // Generate available time slots
        const availableSlots = [];
        const startTime = new Date(`2000-01-01T${workingHours.start}`);
        const endTime = new Date(`2000-01-01T${workingHours.end}`);
        const slotDuration = 30; // 30 minutes per slot

        for (let time = startTime; time < endTime; time.setMinutes(time.getMinutes() + slotDuration)) {
            const timeString = time.toTimeString().substring(0, 5);
            if (!bookedSlots.includes(timeString)) {
                availableSlots.push(timeString);
            }
        }

        res.json(availableSlots);
    } catch (error) {
        console.error('Get available slots error:', error);
        res.status(500).json({ error: 'Failed to fetch available slots' });
    }
});

module.exports = router;