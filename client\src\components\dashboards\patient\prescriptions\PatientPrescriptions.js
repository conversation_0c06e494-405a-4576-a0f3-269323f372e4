import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Modal,
  Image,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../../../contexts/AuthContext';
import { firebasePrescriptionsService } from '../../../../services/firebasePrescriptionsService';
import { PATIENT_COLORS } from '../../../../config/theme';

const PatientPrescriptions = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  useEffect(() => {
    loadPrescriptions();

    // Refresh prescriptions when the screen is focused
    const unsubscribe = navigation.addListener('focus', () => {
      loadPrescriptions();
    });

    return unsubscribe;
  }, [navigation, user]);

  const loadPrescriptions = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const patientId = user.uid;
      // Retrieve only prescriptions with "sent" status
      const data = await firebasePrescriptionsService.getPatientPrescriptions(patientId, 'sent');

      // Check if there was a permission error
      if (data._permissionError) {
        Alert.alert(
          'Permission Error',
          'You do not have permission to access prescriptions. Please contact your healthcare provider.',
          [{ text: 'OK' }]
        );
      }

      setPrescriptions(data);
    } catch (error) {
      console.error('Error loading prescriptions from Firebase:', error);
      Alert.alert('Error', 'Failed to load prescriptions. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePrescriptionPress = (prescription) => {
    setSelectedPrescription(prescription);
    setDetailModalVisible(true);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };



  const renderPrescriptionItem = ({ item }) => (
    <TouchableOpacity
      style={styles.prescriptionItem}
      onPress={() => handlePrescriptionPress(item)}
    >
      <View style={styles.prescriptionHeader}>
        <View style={styles.prescriptionIcon}>
          <Ionicons name="document-text" size={24} color={PATIENT_COLORS.primary} />
        </View>
        <View style={styles.prescriptionInfo}>
          <Text style={styles.prescriptionTitle}>Dr. {item.doctorName}</Text>
          <Text style={styles.prescriptionMedication}>
            {item.medications.length > 0
              ? `${item.medications.length} medication${item.medications.length > 1 ? 's' : ''}: ${item.medications.map(med => med.name).join(', ')}`
              : 'No medications'
            }
          </Text>
          <Text style={styles.prescriptionDate}>{formatDate(item.createdAt)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderDetailModal = () => {
    if (!selectedPrescription) return null;

    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={detailModalVisible}
        onRequestClose={() => setDetailModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Prescription Details</Text>
              <TouchableOpacity onPress={() => setDetailModalVisible(false)}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Doctor:</Text>
                <Text style={styles.detailValue}>Dr. {selectedPrescription.doctorName}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Date:</Text>
                <Text style={styles.detailValue}>{formatDate(selectedPrescription.createdAt)}</Text>
              </View>



              <View style={styles.medicationsSection}>
                <Text style={styles.sectionTitle}>Medications</Text>
                {selectedPrescription.medications.map((med, index) => (
                  <View key={index} style={styles.medicationItem}>
                    <Text style={styles.medicationName}>{med.name || 'Unnamed medication'}</Text>

                    {med.dosage ? (
                      <View style={styles.medicationDetailRow}>
                        <Text style={styles.medicationDetailLabel}>Dosage:</Text>
                        <Text style={styles.medicationDetailValue}>{med.dosage}</Text>
                      </View>
                    ) : null}

                    {med.instructions ? (
                      <View style={styles.medicationDetailRow}>
                        <Text style={styles.medicationDetailLabel}>Instructions:</Text>
                        <Text style={styles.medicationDetailValue}>{med.instructions}</Text>
                      </View>
                    ) : null}
                  </View>
                ))}
              </View>

              {selectedPrescription.notes && (
                <View style={styles.notesSection}>
                  <Text style={styles.sectionTitle}>Notes</Text>
                  <Text style={styles.notesText}>{selectedPrescription.notes}</Text>
                </View>
              )}

              {selectedPrescription.prescriptionImage && (
                <View style={styles.imageSection}>
                  <Text style={styles.sectionTitle}>Prescription Image</Text>
                  <Image
                    source={{ uri: selectedPrescription.prescriptionImage }}
                    style={styles.prescriptionImage}
                    resizeMode="contain"
                  />
                </View>
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setDetailModalVisible(false)}
              >
                <Text style={styles.closeButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Prescriptions</Text>
        <View style={{ width: 40 }} />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={PATIENT_COLORS.primary} />
        </View>
      ) : (
        <View style={styles.content}>
          {prescriptions.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text-outline" size={64} color="#BDBDBD" />
              <Text style={styles.emptyText}>No prescriptions yet</Text>
              <Text style={styles.emptySubText}>
                Prescriptions sent by your doctors will appear here
              </Text>
            </View>
          ) : (
            <FlatList
              data={prescriptions}
              renderItem={renderPrescriptionItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
      )}

      {renderDetailModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: '#9E9E9E',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  listContainer: {
    padding: 16,
  },
  prescriptionItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prescriptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: PATIENT_COLORS.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  prescriptionInfo: {
    flex: 1,
    paddingRight: 8,
  },
  prescriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  prescriptionMedication: {
    fontSize: 14,
    color: '#757575',
    marginTop: 2,
  },
  prescriptionDate: {
    fontSize: 12,
    color: '#9E9E9E',
    marginTop: 4,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalScrollView: {
    padding: 16,
  },
  detailSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#757575',
    width: 80,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  medicationsSection: {
    marginTop: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  medicationItem: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  medicationDetailRow: {
    flexDirection: 'row',
    marginBottom: 6,
    paddingLeft: 4,
  },
  medicationDetailLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#555',
    width: 90,
  },
  medicationDetailValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  medicationDosage: {
    fontSize: 14,
    color: '#757575',
    marginTop: 4,
  },
  medicationInstructions: {
    fontSize: 14,
    color: '#757575',
    marginTop: 4,
  },
  notesSection: {
    marginBottom: 16,
  },
  notesText: {
    fontSize: 14,
    color: '#333',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
  },
  imageSection: {
    marginBottom: 16,
  },
  prescriptionImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'center',
  },
  closeButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: PATIENT_COLORS.primary,
    borderRadius: 8,
  },
  closeButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default PatientPrescriptions;
