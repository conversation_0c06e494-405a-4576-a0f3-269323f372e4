import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ROLE_COLORS } from '../config/theme';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';
import { showMessage } from 'react-native-flash-message';

const CaregiverRecordVitalsScreen = ({ route }) => {
  const { patient } = route.params;
  const navigation = useNavigation();
  const caregiverColors = ROLE_COLORS.caregiver;
  
  const [vitalType, setVitalType] = useState('heartRate');
  const [vitalValues, setVitalValues] = useState({
    heartRate: '',
    systolic: '',
    diastolic: '',
    bloodGlucose: '',
    weight: '',
    temperature: '',
    oxygenSaturation: '',
  });
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const handleVitalTypeChange = (type) => {
    setVitalType(type);
  };

  const handleValueChange = (field, value) => {
    setVitalValues({
      ...vitalValues,
      [field]: value
    });
  };

  const getVitalValues = () => {
    switch (vitalType) {
      case 'heartRate':
        return { bpm: vitalValues.heartRate };
      case 'bloodPressure':
        return { 
          systolic: vitalValues.systolic,
          diastolic: vitalValues.diastolic
        };
      case 'bloodGlucose':
        return { mgdl: vitalValues.bloodGlucose };
      case 'weight':
        return { kg: vitalValues.weight };
      case 'temperature':
        return { celsius: vitalValues.temperature };
      case 'oxygenSaturation':
        return { percentage: vitalValues.oxygenSaturation };
      default:
        return {};
    }
  };

  const validateForm = () => {
    switch (vitalType) {
      case 'heartRate':
        return vitalValues.heartRate.trim() !== '';
      case 'bloodPressure':
        return vitalValues.systolic.trim() !== '' && vitalValues.diastolic.trim() !== '';
      case 'bloodGlucose':
        return vitalValues.bloodGlucose.trim() !== '';
      case 'weight':
        return vitalValues.weight.trim() !== '';
      case 'temperature':
        return vitalValues.temperature.trim() !== '';
      case 'oxygenSaturation':
        return vitalValues.oxygenSaturation.trim() !== '';
      default:
        return false;
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const values = getVitalValues();
      await firebaseCaregiverService.recordPatientVital(
        patient.uid,
        vitalType,
        values,
        notes
      );

      showMessage({
        message: 'Success',
        description: 'Vital signs recorded successfully',
        type: 'success',
        backgroundColor: caregiverColors.primary,
      });

      // Navigate back to patient detail
      navigation.goBack();
    } catch (error) {
      console.error('Error recording vital signs:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to record vital signs. Please try again.',
        type: 'danger',
        backgroundColor: '#ff4444',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderVitalTypeButton = (type, label, icon) => (
    <TouchableOpacity
      style={[
        styles.vitalTypeButton,
        vitalType === type && [styles.activeVitalTypeButton, { borderColor: caregiverColors.primary }]
      ]}
      onPress={() => handleVitalTypeChange(type)}
    >
      <Ionicons
        name={icon}
        size={20}
        color={vitalType === type ? caregiverColors.primary : '#888'}
      />
      <Text
        style={[
          styles.vitalTypeButtonText,
          vitalType === type && [styles.activeVitalTypeButtonText, { color: caregiverColors.primary }]
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderVitalInputs = () => {
    switch (vitalType) {
      case 'heartRate':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Heart Rate (BPM)</Text>
            <TextInput
              style={styles.input}
              value={vitalValues.heartRate}
              onChangeText={(value) => handleValueChange('heartRate', value)}
              placeholder="Enter heart rate"
              keyboardType="numeric"
            />
          </View>
        );
      case 'bloodPressure':
        return (
          <>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Systolic (mmHg)</Text>
              <TextInput
                style={styles.input}
                value={vitalValues.systolic}
                onChangeText={(value) => handleValueChange('systolic', value)}
                placeholder="Enter systolic pressure"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Diastolic (mmHg)</Text>
              <TextInput
                style={styles.input}
                value={vitalValues.diastolic}
                onChangeText={(value) => handleValueChange('diastolic', value)}
                placeholder="Enter diastolic pressure"
                keyboardType="numeric"
              />
            </View>
          </>
        );
      case 'bloodGlucose':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Blood Glucose (mg/dL)</Text>
            <TextInput
              style={styles.input}
              value={vitalValues.bloodGlucose}
              onChangeText={(value) => handleValueChange('bloodGlucose', value)}
              placeholder="Enter blood glucose"
              keyboardType="numeric"
            />
          </View>
        );
      case 'weight':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Weight (kg)</Text>
            <TextInput
              style={styles.input}
              value={vitalValues.weight}
              onChangeText={(value) => handleValueChange('weight', value)}
              placeholder="Enter weight"
              keyboardType="numeric"
            />
          </View>
        );
      case 'temperature':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Temperature (°C)</Text>
            <TextInput
              style={styles.input}
              value={vitalValues.temperature}
              onChangeText={(value) => handleValueChange('temperature', value)}
              placeholder="Enter temperature"
              keyboardType="numeric"
            />
          </View>
        );
      case 'oxygenSaturation':
        return (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Oxygen Saturation (%)</Text>
            <TextInput
              style={styles.input}
              value={vitalValues.oxygenSaturation}
              onChangeText={(value) => handleValueChange('oxygenSaturation', value)}
              placeholder="Enter oxygen saturation"
              keyboardType="numeric"
            />
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Record Vital Signs</Text>
        <Text style={styles.patientName}>
          Patient: {patient.firstName} {patient.lastName}
        </Text>
      </View>

      <View style={styles.vitalTypeContainer}>
        <Text style={styles.sectionTitle}>Select Vital Type</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.vitalTypeScroll}>
          {renderVitalTypeButton('heartRate', 'Heart Rate', 'heart')}
          {renderVitalTypeButton('bloodPressure', 'Blood Pressure', 'fitness')}
          {renderVitalTypeButton('bloodGlucose', 'Blood Glucose', 'water')}
          {renderVitalTypeButton('weight', 'Weight', 'scale')}
          {renderVitalTypeButton('temperature', 'Temperature', 'thermometer')}
          {renderVitalTypeButton('oxygenSaturation', 'Oxygen', 'pulse')}
        </ScrollView>
      </View>

      <View style={styles.formContainer}>
        <Text style={styles.sectionTitle}>Enter Values</Text>
        {renderVitalInputs()}

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Notes (Optional)</Text>
          <TextInput
            style={[styles.input, styles.notesInput]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Enter any additional notes"
            multiline
          />
        </View>

        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: caregiverColors.primary }]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name="save" size={20} color="#fff" />
              <Text style={styles.submitButtonText}>Save Vital Signs</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  patientName: {
    fontSize: 16,
    color: '#666',
  },
  vitalTypeContainer: {
    padding: 20,
    backgroundColor: '#fff',
    marginTop: 15,
    borderRadius: 10,
    marginHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  vitalTypeScroll: {
    flexDirection: 'row',
  },
  vitalTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#f5f5f5',
    marginRight: 10,
  },
  activeVitalTypeButton: {
    backgroundColor: '#f0f8ff',
  },
  vitalTypeButtonText: {
    marginLeft: 5,
    fontSize: 14,
    color: '#888',
  },
  activeVitalTypeButtonText: {
    fontWeight: '500',
  },
  formContainer: {
    padding: 20,
    backgroundColor: '#fff',
    marginTop: 15,
    borderRadius: 10,
    marginHorizontal: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  notesInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 5,
    marginTop: 20,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default CaregiverRecordVitalsScreen;
