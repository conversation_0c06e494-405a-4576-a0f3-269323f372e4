const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const admin = require('firebase-admin');
const path = require('path');

// Initialize environment variables
dotenv.config();

// Initialize Firebase Admin
try {
    // If running in production with environment variables
    if (process.env.FIREBASE_CONFIG) {
        admin.initializeApp({
            credential: admin.credential.cert(JSON.parse(process.env.FIREBASE_CONFIG)),
            databaseURL: process.env.FIREBASE_DATABASE_URL,
            storageBucket: process.env.FIREBASE_STORAGE_BUCKET
        });
    }
    // If running locally with service account file
    else {
        const serviceAccount = require('./config/serviceAccountKey.json');
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            databaseURL: process.env.FIREBASE_DATABASE_URL,
            storageBucket: process.env.FIREBASE_STORAGE_BUCKET
        });
    }
    console.log('Firebase Admin initialized successfully');
} catch (error) {
    console.error('Error initializing Firebase Admin:', error);
    process.exit(1);
}

// Initialize Express app
const app = express();

// CORS configuration - Allow all origins
app.use(cors({
    origin: true, // Allow all origins
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize services
const db = admin.firestore();
const messaging = admin.messaging();
const realtimeDb = admin.database();

// Routes import
const notificationRoutes = require('./routes/notifications');
const symptomRoutes = require('./routes/symptoms');
const securityRoutes = require('./routes/security');
const communicationRoutes = require('./routes/communication');
const aiRoutes = require('./routes/ai');
const usersRoutes = require('./routes/users');
const authRoutes = require('./routes/auth');
const vitalsRoutes = require('./routes/vitals');
const appointmentRoutes = require('./routes/appointments');
const medicalRoutes = require('./routes/medical');

// Use routes
app.use('/api/notifications', notificationRoutes);
app.use('/api/symptoms', symptomRoutes);
app.use('/api/security', securityRoutes);
app.use('/api/communication', communicationRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/vitals', vitalsRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/medical', medicalRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err.stack);
    res.status(500).json({
        error: 'Something went wrong!',
        message: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

const PORT = parseInt(process.env.PORT || '3001', 10);
const HOST = process.env.HOST || '0.0.0.0';

const startServer = () => {
    const server = app.listen(PORT, HOST, () => {
        console.log(`Server running on http://${HOST}:${PORT}`);
        console.log('Server available at:');
        console.log(`- http://localhost:${PORT}`);

        // Get all network interfaces to show available IP addresses
        const { networkInterfaces } = require('os');
        const nets = networkInterfaces();

        console.log('Available on all these IP addresses:');
        // List all IP addresses where the server is accessible
        for (const name of Object.keys(nets)) {
            for (const net of nets[name]) {
                // Skip internal and non-IPv4 addresses
                if (net.family === 'IPv4' && !net.internal) {
                    console.log(`- http://${net.address}:${PORT}`);
                }
            }
        }
        console.log('The server is now accessible from any device on your network.');
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
        console.log('SIGTERM signal received: closing HTTP server');
        server.close(() => {
            console.log('HTTP server closed');
            process.exit(0);
        });
    });
};

startServer();