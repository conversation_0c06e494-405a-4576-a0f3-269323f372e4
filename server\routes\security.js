const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');

// Initialize Firestore
const db = admin.firestore();

// Report security alert
router.post('/alert', verifyToken, async (req, res) => {
    try {
        const { type, severity, description, location } = req.body;
        const userId = req.user.uid;

        const alert = {
            userId,
            type,
            severity,
            description,
            location,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            resolved: false
        };

        const docRef = await db.collection('securityAlerts').add(alert);

        // If high severity, notify caregivers
        if (severity === 'high') {
            const userDoc = await db.collection('users').doc(userId).get();
            const userData = userDoc.data();

            if (userData.emergencyContacts) {
                // Here you would implement notification logic for emergency contacts
                console.log('Emergency contacts to notify:', userData.emergencyContacts);
            }
        }

        res.status(201).json({
            success: true,
            alertId: docRef.id
        });
    } catch (error) {
        console.error('Error creating security alert:', error);
        res.status(500).json({ error: 'Failed to create security alert' });
    }
});

// Get security alerts
router.get('/alerts', verifyToken, async (req, res) => {
    try {
        const userId = req.user.uid;
        const { status = 'unresolved' } = req.query;

        let query = db.collection('securityAlerts')
            .where('userId', '==', userId)
            .orderBy('timestamp', 'desc');

        if (status === 'unresolved') {
            query = query.where('resolved', '==', false);
        }

        const alertsSnapshot = await query.get();

        const alerts = [];
        alertsSnapshot.forEach(doc => {
            alerts.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(alerts);
    } catch (error) {
        console.error('Error fetching security alerts:', error);
        res.status(500).json({ error: 'Failed to fetch security alerts' });
    }
});

// Resolve alert
router.put('/alert/:alertId/resolve', verifyToken, async (req, res) => {
    try {
        const { alertId } = req.params;
        const userId = req.user.uid;

        const alertRef = db.collection('securityAlerts').doc(alertId);
        const alert = await alertRef.get();

        if (!alert.exists) {
            return res.status(404).json({ error: 'Alert not found' });
        }

        if (alert.data().userId !== userId) {
            return res.status(403).json({ error: 'Not authorized' });
        }

        await alertRef.update({
            resolved: true,
            resolvedBy: userId,
            resolvedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.json({ success: true });
    } catch (error) {
        console.error('Error resolving alert:', error);
        res.status(500).json({ error: 'Failed to resolve alert' });
    }
});

module.exports = router;
