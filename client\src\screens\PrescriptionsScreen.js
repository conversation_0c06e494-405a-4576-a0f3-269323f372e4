import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Modal,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { firebasePrescriptionsService } from '../services/firebasePrescriptionsService';
import { ROLE_COLORS } from '../config/theme';

const PrescriptionsScreen = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    loadPrescriptions();

    // Refresh prescriptions when the screen is focused
    const unsubscribe = navigation.addListener('focus', () => {
      loadPrescriptions();
    });

    return unsubscribe;
  }, [navigation, user]);

  const loadPrescriptions = async () => {
    setLoading(true);
    try {
      const doctorId = user?.uid;
      const data = await firebasePrescriptionsService.getDoctorPrescriptions(doctorId);

      // Check if there was a permission error
      if (data._permissionError) {
        Alert.alert(
          'Permission Error',
          'You do not have permission to access prescriptions. Please contact your administrator to update Firestore security rules.',
          [{ text: 'OK' }]
        );
      }

      setPrescriptions(data);
    } catch (error) {
      console.error('Error loading prescriptions from Firebase:', error);
      Alert.alert('Error', 'Failed to load prescriptions. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePrescriptionPress = (prescription) => {
    setSelectedPrescription(prescription);
    setDetailModalVisible(true);
  };

  const handleSendPrescription = async (prescription) => {
    try {
      await firebasePrescriptionsService.updatePrescriptionStatus(prescription.id, 'sent');
      Alert.alert('Success', 'Prescription sent successfully');
      setDetailModalVisible(false);
      loadPrescriptions();
    } catch (error) {
      console.error('Error sending prescription in Firebase:', error);
      Alert.alert('Error', 'Failed to send prescription. Please check your connection and try again.');
    }
  };

  const handleDeletePrescription = async (prescription) => {
    Alert.alert(
      'Delete Prescription',
      'Are you sure you want to delete this prescription?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await firebasePrescriptionsService.deletePrescription(prescription.id);
              setDetailModalVisible(false);
              loadPrescriptions();
            } catch (error) {
              console.error('Error deleting prescription from Firebase:', error);
              Alert.alert('Error', 'Failed to delete prescription. Please check your connection and try again.');
            }
          }
        }
      ]
    );
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA000';
      case 'sent':
        return '#4CAF50';
      case 'filled':
        return '#2196F3';
      default:
        return '#9E9E9E';
    }
  };

  const renderPrescriptionItem = ({ item }) => (
    <TouchableOpacity
      style={styles.prescriptionItem}
      onPress={() => handlePrescriptionPress(item)}
    >
      <View style={styles.prescriptionHeader}>
        <View style={styles.prescriptionIcon}>
          <Ionicons name="document-text" size={24} color={doctorColors.primary} />
        </View>
        <View style={styles.prescriptionInfo}>
          <Text style={styles.prescriptionTitle}>{item.patientName}</Text>
          <Text style={styles.prescriptionMedication}>{item.medications.map(med => med.name).join(', ')}</Text>
          <Text style={styles.prescriptionDate}>{formatDate(item.createdAt)}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Prescriptions</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('NewPrescription')}
        >
          <Ionicons name="add" size={24} color={doctorColors.primary} />
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={doctorColors.primary} />
        </View>
      ) : (
        <View style={styles.content}>
          {prescriptions.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text-outline" size={64} color="#BDBDBD" />
              <Text style={styles.emptyText}>No prescriptions yet</Text>
              <TouchableOpacity
                style={[styles.emptyButton, { backgroundColor: doctorColors.primary }]}
                onPress={() => navigation.navigate('NewPrescription')}
              >
                <Text style={styles.emptyButtonText}>Create Prescription</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={prescriptions}
              renderItem={renderPrescriptionItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
      )}

      {/* Prescription Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={detailModalVisible}
        onRequestClose={() => setDetailModalVisible(false)}
      >
        {selectedPrescription && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Prescription Details</Text>
                <TouchableOpacity onPress={() => setDetailModalVisible(false)}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Patient:</Text>
                <Text style={styles.detailValue}>{selectedPrescription.patientName}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Date:</Text>
                <Text style={styles.detailValue}>{formatDate(selectedPrescription.createdAt)}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailLabel}>Status:</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedPrescription.status) }]}>
                  <Text style={styles.statusText}>{selectedPrescription.status}</Text>
                </View>
              </View>

              <View style={styles.medicationsSection}>
                <Text style={styles.sectionTitle}>Medications</Text>
                {selectedPrescription.medications.map((med, index) => (
                  <View key={index} style={styles.medicationItem}>
                    <Text style={styles.medicationName}>{med.name}</Text>
                    <Text style={styles.medicationDosage}>{med.dosage}</Text>
                    <Text style={styles.medicationInstructions}>{med.instructions}</Text>
                  </View>
                ))}
              </View>

              {selectedPrescription.notes && (
                <View style={styles.notesSection}>
                  <Text style={styles.sectionTitle}>Notes</Text>
                  <Text style={styles.notesText}>{selectedPrescription.notes}</Text>
                </View>
              )}

              {selectedPrescription.prescriptionImage && (
                <View style={styles.imageSection}>
                  <Text style={styles.sectionTitle}>Prescription Image</Text>
                  <Image
                    source={{ uri: selectedPrescription.prescriptionImage }}
                    style={styles.prescriptionImage}
                    resizeMode="contain"
                  />
                </View>
              )}

              <View style={styles.actionButtons}>
                {selectedPrescription.status === 'pending' && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
                    onPress={() => handleSendPrescription(selectedPrescription)}
                  >
                    <Ionicons name="send" size={20} color="#fff" />
                    <Text style={styles.actionButtonText}>Send</Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: '#F44336' }]}
                  onPress={() => handleDeletePrescription(selectedPrescription)}
                >
                  <Ionicons name="trash" size={20} color="#fff" />
                  <Text style={styles.actionButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 8,
  },
  addButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    color: '#757575',
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  emptyButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  listContainer: {
    paddingBottom: 16,
  },
  prescriptionItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  prescriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prescriptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  prescriptionInfo: {
    flex: 1,
  },
  prescriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  prescriptionMedication: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  prescriptionDate: {
    fontSize: 12,
    color: '#9E9E9E',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  detailSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    width: 80,
  },
  detailValue: {
    fontSize: 16,
    color: '#424242',
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  medicationsSection: {
    marginTop: 16,
    marginBottom: 16,
  },
  medicationItem: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  medicationDosage: {
    fontSize: 14,
    color: '#616161',
    marginBottom: 4,
  },
  medicationInstructions: {
    fontSize: 14,
    color: '#757575',
  },
  notesSection: {
    marginBottom: 16,
  },
  notesText: {
    fontSize: 14,
    color: '#616161',
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
  },
  imageSection: {
    marginBottom: 16,
  },
  prescriptionImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 120,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default PrescriptionsScreen;
