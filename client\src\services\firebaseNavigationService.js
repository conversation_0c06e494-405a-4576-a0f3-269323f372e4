import { auth, db } from '../config/firebase';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  addDoc,
  updateDoc,
  deleteDoc,
  orderBy,
  limit,
  serverTimestamp
} from 'firebase/firestore';
import * as Location from 'expo-location';

/**
 * Service for managing navigation guidance data in Firebase
 */
export const firebaseNavigationService = {
  /**
   * Send navigation guidance to a patient
   * @param {Object} guidanceData - The guidance data to save
   * @returns {Promise<Object>} The saved guidance data with ID
   */
  sendPatientGuidance: async (guidanceData) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get caregiver name
      const caregiverDoc = await getDoc(doc(db, 'users', currentUser.uid));
      const caregiverName = caregiverDoc.exists()
        ? `${caregiverDoc.data().firstName || ''} ${caregiverDoc.data().lastName || ''}`.trim()
        : 'Your Caregiver';

      // Create guidance data
      const guidanceToSave = {
        ...guidanceData,
        caregiverId: currentUser.uid,
        caregiverName,
        status: 'sent',
        createdAt: serverTimestamp(),
      };

      // Save to Firebase
      const guidanceCollection = collection(db, 'patientGuidance');
      const docRef = await addDoc(guidanceCollection, guidanceToSave);

      // Get the newly created document to return with server timestamp
      const newDoc = await getDoc(docRef);
      const newDocData = newDoc.data();

      // Handle the case where timestamp might be a server timestamp object
      const result = {
        id: docRef.id,
        ...newDocData,
      };

      // Ensure timestamps are strings if they're not already
      if (newDocData.createdAt && typeof newDocData.createdAt.toDate === 'function') {
        result.createdAt = newDocData.createdAt.toDate().toISOString();
      }

      return result;
    } catch (error) {
      console.error('Error saving guidance to Firebase:', error);
      throw new Error('Failed to save guidance');
    }
  },

  /**
   * Get navigation guidance for a patient
   * @param {string} patientId - The ID of the patient
   * @param {string} status - Optional status filter ('sent', 'active', 'completed', 'cancelled')
   * @returns {Promise<Array>} Array of guidance objects
   */
  getPatientGuidance: async (patientId, status = null) => {
    try {
      const guidanceCollection = collection(db, 'patientGuidance');
      
      // Create query
      let q;
      if (status) {
        q = query(
          guidanceCollection,
          where('patientId', '==', patientId),
          where('status', '==', status),
          orderBy('createdAt', 'desc')
        );
      } else {
        q = query(
          guidanceCollection,
          where('patientId', '==', patientId),
          orderBy('createdAt', 'desc')
        );
      }

      // Execute query
      const snapshot = await getDocs(q);
      
      // Process results
      const guidance = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        guidance.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      return guidance;
    } catch (error) {
      console.error('Error getting patient guidance:', error);
      throw new Error('Failed to get guidance');
    }
  },

  /**
   * Get navigation guidance sent by a caregiver
   * @param {string} caregiverId - The ID of the caregiver
   * @param {string} status - Optional status filter ('sent', 'active', 'completed', 'cancelled')
   * @returns {Promise<Array>} Array of guidance objects
   */
  getCaregiverGuidance: async (caregiverId, status = null) => {
    try {
      const guidanceCollection = collection(db, 'patientGuidance');
      
      // Create query
      let q;
      if (status) {
        q = query(
          guidanceCollection,
          where('caregiverId', '==', caregiverId),
          where('status', '==', status),
          orderBy('createdAt', 'desc')
        );
      } else {
        q = query(
          guidanceCollection,
          where('caregiverId', '==', caregiverId),
          orderBy('createdAt', 'desc')
        );
      }

      // Execute query
      const snapshot = await getDocs(q);
      
      // Process results
      const guidance = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        guidance.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      return guidance;
    } catch (error) {
      console.error('Error getting caregiver guidance:', error);
      throw new Error('Failed to get guidance');
    }
  },

  /**
   * Update the status of a guidance
   * @param {string} guidanceId - The ID of the guidance to update
   * @param {string} status - The new status ('sent', 'active', 'completed', 'cancelled')
   * @returns {Promise<void>}
   */
  updateGuidanceStatus: async (guidanceId, status) => {
    try {
      const guidanceRef = doc(db, 'patientGuidance', guidanceId);
      await updateDoc(guidanceRef, {
        status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating guidance status:', error);
      throw new Error('Failed to update guidance status');
    }
  },

  /**
   * Calculate route between two points
   * @param {Object} origin - The origin coordinates {latitude, longitude}
   * @param {Object} destination - The destination coordinates {latitude, longitude}
   * @param {string} mode - The travel mode ('walking', 'driving', 'transit')
   * @returns {Promise<Object>} Route information including coordinates, distance, and duration
   */
  calculateRoute: async (origin, destination, mode) => {
    try {
      // This is a placeholder for route calculation
      // In a real app, you would use a routing API like Google Directions API
      
      // For now, we'll create a simple straight line between points
      const routeCoordinates = [origin, destination];
      
      // Calculate approximate distance and duration
      const distance = calculateDistance(
        origin.latitude, 
        origin.longitude, 
        destination.latitude, 
        destination.longitude
      );
      
      // Estimate duration based on mode and distance
      let duration;
      switch (mode) {
        case 'walking':
          // Assume walking speed of 5 km/h
          duration = Math.round((distance / 5) * 60);
          break;
        case 'driving':
          // Assume driving speed of 50 km/h
          duration = Math.round((distance / 50) * 60);
          break;
        case 'transit':
          // Assume transit speed of 30 km/h
          duration = Math.round((distance / 30) * 60);
          break;
        default:
          duration = Math.round((distance / 5) * 60);
      }
      
      return {
        routeCoordinates,
        distance,
        duration
      };
    } catch (error) {
      console.error('Error calculating route:', error);
      throw new Error('Failed to calculate route');
    }
  }
};

// Helper function to calculate distance between two coordinates in kilometers
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
};

const deg2rad = (deg) => {
  return deg * (Math.PI / 180);
};
