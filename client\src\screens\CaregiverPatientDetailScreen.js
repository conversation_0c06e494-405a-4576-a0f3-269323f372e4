import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Avatar } from 'react-native-paper';
import { ROLE_COLORS } from '../config/theme';
import { useAuth } from '../contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { showMessage } from 'react-native-flash-message';
import PatientVitalsViewer from '../components/dashboards/doctor/PatientVitalsViewer';
import PatientSymptomsViewer from '../components/dashboards/doctor/PatientSymptomsViewer';
import PatientMedicationsViewer from '../components/dashboards/doctor/PatientMedicationsViewer';
import PatientActivitiesViewer from '../components/dashboards/caregiver/PatientActivitiesViewer';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';

const CaregiverPatientDetailScreen = ({ route }) => {
  const { patient } = route.params;
  const navigation = useNavigation();
  const { user } = useAuth();
  const caregiverColors = ROLE_COLORS.caregiver;

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('vitals');
  const [patientDetails, setPatientDetails] = useState(patient);

  useEffect(() => {
    loadPatientDetails();
  }, []);

  const loadPatientDetails = async () => {
    try {
      setLoading(true);

      // Get the full patient document to ensure we have all details
      const patientDocRef = doc(db, 'users', patient.uid);
      const patientDocSnap = await getDoc(patientDocRef);

      if (patientDocSnap.exists()) {
        const patientData = patientDocSnap.data();
        setPatientDetails({
          ...patient,
          ...patientData
        });
      }
    } catch (error) {
      console.error('Error loading patient details:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load patient details',
        type: 'danger',
        backgroundColor: caregiverColors.primary,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPatientDetails();
    setRefreshing(false);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Render health data content based on active tab
  const renderHealthContent = () => {
    switch (activeTab) {
      case 'vitals':
        return (
          <PatientVitalsViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
          />
        );
      case 'symptoms':
        return (
          <PatientSymptomsViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
          />
        );
      case 'medications':
        return (
          <PatientMedicationsViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
          />
        );
      case 'activities':
        return (
          <PatientActivitiesViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
            onAddActivity={() => navigation.navigate('CaregiverRecordActivity', { patient })}
          />
        );
      default:
        return null;
    }
  };

  // Render the tab button
  const renderTabButton = (tabName, label, icon) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tabName && [styles.activeTabButton, { borderColor: caregiverColors.primary }]
      ]}
      onPress={() => handleTabChange(tabName)}
    >
      <Ionicons
        name={icon}
        size={20}
        color={activeTab === tabName ? caregiverColors.primary : '#888'}
      />
      <Text
        style={[
          styles.tabButtonText,
          activeTab === tabName && [styles.activeTabButtonText, { color: caregiverColors.primary }]
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={caregiverColors.primary} />
          <Text style={styles.loadingText}>Loading patient details...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[caregiverColors.primary]}
            />
          }
        >
          {/* Patient Header */}
          <View style={styles.patientHeader}>
            <Avatar.Text
              size={60}
              label={`${patient.firstName?.charAt(0) || ''}${patient.lastName?.charAt(0) || ''}`}
              backgroundColor={caregiverColors.primary}
              color="#fff"
              style={styles.avatar}
            />
            <View style={styles.patientInfo}>
              <Text style={styles.patientName}>{`${patient.firstName} ${patient.lastName}`}</Text>
              <Text style={styles.patientDetail}>{patient.email}</Text>
              <Text style={styles.patientDetail}>
                {patient.gender}, {patient.age || 'Age not specified'}
              </Text>
              <Text style={styles.patientCondition}>
                {patient.condition || 'No condition specified'}
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: caregiverColors.primary }]}
              onPress={() => navigation.navigate('CaregiverRecordVitals', { patient })}
            >
              <Ionicons name="add-circle" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Record Vitals</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: caregiverColors.primary }]}
              onPress={() => navigation.navigate('CaregiverRecordActivity', { patient })}
            >
              <Ionicons name="calendar" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Record Activity</Text>
            </TouchableOpacity>
          </View>

          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            {renderTabButton('vitals', 'Vitals', 'heart')}
            {renderTabButton('symptoms', 'Symptoms', 'medical')}
            {renderTabButton('medications', 'Medications', 'medkit')}
            {renderTabButton('activities', 'Activities', 'calendar')}
          </View>

          {/* Health Content */}
          <View style={styles.healthContentContainer}>
            <View style={styles.healthContentHeader}>
              <Text style={styles.healthContentTitle}>
                {`${patient.firstName}'s ${
                  activeTab === 'vitals'
                    ? 'Vitals'
                    : activeTab === 'symptoms'
                    ? 'Symptoms'
                    : activeTab === 'medications'
                    ? 'Medications'
                    : 'Activities'
                }`}
              </Text>
              <Text style={styles.healthContentSubtitle}>
                Monitor your patient's health metrics
              </Text>
            </View>
            {renderHealthContent()}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  patientHeader: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatar: {
    marginRight: 15,
  },
  patientInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  patientName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  patientDetail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  patientCondition: {
    fontSize: 14,
    fontWeight: '500',
    color: ROLE_COLORS.caregiver.primary,
    marginTop: 5,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    marginTop: 0,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#f5f5f5',
  },
  activeTabButton: {
    backgroundColor: '#f0f8ff',
  },
  tabButtonText: {
    marginLeft: 5,
    fontSize: 14,
    color: '#888',
  },
  activeTabButtonText: {
    fontWeight: '500',
  },
  healthContentContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 15,
    marginTop: 0,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  healthContentHeader: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 10,
  },
  healthContentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  healthContentSubtitle: {
    fontSize: 14,
    color: '#888',
    marginTop: 5,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 15,
    marginTop: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 0.48,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default CaregiverPatientDetailScreen;
