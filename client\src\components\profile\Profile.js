import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  BackHandler,
  ImageBackground
} from "react-native";
import CountryFlag from "react-native-country-flag";
import { Picker } from "@react-native-picker/picker";
import axios from "axios";
import { Ionicons } from '@expo/vector-icons'; // Assurez-vous d'avoir installé @expo/vector-icons
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '../../contexts/AuthContext';
import { firestore, auth, storage } from '../../config/firebase';
import { doc, getDoc, updateDoc, setDoc } from 'firebase/firestore';
import { updateProfile } from 'firebase/auth';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useNavigation } from '@react-navigation/native';
import { useTheme, Surface, Appbar } from 'react-native-paper';
import { getThemeForRole } from '../../config/theme';
import SharedProfileForm from './SharedProfileForm';
import UserQRCodeSection from './UserQRCodeSection';
import ProfileImageUploader from './ProfileImageUploader';
import { authAPI } from '../../config/api';

// Main component
const Profile = ({ route }) => {
  const { user, updateUserProfile } = useAuth();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState({});
  const [dataLoading, setDataLoading] = useState(true);
  const theme = getThemeForRole(user?.role || 'default');

  // Check if profile completion is required (passed from route params)
  const isRequired = route?.params?.required || false;

  // If profile is required, force editing mode
  useEffect(() => {
    if (isRequired) {
      setLoading(true);
    }
  }, [isRequired]);

  // Fetch user data on component mount
  useEffect(() => {
    if (user) {
      fetchUserData();
    } else {
      setDataLoading(false);
    }
  }, [user]);

  const fetchUserData = async () => {
    if (!user) return;

    try {
      const userRef = doc(firestore, 'users', user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        setInitialData(userData);
      }
    } catch (error) {
      showMessage({
        message: 'Error',
        description: 'Could not load user data',
        type: 'danger',
        backgroundColor: theme.colors.error,
      });
      console.error('Error fetching user data:', error);
    } finally {
      setDataLoading(false);
    }
  };

  const handleSubmit = async (formData) => {
    setLoading(true);

    try {
      // Show a progress message
      showMessage({
        message: 'Saving...',
        description: 'Updating your profile',
        type: 'info',
        backgroundColor: theme.colors.primary,
        duration: 2000,
      });

      // Update user profile in Firestore
      const userRef = doc(firestore, 'users', user.uid);
      const updatedData = {
        ...formData,
        updatedAt: new Date().toISOString()
      };

      // First update Firestore (this is most important)
      await setDoc(userRef, updatedData, { merge: true });

      // Update local user state right after Firestore success
      await updateUserProfile(updatedData);

      // Then try to update the backend API
      try {
        await authAPI.updateProfile(updatedData);
      } catch (apiError) {
        console.warn('API update failed, but Firestore updated successfully:', apiError);
        // Don't throw error here, we already updated Firestore
      }

      showMessage({
        message: 'Success',
        description: 'Your profile has been updated successfully',
        type: 'success',
        backgroundColor: theme.colors.primary,
      });

      // Refresh the data
      await fetchUserData();
    } catch (error) {
      showMessage({
        message: 'Error',
        description: 'Failed to update profile',
        type: 'danger',
        backgroundColor: theme.colors.error,
      });
      console.error('Profile update error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (dataLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Appbar.Header style={{ backgroundColor: theme.colors.primary }}>
        <Appbar.BackAction color="white" onPress={() => navigation.goBack()} />
        <Appbar.Content title="My Profile" color="white" />
      </Appbar.Header>

      <ScrollView style={styles.scrollView}>
        <Surface style={styles.content} elevation={0}>
          <UserQRCodeSection />

          <ProfileImageUploader />

          <SharedProfileForm
            initialData={initialData}
            onSubmit={handleSubmit}
            loading={loading}
            requiredFields={[]}
            buttonText="Update Profile"
            theme={theme}
            nonEditableFields={['firstName', 'lastName', 'email']} // Add this prop
          />
        </Surface>
      </ScrollView>
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    marginBottom: 20,
  },
});

export default Profile;