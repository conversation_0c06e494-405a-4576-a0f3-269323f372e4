import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Card, Title, Avatar, Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { getThemeForRole } from '../../config/theme';
import * as ImagePicker from 'expo-image-picker';
import { storage, auth, updateProfile } from '../../config/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

const ProfileImageUploader = () => {
  const { user, updateUserProfile } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');
  const [profileImage, setProfileImage] = useState(user?.photoURL || null);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [uploading, setUploading] = useState(false);

  // Request permissions when component mounts
  useEffect(() => {
    (async () => {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
        Alert.alert(
          'Permissions Required',
          'Please grant camera and photo library permissions to change your profile picture.',
          [{ text: 'OK' }]
        );
      }
    })();
  }, []);

  // Upload image to Firebase Storage and update user profile
  const uploadImageToFirebase = async (uri) => {
    if (!uri) {
      console.log('No URI provided for upload');
      return null;
    }

    try {
      console.log('Starting image upload process...');
      setUploading(true);

      // Save the image locally first
      console.log('Saving image locally first...');
      const timestamp = new Date().getTime();
      const filename = `profile_${timestamp}.jpg`;
      const localUri = uri;

      // Store the local URI in state for immediate display
      setProfileImage(localUri);

      // Create a reference to the storage location
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.error('No current user found');
        throw new Error('User not authenticated');
      }
      console.log('Current user ID:', currentUser.uid);

      // Try to upload to Firebase in the background
      try {
        console.log('Fetching image from URI:', uri.substring(0, 50) + '...');
        const response = await fetch(uri);
        console.log('Response received, converting to blob...');
        const blob = await response.blob();
        console.log('Blob created, size:', blob.size);

        console.log('Creating storage reference...');
        const storageRef = ref(storage, `profile_images/${currentUser.uid}`);
        console.log('Storage reference created:', storageRef);

        // Upload the blob
        console.log('Starting upload to Firebase Storage...');
        const uploadResult = await uploadBytes(storageRef, blob);
        console.log('Upload successful:', uploadResult);

        // Get the download URL
        console.log('Getting download URL...');
        const downloadURL = await getDownloadURL(storageRef);
        console.log('Download URL received:', downloadURL);

        // Update Firebase Auth profile
        console.log('Updating Firebase Auth profile...');
        await updateProfile(currentUser, { photoURL: downloadURL });
        console.log('Firebase Auth profile updated');

        // Update Firestore user document
        console.log('Updating Firestore user document...');
        await updateUserProfile({ photoURL: downloadURL });
        console.log('Firestore user document updated');

        console.log('Firebase upload completed successfully');
        return downloadURL;
      } catch (firebaseError) {
        console.error('Error with Firebase upload:', firebaseError);
        console.error('Error details:', firebaseError.message);

        // Even if Firebase upload fails, we still have the local image
        console.log('Using local image as fallback');

        // Update user profile with local URI
        await updateUserProfile({ photoURL: localUri });

        // Show a warning but don't treat it as a complete failure
        Alert.alert(
          'Warning',
          'Image saved locally but could not be uploaded to cloud storage. It may not be available on other devices.',
          [{ text: 'OK' }]
        );

        return localUri;
      }
    } catch (error) {
      console.error('Error in image processing:', error);
      console.error('Error details:', error.message);
      if (error.code) {
        console.error('Error code:', error.code);
      }
      if (error.stack) {
        console.error('Error stack:', error.stack);
      }

      Alert.alert(
        'Upload Failed',
        'Failed to process profile image: ' + error.message,
        [{ text: 'OK' }]
      );
      return null;
    } finally {
      setUploading(false);
    }
  };

  // Take a photo with the camera
  const handleTakePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!result.canceled) {
        const imageUri = result.assets[0].uri;
        setSelectedImage({ uri: imageUri });

        Alert.alert(
          'Use This Photo?',
          'Do you want to use this photo as your profile picture?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Confirm',
              onPress: async () => {
                const downloadURL = await uploadImageToFirebase(imageUri);
                if (downloadURL) {
                  setProfileImage(downloadURL);
                  setShowImagePickerModal(false);
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  // Pick an image from the gallery
  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!result.canceled) {
        const imageUri = result.assets[0].uri;
        setSelectedImage({ uri: imageUri });

        Alert.alert(
          'Use This Photo?',
          'Do you want to use this photo as your profile picture?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Confirm',
              onPress: async () => {
                const downloadURL = await uploadImageToFirebase(imageUri);
                if (downloadURL) {
                  setProfileImage(downloadURL);
                  setShowImagePickerModal(false);
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  return (
    <Card style={styles.card} elevation={1}>
      <Card.Content>
        <View style={styles.titleContainer}>
          <Title style={[styles.title, { color: theme.colors.primary }]}>Profile Picture</Title>
        </View>

        <View style={styles.profileImageContainer}>
          {profileImage ? (
            <Image
              source={{ uri: profileImage }}
              style={styles.profileImage}
            />
          ) : (
            <Avatar.Text
              size={120}
              label={user?.displayName?.substring(0, 2).toUpperCase() || "U"}
              style={{ backgroundColor: theme.colors.primary }}
            />
          )}

          <TouchableOpacity
            style={[styles.editButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => setShowImagePickerModal(true)}
          >
            <Ionicons name="camera" size={20} color="white" />
          </TouchableOpacity>
        </View>

        <Text style={styles.helperText}>
          Click on the camera icon to change your profile picture
        </Text>
      </Card.Content>

      {/* Image selection modal */}
      <Modal
        visible={showImagePickerModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImagePickerModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.profileModalContent}>
            <View style={styles.modalHeaderBar}>
              <Title style={styles.modalTitle}>Choose Profile Picture</Title>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowImagePickerModal(false)}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>



            {uploading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={styles.loadingText}>Uploading image...</Text>
              </View>
            ) : (
              <>
                <View style={styles.modalInstructions}>
                  <Text style={styles.modalInstructionsText}>
                    Select a method to set your profile picture
                  </Text>
                </View>

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.profileModalButton}
                    onPress={handleTakePhoto}
                  >
                    <View style={styles.profileButtonIconContainer}>
                      <Ionicons name="camera-outline" size={32} color="#555555" />
                    </View>
                    <Text style={styles.profileModalButtonText}>Take Photo</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.profileModalButton}
                    onPress={handlePickImage}
                  >
                    <View style={styles.profileButtonIconContainer}>
                      <Ionicons name="image-outline" size={32} color="#555555" />
                    </View>
                    <Text style={styles.profileModalButtonText}>Gallery</Text>
                  </TouchableOpacity>
                </View>

                {selectedImage && (
                  <View style={styles.previewContainer}>
                    <Text style={styles.previewTitle}>Preview</Text>
                    <Image
                      source={{ uri: selectedImage.uri }}
                      style={styles.previewImage}
                    />
                  </View>
                )}

                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowImagePickerModal(false)}
                  disabled={uploading}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 20,
    borderRadius: 8,
    elevation: 2,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  profileImageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  editButton: {
    position: 'absolute',
    bottom: 5,
    right: '32%',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  helperText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Profile modal specific styles
  profileModalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxWidth: 450,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  modalHeaderBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalDivider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    width: '100%',
  },
  modalInstructions: {
    padding: 15,
    alignItems: 'center',
  },
  modalInstructionsText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  closeButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    marginTop: 10,
    paddingHorizontal: 20,
  },
  // Profile modal specific button styles
  profileModalButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 10,
    borderRadius: 12,
    height: 120,
    backgroundColor: '#ffffff',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  profileButtonIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  profileModalButtonText: {
    color: '#333333',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  previewContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  previewImage: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  cancelButton: {
    padding: 15,
    margin: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#555',
    fontWeight: '600',
  },
});

export default ProfileImageUploader;
