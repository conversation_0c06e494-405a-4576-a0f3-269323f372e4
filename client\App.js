import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import { AuthProvider } from './src/contexts/AuthContext';
import { VitalsProvider } from './src/contexts/VitalsContext';
import { AppointmentProvider } from './src/contexts/AppointmentContext';
import { MedicationProvider } from './src/contexts/MedicationContext';
import AppNavigator from './src/navigation/AppNavigator';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet, LogBox, Text } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import FlashMessage from 'react-native-flash-message';
import { ThemeProvider } from './src/contexts/ThemeContext';
import VideoCallNotification from './src/components/video/VideoCallNotification';

// Firebase is already initialized in the components that need it

// Ignore specific LogBox warnings
LogBox.ignoreLogs([
  'Native module RNFBAppModule not found',
  'RCTBridge required dispatch_sync to load RNFBAppModule',
  'Cannot read property \'_context\' of undefined',
  'Native module cannot be null',
  'TypeError: Cannot read property',
  // Add any other warnings you want to ignore
]);

export default function App() {
  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <ThemeProvider>
          <PaperProvider>
            <AuthProvider>
              <VitalsProvider>
                <AppointmentProvider>
                  <MedicationProvider>
                    <NavigationContainer fallback={<Text>Loading...</Text>}>
                      <AppNavigator />
                      <VideoCallNotification />
                    </NavigationContainer>
                    <FlashMessage position="top" duration={3000} />
                  </MedicationProvider>
                </AppointmentProvider>
              </VitalsProvider>
            </AuthProvider>
          </PaperProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
