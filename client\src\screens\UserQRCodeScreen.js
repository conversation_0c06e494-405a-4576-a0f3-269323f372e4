import React, { useEffect } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { showMessage } from 'react-native-flash-message';
import { useAuth } from '../contexts/AuthContext';
import UserQRCode from '../components/profile/UserQRCode';
import { COLORS } from '../config/theme';

const UserQRCodeScreen = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  
  // Check if profile is incomplete
  useEffect(() => {
    if (!user?.profileCompleted) {
      showMessage({
        message: 'Profile Incomplete',
        description: 'Complete your profile to ensure your QR code works correctly with all healthcare providers.',
        type: 'info',
        backgroundColor: COLORS.primary,
        duration: 4000,
        onPress: () => navigation.navigate('Profile'),
      });
    }
  }, [user]);
  
  return (
    <View style={styles.container}>
      <UserQRCode />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default UserQRCodeScreen;