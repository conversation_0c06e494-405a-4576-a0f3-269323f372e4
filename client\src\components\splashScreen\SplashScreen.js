import React, { useEffect, useRef, useState } from 'react';
import { View, Image, StyleSheet, Animated, Text, ImageBackground } from 'react-native';

export default function SplashScreen({ onFinish }) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;
  const [animationsComplete, setAnimationsComplete] = useState(false);

  useEffect(() => {
    console.log('SplashScreen mounted, starting animations');
    
    // Create a flag to track if the component is mounted
    let isMounted = true;
    
    // Start the logo fade animation
    const logoAnimation = Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    });

    // Start the dot animations
    const dot1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(dot1Anim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(dot1Anim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    const dot2Animation = Animated.loop(
      Animated.sequence([
        Animated.delay(250),
        Animated.timing(dot2Anim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(dot2Anim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    const dot3Animation = Animated.loop(
      Animated.sequence([
        Animated.delay(500),
        Animated.timing(dot3Anim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(dot3Anim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    // Start all animations
    logoAnimation.start();
    dot1Animation.start();
    dot2Animation.start();
    dot3Animation.start();

    // Set a timer to complete after animations
    const timer = setTimeout(() => {
      if (isMounted) {
        console.log('SplashScreen timer complete, transitioning to main app');
        setAnimationsComplete(true);
        // Call onFinish with a slight delay to ensure all state changes are processed
        setTimeout(() => {
          if (isMounted && onFinish) {
            onFinish();
          }
        }, 100);
      }
    }, 2500); // Reduced from 3000 to speed up transition

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(timer);
      // Stop animations to prevent memory leaks
      logoAnimation.stop();
      dot1Animation.stop();
      dot2Animation.stop();
      dot3Animation.stop();
      console.log('SplashScreen unmounted, animations cleaned up');
    };
  }, [fadeAnim, dot1Anim, dot2Anim, dot3Anim, onFinish]);

  return (
    <ImageBackground
      source={require('.././../../assets/Backgrounds/pexels-pixabay-40568.jpg')}
      style={styles.background}
    >
      <View style={styles.container}>
        <Animated.View style={{ ...styles.logoContainer, opacity: fadeAnim }}>
          <Image
            source={require('../../../assets/LogoNeuroCare/Neuro_Care_rounded_3.png')}
            style={styles.logo}
          />
        </Animated.View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>loading </Text>
          <Animated.Text style={{ ...styles.loadingText, opacity: dot1Anim }}>.</Animated.Text>
          <Animated.Text style={{ ...styles.loadingText, opacity: dot2Anim }}>.</Animated.Text>
          <Animated.Text style={{ ...styles.loadingText, opacity: dot3Anim }}>.</Animated.Text>
        </View>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(2, 18, 2, 0.92)',
  },
  logoContainer: {
    marginBottom: 20,
  },
  logo: {
    width: 150,
    height: 150,
  },
  loadingContainer: {
    flexDirection: 'row',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
  },
});