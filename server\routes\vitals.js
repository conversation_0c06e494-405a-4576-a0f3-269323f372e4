const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const { verifyToken } = require('../middleware/auth');

// Initialize Firestore
const db = admin.firestore();

/**
 * @route POST /api/vitals/record
 * @desc Record a new vital sign
 * @access Private
 */
router.post('/record', verifyToken, async (req, res) => {
    try {
        const { vitalType, values, notes, recordMethod, recordType } = req.body;
        const userId = req.user.uid;

        // Validate required fields
        if (!vitalType || !values) {
            return res.status(400).json({ error: 'Vital type and values are required' });
        }

        // Create vital record
        const vitalRecord = {
            userId,
            vitalType,
            values,
            notes: notes || '',
            recordMethod: recordMethod || 'manual',
            recordType: recordType || 'self',
            timestamp: admin.firestore.FieldValue.serverTimestamp()
        };

        // Save to Firestore
        const docRef = await db.collection('vitals').add(vitalRecord);

        res.status(201).json({
            success: true,
            vitalId: docRef.id,
            vitalRecord: {
                ...vitalRecord,
                id: docRef.id,
                timestamp: new Date().toISOString() // Convert server timestamp to ISO string for response
            }
        });
    } catch (error) {
        console.error('Error recording vital sign:', error);
        res.status(500).json({ error: 'Failed to record vital sign' });
    }
});

/**
 * @route GET /api/vitals/user/:userId
 * @desc Get all vital records for a user
 * @access Private
 */
router.get('/user/:userId', verifyToken, async (req, res) => {
    try {
        const { userId } = req.params;
        const { vitalType, limit } = req.query;

        // Verify the requesting user has permission to access this data
        if (req.user.uid !== userId && req.user.role !== 'doctor' && req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Unauthorized to access this data' });
        }

        // Build query
        let query = db.collection('vitals').where('userId', '==', userId);
        
        // Filter by vital type if specified
        if (vitalType) {
            query = query.where('vitalType', '==', vitalType);
        }
        
        // Order by timestamp (newest first)
        query = query.orderBy('timestamp', 'desc');
        
        // Apply limit if specified
        if (limit && !isNaN(parseInt(limit))) {
            query = query.limit(parseInt(limit));
        }
        
        // Execute query
        const snapshot = await query.get();
        
        if (snapshot.empty) {
            return res.json([]);
        }
        
        // Transform data
        const vitals = [];
        snapshot.forEach(doc => {
            const data = doc.data();
            vitals.push({
                id: doc.id,
                ...data,
                timestamp: data.timestamp ? data.timestamp.toDate().toISOString() : new Date().toISOString()
            });
        });
        
        res.json(vitals);
    } catch (error) {
        console.error('Error fetching vital records:', error);
        res.status(500).json({ error: 'Failed to fetch vital records' });
    }
});

/**
 * @route GET /api/vitals/latest/:userId
 * @desc Get latest vital records for each type for a user
 * @access Private
 */
router.get('/latest/:userId', verifyToken, async (req, res) => {
    try {
        const { userId } = req.params;
        
        // Verify the requesting user has permission to access this data
        if (req.user.uid !== userId && req.user.role !== 'doctor' && req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Unauthorized to access this data' });
        }
        
        // Get all vital types
        const vitalTypes = ['heartRate', 'bloodPressure', 'bloodGlucose', 'weight'];
        const latestVitals = {};
        
        // For each vital type, get the latest record
        for (const vitalType of vitalTypes) {
            const snapshot = await db.collection('vitals')
                .where('userId', '==', userId)
                .where('vitalType', '==', vitalType)
                .orderBy('timestamp', 'desc')
                .limit(1)
                .get();
            
            if (!snapshot.empty) {
                const doc = snapshot.docs[0];
                const data = doc.data();
                latestVitals[vitalType] = {
                    id: doc.id,
                    ...data,
                    timestamp: data.timestamp ? data.timestamp.toDate().toISOString() : new Date().toISOString()
                };
            }
        }
        
        res.json(latestVitals);
    } catch (error) {
        console.error('Error fetching latest vital records:', error);
        res.status(500).json({ error: 'Failed to fetch latest vital records' });
    }
});

/**
 * @route DELETE /api/vitals/:vitalId
 * @desc Delete a vital record
 * @access Private
 */
router.delete('/:vitalId', verifyToken, async (req, res) => {
    try {
        const { vitalId } = req.params;
        
        // Get the vital record
        const vitalDoc = await db.collection('vitals').doc(vitalId).get();
        
        if (!vitalDoc.exists) {
            return res.status(404).json({ error: 'Vital record not found' });
        }
        
        const vitalData = vitalDoc.data();
        
        // Verify the requesting user has permission to delete this record
        if (vitalData.userId !== req.user.uid && req.user.role !== 'admin') {
            return res.status(403).json({ error: 'Unauthorized to delete this record' });
        }
        
        // Delete the record
        await db.collection('vitals').doc(vitalId).delete();
        
        res.json({ success: true, message: 'Vital record deleted successfully' });
    } catch (error) {
        console.error('Error deleting vital record:', error);
        res.status(500).json({ error: 'Failed to delete vital record' });
    }
});

module.exports = router;
