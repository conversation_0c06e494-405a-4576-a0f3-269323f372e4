import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Dimensions,
  Image
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { usersAPI } from '../config/api';
import { useAuth } from '../contexts/AuthContext';
import { showMessage } from 'react-native-flash-message';
import { ROLE_COLORS } from '../config/theme';
import * as ImagePicker from 'expo-image-picker';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';

const { width } = Dimensions.get('window');
const qrSize = width * 0.7;

const ScanCaregiverQRScreen = () => {
  // State variables
  const [loading, setLoading] = useState(false);
  const [caregiverCode, setCaregiverCode] = useState('');
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);

  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const supervisorColors = ROLE_COLORS.supervisor;

  // Get patient ID from route params
  const patientId = route.params?.patientId;

  // Check if we should show camera or manual entry
  const mode = route.params?.mode || 'manual';
  const showCamera = mode === 'camera';

  // Validate code format
  const isValidCode = (code) => {
    return /^[A-Z0-9]{8}$/.test(code);
  };

  // Handle code input change
  const handleCodeChange = (text) => {
    // Convert to uppercase and remove non-alphanumeric characters
    const formattedCode = text.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setCaregiverCode(formattedCode);
  };

  // Manual code submission
  const handleSubmit = () => {
    if (!isValidCode(caregiverCode)) {
      showMessage({
        message: 'Invalid Code Format',
        description: 'Please enter a valid 8-character code (letters and numbers only)',
        type: 'danger',
        backgroundColor: '#4A148C',
      });
      return;
    }

    processUserCode(caregiverCode);
  };

  // Request camera permissions when component mounts
  useEffect(() => {
    if (showCamera) {
      const getBarCodeScannerPermissions = async () => {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');

        if (status !== 'granted') {
          showMessage({
            message: 'Camera Permission Denied',
            description: 'Please grant camera permissions to scan QR codes',
            type: 'warning',
            backgroundColor: '#4A148C',
          });
        }
      };

      getBarCodeScannerPermissions();
    }
  }, [showCamera]);

  // Handle taking a picture
  const takePicture = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        // In a real app, you would process the image to extract QR code
        // For now, we'll just simulate a successful scan
        showMessage({
          message: 'QR Code Captured',
          description: 'Processing the image...',
          type: 'info',
          backgroundColor: '#4A148C',
        });

        setScanned(true);

        // Simulate finding a valid code after a delay
        setTimeout(() => {
          // Generate a random 8-character code
          const randomCode = Math.random().toString(36).substring(2, 10).toUpperCase();
          processUserCode(randomCode);
        }, 2000);
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to capture image',
        type: 'danger',
        backgroundColor: '#4A148C',
      });
    }
  };

  // Process a user code (from QR or manual entry)
  const processUserCode = async (code) => {
    if (!patientId) {
      showMessage({
        message: 'Error',
        description: 'Patient ID is missing. Please try again.',
        type: 'danger',
        backgroundColor: supervisorColors.primary,
      });
      return;
    }

    setLoading(true);

    try {
      console.log('Processing code:', code);

      // Verify the code belongs to a caregiver
      const userResponse = await usersAPI.getUserByCode(code);
      console.log('User found:', userResponse);

      if (!userResponse || userResponse.role !== 'caregiver') {
        showMessage({
          message: 'Invalid User',
          description: 'This code does not belong to a caregiver',
          type: 'danger',
          backgroundColor: '#4A148C',
        });
        setLoading(false);
        setScanned(false);
        return;
      }

      try {
        // Assign caregiver to patient
        const result = await firebaseCaregiverService.assignCaregiverByCode(patientId, code);

        showMessage({
          message: 'Success',
          description: `Caregiver ${userResponse.displayName} assigned successfully`,
          type: 'success',
          backgroundColor: '#4A148C',
        });

        // Go back to the appropriate screen
        setTimeout(() => {
          const returnScreen = route.params?.returnScreen;
          if (returnScreen === 'SupervisorCaregiverManagement') {
            navigation.navigate('SupervisorCaregiverManagement');
          } else {
            navigation.goBack();
          }
        }, 1500);
      } catch (assignError) {
        console.error('Error assigning caregiver:', assignError);

        let errorMessage = 'Failed to assign caregiver. Please try again.';

        // Check for specific error conditions
        if (assignError.response?.status === 400) {
          errorMessage = assignError.response.data.error || 'Invalid request. Please check your inputs.';
        } else if (assignError.response?.status === 403) {
          errorMessage = assignError.response.data.error || 'You do not have permission to assign caregivers to this patient.';
        } else if (assignError.response?.status === 404) {
          if (assignError.response.data.error === 'No caregiver found with this code') {
            errorMessage = 'No caregiver found with this code.';
          } else if (assignError.response.data.error === 'Patient not found') {
            errorMessage = 'Patient not found.';
          } else {
            errorMessage = assignError.response.data.error || 'Resource not found.';
          }
        } else if (assignError.response?.data?.error) {
          errorMessage = assignError.response.data.error;
        } else if (assignError.message) {
          errorMessage = assignError.message;
        }

        showMessage({
          message: 'Error',
          description: errorMessage,
          type: 'danger',
          backgroundColor: '#4A148C',
        });
      }
    } catch (error) {
      console.error('Error processing code:', error);
      let errorMessage = 'Failed to find caregiver. Please try again.';

      if (error?.response?.status === 404) {
        errorMessage = 'Invalid code. No caregiver found with this code.';
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      showMessage({
        message: 'Error',
        description: errorMessage,
        type: 'danger',
        backgroundColor: '#4A148C',
      });
    } finally {
      setLoading(false);
      setScanned(false);
    }
  };

  // Camera mode
  if (showCamera) {
    // If we don't have permission yet, show a loading screen
    if (hasPermission === null) {
      return (
        <View style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4A148C" />
            <Text style={styles.loadingText}>Requesting camera permission...</Text>
          </View>
        </View>
      );
    }

    // If permission was denied, show an error message
    if (hasPermission === false) {
      return (
        <View style={styles.container}>
          <View style={styles.errorContainer}>
            <Ionicons name="camera-off" size={60} color="#FF5252" />
            <Text style={styles.errorTitle}>Camera Access Denied</Text>
            <Text style={styles.errorText}>
              Please enable camera access in your device settings to scan QR codes.
            </Text>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#4A148C', marginTop: 20 }]}
              onPress={() => navigation.goBack()}
            >
              <Text style={[styles.buttonText, { color: '#fff' }]}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // If we have permission, show the camera interface
    return (
      <View style={styles.container}>
        <View style={styles.cameraContainer}>
          <View style={styles.scanArea}>
            {scanned && !loading && (
              <View style={styles.scanningAnimation}>
                <ActivityIndicator size="large" color="#fff" />
                <Text style={styles.scanningText}>Processing...</Text>
              </View>
            )}
          </View>

          <TouchableOpacity
            style={styles.captureButton}
            onPress={takePicture}
            disabled={scanned}
          >
            <Ionicons name="camera" size={32} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Informational note for users */}
        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            Position the caregiver's QR code within the frame to scan.
          </Text>
        </View>

        <View style={styles.footer}>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#f5f5f5' }]}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>

            {scanned && !loading && (
              <TouchableOpacity
                style={[styles.button, { backgroundColor: '#4A148C' }]}
                onPress={() => setScanned(false)}
              >
                <Text style={[styles.buttonText, { color: '#fff' }]}>Scan Again</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#f5f5f5' }]}
              onPress={() => navigation.navigate('ScanCaregiverQRScreen', { mode: 'manual', patientId })}
            >
              <Text style={styles.buttonText}>Enter Manually</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  // Manual entry mode
  return (
    <View style={styles.container}>
      <View style={styles.formContainer}>
        <Ionicons name="qr-code" size={80} color="#4A148C" style={styles.qrIcon} />

        <Text style={styles.title}>Add Caregiver by Code</Text>
        <Text style={styles.subtitle}>
          Enter the 8-character code from caregiver's profile
        </Text>

        <TextInput
          style={styles.input}
          value={caregiverCode}
          onChangeText={handleCodeChange}
          placeholder="Enter code (e.g. ABCD1234)"
          placeholderTextColor="#999"
          autoCapitalize="characters"
          maxLength={8}
          keyboardType="default"
        />

        {loading && (
          <ActivityIndicator
            size="large"
            color="#4A148C"
            style={styles.loader}
          />
        )}

        <TouchableOpacity
          style={[
            styles.button,
            isValidCode(caregiverCode) && !loading ?
              { backgroundColor: '#fff', borderWidth: 2, borderColor: '#4A148C' } :
              styles.buttonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!isValidCode(caregiverCode) || loading}
        >
          <Text style={[
            styles.buttonText,
            { color: isValidCode(caregiverCode) ? '#4A148C' : '#999' }
          ]}>
            {isValidCode(caregiverCode) ? 'Assign Caregiver' : 'Enter Valid Code'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
          disabled={loading}
        >
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  cameraContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  captureButton: {
    position: 'absolute',
    bottom: 40,
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#4A148C',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  scanArea: {
    width: qrSize,
    height: qrSize,
    borderWidth: 2,
    borderColor: '#fff',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningAnimation: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  captureButton: {
    position: 'absolute',
    bottom: 40,
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#4A148C',
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoBox: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 16,
    position: 'absolute',
    top: 40,
    left: 20,
    right: 20,
    borderRadius: 8,
  },
  infoText: {
    color: '#fff',
    textAlign: 'center',
  },
  footer: {
    backgroundColor: '#fff',
    padding: 16,
    alignItems: 'center',
  },
  footerText: {
    color: '#666',
    marginBottom: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  formContainer: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrIcon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    width: '100%',
    height: 60,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 20,
    letterSpacing: 2,
    textAlign: 'center',
    marginBottom: 24,
  },
  loader: {
    marginVertical: 20,
  },
  button: {
    width: '100%',
    height: 56,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonDisabled: {
    backgroundColor: '#f5f5f5',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    padding: 12,
  },
  cancelText: {
    color: '#666',
    fontSize: 16,
  },
});

export default ScanCaregiverQRScreen;
