import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
  Dimensions,
  Image,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Avatar, Button } from 'react-native-paper';
import * as Location from 'expo-location';
import { ROLE_COLORS } from '../config/theme';
import { firebaseLocationService } from '../services/firebaseLocationService';
import { collection, query, where, onSnapshot, orderBy, limit, doc, getDoc } from 'firebase/firestore';
import { db, auth } from '../config/firebase';

const { width, height } = Dimensions.get('window');

const SupervisorPatientTrackingScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const supervisorColors = ROLE_COLORS.supervisor;

  // State variables
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [currentLocation, setCurrentLocation] = useState(null);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [patientLocations, setPatientLocations] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  // References
  const locationSubscription = useRef(null);
  const patientLocationsListener = useRef(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
    return () => {
      // Clean up listeners when component unmounts
      if (patientLocationsListener.current) {
        patientLocationsListener.current();
      }
    };
  }, []);

  // Effect to listen for patient location updates when a patient is selected
  useEffect(() => {
    if (selectedPatient) {
      listenToPatientLocations(selectedPatient.uid);
    } else {
      // Clean up listener if no patient is selected
      if (patientLocationsListener.current) {
        patientLocationsListener.current();
        patientLocationsListener.current = null;
      }
    }
  }, [selectedPatient]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMessage('Permission to access location was denied');
        setLoading(false);
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      if (location) {
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      }

      // Load linked patients
      await loadLinkedPatients();
    } catch (error) {
      console.error('Error loading initial data:', error);
      setErrorMessage('Failed to load tracking data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadLinkedPatients = async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        setPatients([]);
        return;
      }

      // Get details for each linked patient
      const patientsData = [];
      for (const patientId of linkedPatientIds) {
        const patientDocRef = doc(db, 'users', patientId);
        const patientDocSnap = await getDoc(patientDocRef);
        if (patientDocSnap.exists()) {
          const patientData = patientDocSnap.data();
          patientsData.push({
            uid: patientId,
            ...patientData
          });
        }
      }

      setPatients(patientsData);
    } catch (error) {
      console.error('Error loading linked patients:', error);
      Alert.alert('Error', 'Failed to load patient data. Please try again.');
    }
  };

  const listenToPatientLocations = (patientId) => {
    // Clean up previous listener if exists
    if (patientLocationsListener.current) {
      patientLocationsListener.current();
    }

    // Create a query for the patient's locations
    const locationsCollection = collection(db, 'patientLocations');
    const q = query(
      locationsCollection,
      where('userId', '==', patientId),
      orderBy('createdAt', 'desc'),
      limit(10)
    );

    // Set up real-time listener
    patientLocationsListener.current = onSnapshot(q, (snapshot) => {
      const locations = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        locations.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      setPatientLocations(locations);
    }, (error) => {
      console.error('Error listening to patient locations:', error);
      Alert.alert('Error', 'Failed to get real-time location updates.');
    });
  };

  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
  };

  const handleSendGuidance = (patient) => {
    // Navigate to guidance screen
    navigation.navigate('SupervisorGuidanceScreen', { patient });
  };

  const renderPatientItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.patientCard,
        selectedPatient?.uid === item.uid && styles.selectedPatientCard
      ]}
      onPress={() => handlePatientSelect(item)}
    >
      <View style={styles.patientCardContent}>
        <Avatar.Text
          size={50}
          label={`${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`}
          backgroundColor={supervisorColors.primary}
          color="#fff"
        />
        <View style={styles.patientInfo}>
          <Text style={styles.patientName}>{`${item.firstName || ''} ${item.lastName || ''}`}</Text>
          <Text style={styles.patientDetails}>{item.condition || 'No condition specified'}</Text>
        </View>
      </View>

      <TouchableOpacity
        style={styles.guidanceButton}
        onPress={() => handleSendGuidance(item)}
      >
        <Ionicons name="navigate" size={24} color={supervisorColors.primary} />
        <Text style={styles.guidanceButtonText}>Guide</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  // Render loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={supervisorColors.primary} />
        <Text style={styles.loadingText}>Loading patient tracking...</Text>
      </View>
    );
  }

  // Get static map URL for current location
  const getStaticMapUrl = (latitude, longitude, zoom = 14, width = 600, height = 300) => {
    return `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&zoom=${zoom}&size=${width}x${height}&markers=color:red%7C${latitude},${longitude}&key=YOUR_API_KEY`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Patient Location Tracking</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={loadInitialData}
        >
          <Ionicons name="refresh" size={24} color={supervisorColors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.patientListContainer}>
        <Text style={styles.sectionTitle}>Your Patients</Text>
        {patients.length > 0 ? (
          <FlatList
            data={patients}
            renderItem={renderPatientItem}
            keyExtractor={(item) => item.uid}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.patientList}
          />
        ) : (
          <View style={styles.emptyPatients}>
            <Ionicons name="people" size={40} color="#ccc" />
            <Text style={styles.emptyPatientsText}>No patients connected</Text>
            <TouchableOpacity
              style={styles.addPatientButton}
              onPress={() => navigation.navigate('Patients')}
            >
              <Text style={styles.addPatientButtonText}>Add Patients</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Map View (Static Image) */}
      <View style={styles.mapContainer}>
        {selectedPatient ? (
          patientLocations.length > 0 ? (
            <View style={styles.staticMapContainer}>
              <Text style={styles.mapTitle}>
                {`${selectedPatient.firstName}'s Location`}
              </Text>
              <Text style={styles.mapPlaceholder}>
                Last known location:{"\n"}
                Latitude: {patientLocations[0].latitude?.toFixed(6) || 'N/A'}{"\n"}
                Longitude: {patientLocations[0].longitude?.toFixed(6) || 'N/A'}{"\n"}
                Time: {new Date(patientLocations[0].createdAt).toLocaleTimeString()}
              </Text>
            </View>
          ) : (
            <View style={styles.noLocationContainer}>
              <Ionicons name="location-outline" size={60} color="#ccc" />
              <Text style={styles.noLocationText}>
                No location data available for {selectedPatient.firstName}
              </Text>
            </View>
          )
        ) : (
          <View style={styles.noPatientSelectedContainer}>
            <Ionicons name="person-outline" size={60} color="#ccc" />
            <Text style={styles.noPatientSelectedText}>
              Select a patient to view their location
            </Text>
          </View>
        )}
      </View>

      {selectedPatient && (
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleSendGuidance(selectedPatient)}
          >
            <Ionicons name="navigate" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Send Guidance</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
            onPress={() => navigation.navigate('SupervisorGuidanceScreen', { patient: selectedPatient })}
          >
            <Ionicons name="map" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Create Route</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  patientListContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  patientList: {
    paddingVertical: 8,
  },
  patientCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    width: 220,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedPatientCard: {
    borderWidth: 2,
    borderColor: ROLE_COLORS.supervisor.primary,
  },
  patientCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  patientInfo: {
    marginLeft: 12,
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  patientDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  guidanceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 8,
  },
  guidanceButtonText: {
    marginLeft: 8,
    color: ROLE_COLORS.supervisor.primary,
    fontWeight: '600',
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  staticMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  mapPlaceholder: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    lineHeight: 24,
  },
  noLocationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  noLocationText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginTop: 16,
  },
  noPatientSelectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  noPatientSelectedText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginTop: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ROLE_COLORS.supervisor.primary,
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyPatients: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  emptyPatientsText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    marginBottom: 16,
  },
  addPatientButton: {
    backgroundColor: ROLE_COLORS.supervisor.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  addPatientButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default SupervisorPatientTrackingScreen;
