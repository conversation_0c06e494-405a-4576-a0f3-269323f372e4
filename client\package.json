{"name": "neurocare-client", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-firebase/app": "^21.14.0", "@react-native-firebase/messaging": "^21.14.0", "@react-native-picker/picker": "^2.9.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "axios": "^1.6.7", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-av": "^15.0.2", "expo-barcode-scanner": "^13.0.1", "expo-camera": "~16.0.18", "expo-checkbox": "^4.0.1", "expo-file-system": "~18.0.12", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-location": "^18.0.10", "expo-speech": "^13.0.1", "expo-status-bar": "~2.0.1", "firebase": "^10.8.0", "react": "18.3.1", "react-native": "^0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-country-flag": "^1.1.9", "react-native-feather": "^1.1.2", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-paper": "^5.6.0", "react-native-popup-menu": "^0.17.0", "react-native-qrcode-svg": "^6.2.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.8.0", "react-native-vector-icons": "^10.2.0", "react-native-webrtc": "^124.0.5", "react-native-webview": "^13.12.5"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^5.0.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}, "private": true}