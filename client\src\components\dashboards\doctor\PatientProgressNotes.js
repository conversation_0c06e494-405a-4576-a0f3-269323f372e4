import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { Card } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import axios from 'axios';
import { format } from 'date-fns';

const PatientProgressNotes = ({ patientId, token }) => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
    followUpRequired: false,
    followUpNotes: '',
  });

  useEffect(() => {
    if (patientId) {
      fetchProgressNotes();
    }
  }, [patientId]);

  const fetchProgressNotes = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/medical/progress-notes/${patientId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setNotes(response.data);
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch progress notes');
      console.error('Error fetching progress notes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddNote = async () => {
    try {
      if (!newNote.title || !newNote.content) {
        Alert.alert('Error', 'Please fill in all required fields');
        return;
      }

      const response = await axios.post(
        '/api/medical/progress-notes',
        {
          patientId,
          ...newNote,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      setNotes([response.data, ...notes]);
      setModalVisible(false);
      setNewNote({
        title: '',
        content: '',
        followUpRequired: false,
        followUpNotes: '',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to add progress note');
      console.error('Error adding progress note:', error);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setModalVisible(true)}
      >
        <Ionicons name="add-circle" size={24} color="#007AFF" />
        <Text style={styles.addButtonText}>Add Progress Note</Text>
      </TouchableOpacity>

      <ScrollView style={styles.notesList}>
        {notes.map((note) => (
          <Card key={note.id} style={styles.noteCard}>
            <Card.Content>
              <View style={styles.noteHeader}>
                <Text style={styles.noteTitle}>{note.title}</Text>
                <Text style={styles.noteDate}>
                  {format(new Date(note.createdAt), 'MMM dd, yyyy')}
                </Text>
              </View>
              <Text style={styles.noteContent}>{note.content}</Text>
              {note.followUpRequired && (
                <View style={styles.followUpContainer}>
                  <Text style={styles.followUpLabel}>Follow-up Required:</Text>
                  <Text style={styles.followUpNotes}>{note.followUpNotes}</Text>
                </View>
              )}
            </Card.Content>
          </Card>
        ))}
      </ScrollView>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Add Progress Note</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Title"
              value={newNote.title}
              onChangeText={(text) => setNewNote({ ...newNote, title: text })}
            />

            <TextInput
              style={[styles.input, styles.contentInput]}
              placeholder="Content"
              value={newNote.content}
              onChangeText={(text) => setNewNote({ ...newNote, content: text })}
              multiline
              numberOfLines={4}
            />

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() =>
                setNewNote({
                  ...newNote,
                  followUpRequired: !newNote.followUpRequired,
                })
              }
            >
              <Ionicons
                name={newNote.followUpRequired ? 'checkbox' : 'square-outline'}
                size={24}
                color="#007AFF"
              />
              <Text style={styles.checkboxLabel}>Follow-up Required</Text>
            </TouchableOpacity>

            {newNote.followUpRequired && (
              <TextInput
                style={[styles.input, styles.contentInput]}
                placeholder="Follow-up Notes"
                value={newNote.followUpNotes}
                onChangeText={(text) =>
                  setNewNote({ ...newNote, followUpNotes: text })
                }
                multiline
                numberOfLines={2}
              />
            )}

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleAddNote}
              >
                <Text style={[styles.buttonText, styles.saveButtonText]}>
                  Save
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    marginBottom: 10,
  },
  addButtonText: {
    marginLeft: 8,
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
  notesList: {
    flex: 1,
  },
  noteCard: {
    marginBottom: 10,
    elevation: 2,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  noteDate: {
    color: '#666',
    fontSize: 14,
  },
  noteContent: {
    fontSize: 16,
    lineHeight: 24,
  },
  followUpContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
  },
  followUpLabel: {
    fontWeight: '600',
    marginBottom: 5,
  },
  followUpNotes: {
    fontSize: 14,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
    fontSize: 16,
  },
  contentInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  checkboxLabel: {
    marginLeft: 8,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  buttonText: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButtonText: {
    color: 'white',
  },
});

export default PatientProgressNotes; 