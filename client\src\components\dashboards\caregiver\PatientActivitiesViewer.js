import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card } from 'react-native-paper';
import { ROLE_COLORS } from '../../../config/theme';
import { firebaseCaregiverService } from '../../../services/firebaseCaregiverService';

const PatientActivitiesViewer = ({ patientId, patientName, onAddActivity }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activities, setActivities] = useState([]);
  const [selectedType, setSelectedType] = useState('all');
  const caregiverColors = ROLE_COLORS.caregiver;

  // Activity type filters
  const activityTypes = [
    { id: 'all', label: 'All', icon: 'list' },
    { id: 'medication', label: 'Medication', icon: 'medkit' },
    { id: 'exercise', label: 'Exercise', icon: 'fitness-outline' },
    { id: 'social', label: 'Social', icon: 'people' },
    { id: 'cognitive', label: 'Cognitive', icon: 'book' },
    { id: 'hygiene', label: 'Hygiene', icon: 'water-outline' },
    { id: 'nutrition', label: 'Nutrition', icon: 'restaurant' },
    { id: 'sleep', label: 'Sleep', icon: 'moon' },
    { id: 'other', label: 'Other', icon: 'list' }
  ];

  // Load activities on component mount
  useEffect(() => {
    loadActivities();
  }, [patientId]);

  // Load activities when filter changes
  useEffect(() => {
    loadActivities();
  }, [selectedType]);

  const loadActivities = async () => {
    if (!patientId) return;

    try {
      setLoading(true);
      const activityType = selectedType === 'all' ? null : selectedType;
      const data = await firebaseCaregiverService.getPatientActivities(patientId, activityType);
      setActivities(data);
    } catch (error) {
      console.error('Error loading patient activities:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadActivities();
  };

  const handleFilterChange = (type) => {
    setSelectedType(type);
  };

  // Get icon for activity type
  const getActivityIcon = (type) => {
    const activityType = activityTypes.find(t => t.id === type);
    return activityType ? activityType.icon : 'list';
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);

    // Format date in a more readable way
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    let dateLabel;
    if (date.toDateString() === today.toDateString()) {
      dateLabel = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      dateLabel = 'Yesterday';
    } else {
      dateLabel = date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }

    // Format time
    const timeString = date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return { dateLabel, timeString };
  };

  // Render activity item
  const renderActivityItem = ({ item }) => {
    const { dateLabel, timeString } = formatDate(item.timestamp);

    return (
      <Card style={styles.activityCard}>
        <View style={styles.activityHeader}>
          <View style={[styles.activityTypeIcon, { backgroundColor: caregiverColors.primary }]}>
            <Ionicons name={getActivityIcon(item.type)} size={20} color="#fff" />
          </View>
          <View style={styles.activityHeaderContent}>
            <Text style={styles.activityType}>{item.type.charAt(0).toUpperCase() + item.type.slice(1)}</Text>
            <View style={styles.activityTimeContainer}>
              <Ionicons name="calendar-outline" size={12} color="#757575" style={styles.activityTimeIcon} />
              <Text style={styles.activityTime}>{dateLabel} at {timeString}</Text>
            </View>
          </View>
          <View style={[styles.activityStatus, { backgroundColor: item.completed ? '#4CAF50' : '#FFC107' }]}>
            <Text style={styles.activityStatusText}>{item.completed ? 'Completed' : 'Not Completed'}</Text>
          </View>
        </View>
        <View style={styles.activityContent}>
          <Text style={styles.activityDescription}>{item.description}</Text>
          {item.duration > 0 && (
            <Text style={styles.activityDuration}>Duration: {item.duration} minutes</Text>
          )}
          {item.notes && (
            <View style={styles.activityNotes}>
              <Text style={styles.activityNotesLabel}>Notes:</Text>
              <Text style={styles.activityNotesText}>{item.notes}</Text>
            </View>
          )}
        </View>
      </Card>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="calendar-outline" size={60} color="#ccc" />
      <Text style={styles.emptyText}>No activities recorded</Text>
      <Text style={styles.emptySubtext}>
        Record daily activities to track patient progress
      </Text>
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: caregiverColors.primary }]}
        onPress={onAddActivity}
      >
        <Ionicons name="add" size={20} color="#fff" />
        <Text style={styles.addButtonText}>Record New Activity</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScrollView}>
          {activityTypes.map((type) => (
            <TouchableOpacity
              key={type.id}
              style={[
                styles.filterButton,
                selectedType === type.id && { backgroundColor: caregiverColors.primary }
              ]}
              onPress={() => handleFilterChange(type.id)}
            >
              <Ionicons
                name={type.icon}
                size={16}
                color={selectedType === type.id ? '#fff' : '#757575'}
              />
              <Text
                style={[
                  styles.filterButtonText,
                  selectedType === type.id && { color: '#fff' }
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={caregiverColors.primary} />
          <Text style={styles.loadingText}>Loading activities...</Text>
        </View>
      ) : (
        <>
          <View style={styles.headerContainer}>
            <Text style={styles.headerTitle}>
              {selectedType === 'all' ? 'All Activities' : `${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)} Activities`}
            </Text>
            <TouchableOpacity
              style={styles.addActivityButton}
              onPress={onAddActivity}
            >
              <Ionicons name="add-circle" size={24} color={caregiverColors.primary} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={activities}
            renderItem={renderActivityItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={activities.length === 0 ? styles.emptyList : styles.list}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[caregiverColors.primary]}
              />
            }
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  filterContainer: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterScrollView: {
    flexDirection: 'row',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#757575',
    marginLeft: 4,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
  },
  addActivityButton: {
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#757575',
  },
  list: {
    padding: 16,
  },
  emptyList: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  activityCard: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  activityTypeIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityHeaderContent: {
    flex: 1,
  },
  activityType: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
  },
  activityTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  activityTimeIcon: {
    marginRight: 4,
  },
  activityTime: {
    fontSize: 12,
    color: '#757575',
  },
  activityStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  activityStatusText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  activityContent: {
    padding: 12,
  },
  activityDescription: {
    fontSize: 14,
    color: '#424242',
    marginBottom: 8,
  },
  activityDuration: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 8,
  },
  activityNotes: {
    backgroundColor: '#f5f5f5',
    padding: 8,
    borderRadius: 4,
  },
  activityNotesLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#616161',
    marginBottom: 4,
  },
  activityNotesText: {
    fontSize: 12,
    color: '#616161',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#757575',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9e9e9e',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default PatientActivitiesViewer;
