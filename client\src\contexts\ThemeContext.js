import React, { createContext, useState, useContext, useEffect } from 'react';

const ThemeContext = createContext({});

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const [loading, setLoading] = useState(true);

  // Function to toggle between light and dark themes
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    // Save to async storage or other persistent storage
    saveThemePreference(newTheme);
  };

  // Function to set a specific theme
  const changeTheme = (newTheme) => {
    if (newTheme === 'light' || newTheme === 'dark') {
      setTheme(newTheme);
      // Save to async storage or other persistent storage
      saveThemePreference(newTheme);
    }
  };

  // Save theme preference to persistent storage (AsyncStorage in this case)
  const saveThemePreference = async (themeValue) => {
    try {
      // For a React Native app, you would typically use AsyncStorage
      // This is a placeholder - implement the actual storage method you're using
      // await AsyncStorage.setItem('theme', themeValue);
      console.log('Theme preference saved:', themeValue);
    } catch (error) {
      console.error('Failed to save theme preference:', error);
    }
  };

  // Load the saved theme preference on component mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        // For a React Native app, you would typically use AsyncStorage
        // This is a placeholder - implement the actual storage method you're using
        // const savedTheme = await AsyncStorage.getItem('theme');
        // if (savedTheme) {
        //   setTheme(savedTheme);
        // }
        setLoading(false);
      } catch (error) {
        console.error('Failed to load theme preference:', error);
        setLoading(false);
      }
    };

    loadThemePreference();
  }, []);

  // Theme values
  const lightTheme = {
    background: '#FFFFFF',
    text: '#121212',
    primary: '#4F46E5',
    secondary: '#8B5CF6',
    accent: '#10B981',
    error: '#EF4444',
    border: '#E5E7EB',
    card: '#F9FAFB',
  };

  const darkTheme = {
    background: '#121212',
    text: '#F9FAFB',
    primary: '#818CF8',
    secondary: '#A78BFA',
    accent: '#34D399',
    error: '#F87171',
    border: '#374151',
    card: '#1F2937',
  };

  // Get the colors based on the current theme
  const colors = theme === 'light' ? lightTheme : darkTheme;

  const value = {
    theme,
    colors,
    loading,
    toggleTheme,
    changeTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext; 