import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../contexts/AuthContext';
import { Card, Title, Paragraph, Button, useTheme, Avatar } from 'react-native-paper';
import { getThemeForRole } from '../../config/theme';

const UserQRCodeSection = () => {
  const { user } = useAuth();
  const navigation = useNavigation();
  const paperTheme = useTheme();
  const theme = getThemeForRole(user?.role || 'default');
  
  return (
    <Card style={styles.card} elevation={1}>
      <Card.Content>
        <View style={styles.titleContainer}>
          <Title style={[styles.title, { color: theme.colors.primary }]}>User Code</Title>
          <TouchableOpacity
            onPress={() => navigation.navigate('UserQRCode')}
            style={[styles.viewQrButton, { backgroundColor: theme.colors.primary }]}
          >
            <Text style={styles.viewQrButtonText}>View QR</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.userInfo}>
          <Avatar.Text 
            size={70} 
            label={user?.displayName?.substring(0, 2).toUpperCase() || "U"} 
            style={{ backgroundColor: theme.colors.primary }}
          />
          <Text style={[styles.username, { color: theme.colors.primary }]}>{user?.displayName || 'User'}</Text>
          <Text style={styles.userRole}>{user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1)}</Text>
          {user?.email && <Text style={styles.userEmail}>{user?.email}</Text>}
        </View>
        
        <View style={styles.codeContainer}>
          <Text style={styles.codeLabel}>Your unique code:</Text>
          <View style={[styles.codeBlock, { borderColor: theme.colors.primary }]}>
            <Text style={[styles.codeText, { color: theme.colors.primary }]}>
              {user?.userCode || 'CODE NOT FOUND'}
            </Text>
          </View>
          <Paragraph style={styles.codeDescription}>
            Use this code to connect with healthcare providers. You can also share your QR code when visiting healthcare facilities.
          </Paragraph>
        </View>
      </Card.Content>
      
      <Card.Actions style={styles.actions}>
        <Button 
          mode="contained" 
          onPress={() => navigation.navigate('UserQRCode')}
          style={{ backgroundColor: theme.colors.primary }}
        >
          View Full QR Code
        </Button>
      </Card.Actions>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 20,
    borderRadius: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  userInfo: {
    marginTop: 16,
    alignItems: 'center',
  },
  username: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
  },
  userRole: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  codeContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  codeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  codeBlock: {
    borderWidth: 2,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  codeText: {
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 2,
  },
  codeDescription: {
    textAlign: 'center',
    fontSize: 12,
    color: '#666',
    paddingHorizontal: 20,
  },
  viewQrButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  viewQrButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  actions: {
    justifyContent: 'center',
    paddingTop: 8,
    paddingBottom: 16,
  },
});

export default UserQRCodeSection; 