import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ROLE_COLORS } from '../config/theme';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';
import { showMessage } from 'react-native-flash-message';

const CaregiverRecordActivityScreen = ({ route }) => {
  const { patient } = route.params;
  const navigation = useNavigation();
  const caregiverColors = ROLE_COLORS.caregiver;

  // Activity types
  const activityTypes = [
    { id: 'medication', label: 'Medication', icon: 'medkit' },
    { id: 'exercise', label: 'Exercise', icon: 'fitness-outline' },
    { id: 'social', label: 'Social', icon: 'people' },
    { id: 'cognitive', label: 'Cognitive', icon: 'book' },
    { id: 'hygiene', label: 'Hygiene', icon: 'water-outline' },
    { id: 'nutrition', label: 'Nutrition', icon: 'restaurant' },
    { id: 'sleep', label: 'Sleep', icon: 'moon' },
    { id: 'other', label: 'Other', icon: 'list' }
  ];

  const [activityType, setActivityType] = useState('medication');
  const [description, setDescription] = useState('');
  const [duration, setDuration] = useState('');
  const [completed, setCompleted] = useState(true);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const handleActivityTypeChange = (type) => {
    setActivityType(type);
  };

  const validateForm = () => {
    if (!description.trim()) {
      return false;
    }

    if (duration && isNaN(parseInt(duration))) {
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please enter a valid description and duration (in minutes)');
      return;
    }

    setLoading(true);
    try {
      await firebaseCaregiverService.recordPatientActivity(
        patient.uid,
        activityType,
        description,
        duration ? parseInt(duration) : 0,
        completed,
        notes
      );

      showMessage({
        message: 'Success',
        description: 'Activity recorded successfully',
        type: 'success',
        backgroundColor: caregiverColors.primary,
      });

      // Navigate back to patient detail
      navigation.goBack();
    } catch (error) {
      console.error('Error recording activity:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to record activity. Please try again.',
        type: 'danger',
        backgroundColor: '#ff4444',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderActivityTypeButton = (id, label, icon) => (
    <TouchableOpacity
      key={id}
      style={[
        styles.activityTypeButton,
        activityType === id && { backgroundColor: caregiverColors.primary }
      ]}
      onPress={() => handleActivityTypeChange(id)}
    >
      <Ionicons
        name={icon}
        size={24}
        color={activityType === id ? '#fff' : caregiverColors.primary}
      />
      <Text
        style={[
          styles.activityTypeLabel,
          activityType === id && { color: '#fff' }
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={caregiverColors.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Record Activity</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.patientInfoContainer}>
          <Text style={styles.patientName}>
            Patient: {patient.firstName} {patient.lastName}
          </Text>
        </View>

        <View style={styles.activityTypeContainer}>
          <Text style={styles.sectionTitle}>Select Activity Type</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.activityTypeScroll}>
            {activityTypes.map(type => renderActivityTypeButton(type.id, type.label, type.icon))}
          </ScrollView>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Activity Details</Text>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Description *</Text>
            <TextInput
              style={styles.input}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter activity description"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Duration (minutes)</Text>
            <TextInput
              style={styles.input}
              value={duration}
              onChangeText={setDuration}
              placeholder="Enter duration in minutes"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.completedContainer}>
            <Text style={styles.inputLabel}>Activity Status</Text>
            <View style={styles.completedButtons}>
              <TouchableOpacity
                style={[
                  styles.completedButton,
                  completed && { backgroundColor: caregiverColors.primary }
                ]}
                onPress={() => setCompleted(true)}
              >
                <Text
                  style={[
                    styles.completedButtonText,
                    completed && { color: '#fff' }
                  ]}
                >
                  Completed
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.completedButton,
                  !completed && { backgroundColor: caregiverColors.primary }
                ]}
                onPress={() => setCompleted(false)}
              >
                <Text
                  style={[
                    styles.completedButtonText,
                    !completed && { color: '#fff' }
                  ]}
                >
                  Not Completed
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Notes (Optional)</Text>
            <TextInput
              style={[styles.input, styles.notesInput]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Enter any additional notes"
              multiline
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, { backgroundColor: caregiverColors.primary }]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Ionicons name="save" size={20} color="#fff" />
                <Text style={styles.submitButtonText}>Save Activity</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  headerRight: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  patientInfoContainer: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  patientName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#424242',
  },
  activityTypeContainer: {
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 12,
  },
  activityTypeScroll: {
    flexDirection: 'row',
  },
  activityTypeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    marginRight: 12,
    minWidth: 100,
  },
  activityTypeLabel: {
    marginTop: 4,
    fontSize: 12,
    color: '#424242',
  },
  formContainer: {
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#424242',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  notesInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  completedContainer: {
    marginBottom: 16,
  },
  completedButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  completedButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    marginRight: 8,
  },
  completedButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#424242',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  submitButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default CaregiverRecordActivityScreen;
