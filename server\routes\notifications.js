const express = require('express');
const router = express.Router();
const admin = require('firebase-admin');
const db = admin.firestore();

// Middleware to verify authentication
const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Invalid token' });
    }
};

// Create a new reminder
router.post('/reminder', async (req, res) => {
    try {
        const { userId, title, body, scheduledTime, type } = req.body;
        
        // Store reminder in Firestore
        const reminderRef = await db.collection('reminders').add({
            userId,
            title,
            body,
            scheduledTime,
            type,
            created: admin.firestore.FieldValue.serverTimestamp(),
            status: 'scheduled'
        });

        // Schedule the notification
        const message = {
            notification: {
                title,
                body
            },
            data: {
                type,
                reminderId: reminderRef.id
            },
            token: userId // This should be the FCM token
        };

        await admin.messaging().send(message);

        res.status(201).json({
            success: true,
            reminderId: reminderRef.id
        });
    } catch (error) {
        console.error('Error creating reminder:', error);
        res.status(500).json({ error: 'Failed to create reminder' });
    }
});

// Get all reminders for a user
router.get('/reminders/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const reminders = await db.collection('reminders')
            .where('userId', '==', userId)
            .orderBy('scheduledTime', 'desc')
            .get();

        const reminderList = [];
        reminders.forEach(doc => {
            reminderList.push({
                id: doc.id,
                ...doc.data()
            });
        });

        res.json(reminderList);
    } catch (error) {
        console.error('Error fetching reminders:', error);
        res.status(500).json({ error: 'Failed to fetch reminders' });
    }
});

// Update a reminder
router.put('/reminder/:reminderId', async (req, res) => {
    try {
        const { reminderId } = req.params;
        const updateData = req.body;
        
        await db.collection('reminders').doc(reminderId).update({
            ...updateData,
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.json({ success: true });
    } catch (error) {
        console.error('Error updating reminder:', error);
        res.status(500).json({ error: 'Failed to update reminder' });
    }
});

// Delete a reminder
router.delete('/reminder/:reminderId', async (req, res) => {
    try {
        const { reminderId } = req.params;
        await db.collection('reminders').doc(reminderId).delete();
        res.json({ success: true });
    } catch (error) {
        console.error('Error deleting reminder:', error);
        res.status(500).json({ error: 'Failed to delete reminder' });
    }
});

// Send a notification to a user
router.post('/send', authenticateUser, async (req, res) => {
    try {
        const { userId, title, body, data } = req.body;
        const senderId = req.user.uid;

        // Get target user FCM token
        const userDoc = await db.collection('users').doc(userId).get();
        
        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        const userData = userDoc.data();
        if (!userData.fcmToken) {
            return res.status(400).json({ error: 'User has no FCM token' });
        }
        
        // Log notification in database
        await db.collection('notifications').add({
            userId,
            senderId,
            title,
            body,
            data,
            isRead: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Send notification via FCM
        const message = {
            token: userData.fcmToken,
            notification: {
                title,
                body,
            },
            data: {
                ...data,
                senderId
            }
        };
        
        await admin.messaging().send(message);
        
        res.json({ success: true });
    } catch (error) {
        console.error('Error sending notification:', error);
        res.status(500).json({ error: 'Failed to send notification' });
    }
});

// Get unread notifications
router.get('/unread', authenticateUser, async (req, res) => {
    try {
        const userId = req.user.uid;
        
        const notificationsSnapshot = await db.collection('notifications')
            .where('userId', '==', userId)
            .where('isRead', '==', false)
            .orderBy('createdAt', 'desc')
            .get();
        
        const notifications = [];
        notificationsSnapshot.forEach(doc => {
            notifications.push({
                id: doc.id,
                ...doc.data()
            });
        });
        
        res.json(notifications);
    } catch (error) {
        console.error('Error fetching unread notifications:', error);
        res.status(500).json({ error: 'Failed to fetch notifications' });
    }
});

// Mark notification as read
router.post('/:notificationId/read', authenticateUser, async (req, res) => {
    try {
        const { notificationId } = req.params;
        const userId = req.user.uid;
        
        const notificationDoc = await db.collection('notifications').doc(notificationId).get();
        
        if (!notificationDoc.exists) {
            return res.status(404).json({ error: 'Notification not found' });
        }
        
        const notificationData = notificationDoc.data();
        
        // User can only mark their own notifications as read
        if (notificationData.userId !== userId) {
            return res.status(403).json({ error: 'Not authorized to access this notification' });
        }
        
        await db.collection('notifications').doc(notificationId).update({
            isRead: true,
            readAt: admin.firestore.FieldValue.serverTimestamp()
        });
        
        res.json({ success: true });
    } catch (error) {
        console.error('Error marking notification as read:', error);
        res.status(500).json({ error: 'Failed to update notification' });
    }
});

// Delete notification
router.delete('/:notificationId', authenticateUser, async (req, res) => {
    try {
        const { notificationId } = req.params;
        const userId = req.user.uid;
        
        const notificationDoc = await db.collection('notifications').doc(notificationId).get();
        
        if (!notificationDoc.exists) {
            return res.status(404).json({ error: 'Notification not found' });
        }
        
        const notificationData = notificationDoc.data();
        
        // User can only delete their own notifications
        if (notificationData.userId !== userId) {
            return res.status(403).json({ error: 'Not authorized to delete this notification' });
        }
        
        await db.collection('notifications').doc(notificationId).delete();
        
        res.json({ success: true });
    } catch (error) {
        console.error('Error deleting notification:', error);
        res.status(500).json({ error: 'Failed to delete notification' });
    }
});

module.exports = router;
