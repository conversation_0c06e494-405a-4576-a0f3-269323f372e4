import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Modal,
  ScrollView,
  Alert,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { useRoute } from '@react-navigation/native';
import { useAuth } from '../../../../contexts/AuthContext';
import { useAppointments } from '../../../../contexts/AppointmentContext';
import { usersAPI } from '../../../../config/api';
import { firebaseAppointmentsService } from '../../../../services/firebaseAppointmentsService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LinearGradient } from 'expo-linear-gradient';
import { Avatar } from 'react-native-paper';

const Appointments = () => {
  const route = useRoute();
  const { user } = useAuth();
  const {
    appointments: contextAppointments,
    loading: appointmentsLoading,
    createAppointment,
    cancelAppointment,
    rescheduleAppointment,
    fetchAppointments,
    getAvailableTimeSlots
  } = useAppointments();

  // La référence à l'animation a été supprimée
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [showCancelled, setShowCancelled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState(null);
  const [availabilityStatus, setAvailabilityStatus] = useState(null);
  const [availabilityChecking, setAvailabilityChecking] = useState(false);
  const [doctors, setDoctors] = useState([]);
  const [isRescheduling, setIsRescheduling] = useState(false);
  const [appointmentToReschedule, setAppointmentToReschedule] = useState(null);
  const [reasonForVisit, setReasonForVisit] = useState('');
  const [suggestedTimeSlots, setSuggestedTimeSlots] = useState([]);

  // Pour le sélecteur de date personnalisé
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [tempSelectedDate, setTempSelectedDate] = useState(new Date());

  // Pour la reconnaissance vocale
  const [requestMode, setRequestMode] = useState('text'); // 'text' ou 'voice'
  const [recording, setRecording] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [processingVoice, setProcessingVoice] = useState(false);
  const [recognizedText, setRecognizedText] = useState('');
  const [voiceAppointmentDetails, setVoiceAppointmentDetails] = useState(null);

  // Pour les détails du rendez-vous
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [cancelReasonModalVisible, setCancelReasonModalVisible] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  const [appointmentToCancel, setAppointmentToCancel] = useState(null);

  // Vérifier si nous devons ouvrir le modal de demande de rendez-vous
  useEffect(() => {
    if (route.params?.openRequestModal) {
      setModalVisible(true);

      // Si un docteur spécifique est sélectionné, le définir
      if (route.params?.selectedDoctorId && doctors.length > 0) {
        const selectedDoctor = doctors.find(doc => doc.id === route.params.selectedDoctorId);
        if (selectedDoctor) {
          setSelectedDoctor(selectedDoctor);
        }
      }
    }
  }, [route.params, doctors]);

  // Load appointments and doctors from API
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Get linked doctors from API
        const linkedDoctors = await usersAPI.getLinkedUsers('doctors');

        if (linkedDoctors && linkedDoctors.length > 0) {
          // Format doctors data for the component
          const formattedDoctors = linkedDoctors.map(doctor => ({
            id: doctor.uid,
            name: doctor.displayName ? `Dr. ${doctor.displayName}` : 'Dr.',
            specialty: doctor.specialty || 'General Practitioner'
          }));
          setDoctors(formattedDoctors);
        }

        // Fetch appointments if they haven't been loaded yet
        if (appointments.length === 0 && contextAppointments.length === 0) {
          await fetchAppointments();
        }

        // Initialize filtered appointments
        if (contextAppointments.length > 0) {
          filterAppointments(contextAppointments, showCancelled);
        }
      } catch (error) {
        console.error('Error loading doctors:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Use appointments from context and filter them
  useEffect(() => {
    if (contextAppointments) {
      // Les logs des rendez-vous ont été supprimés

      setAppointments(contextAppointments);

      // Filter appointments based on showCancelled flag
      filterAppointments(contextAppointments, showCancelled);
    }
  }, [contextAppointments, showCancelled]);

  // L'intervalle de rafraîchissement automatique a été supprimé

  // Function to filter appointments
  const filterAppointments = (appointmentsList, includeCancelled) => {
    if (!appointmentsList) return;

    const filtered = appointmentsList.filter(appointment => {
      const status = appointment.status ? appointment.status.toLowerCase() : 'pending';

      // If includeCancelled is true, show all appointments
      // Otherwise, only show pending and confirmed appointments
      return includeCancelled || (status !== 'cancelled');
    });

    setFilteredAppointments(filtered);
  };

  // Toggle showing cancelled appointments
  const toggleShowCancelled = () => {
    setShowCancelled(prev => !prev);
  };

  // Request permissions for audio recording
  useEffect(() => {
    (async () => {
      try {
        const { status } = await Audio.requestPermissionsAsync();
        if (status !== 'granted') {
          // Log supprimé
        }
      } catch (error) {
        console.error('Error requesting audio permissions:', error);
      }
    })();
  }, []);

  // Show appointment request modal
  const handleRequestAppointment = () => {
    setModalVisible(true);
    setRequestMode('text');
  };

  // Switch to voice mode
  const switchToVoiceMode = () => {
    setRequestMode('voice');
    setRecognizedText('');
    setVoiceAppointmentDetails(null);
  };

  // Switch to text mode
  const switchToTextMode = () => {
    setRequestMode('text');
  };

  // Start recording function
  const startRecording = async () => {
    try {
      // Configure audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Create and prepare the recording
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  // Stop recording function
  const stopRecording = async () => {
    if (!recording) return;

    setIsRecording(false);
    setProcessingVoice(true);

    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      // In a real app, you would send this audio file to a speech-to-text service
      // For now, we'll just show a message that this is not implemented yet

      Alert.alert(
        'Speech Recognition',
        'Speech recognition is not implemented yet. Please use the text mode to request an appointment.',
        [{ text: 'OK' }]
      );

      setProcessingVoice(false);

      // Clean up the recording
      setRecording(null);

      // In a real app, you might want to delete the temporary file
      try {
        await FileSystem.deleteAsync(uri);
      } catch (error) {
        // Log supprimé
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setProcessingVoice(false);
      Alert.alert('Error', 'Failed to process recording. Please try again.');
    }
  };

  // Parse the appointment request text
  const parseAppointmentRequest = (text) => {
    // This is a placeholder for future NLP implementation
    // Currently, voice recognition is not fully implemented

    // Create a default appointment details object
    const details = {
      doctor: 'Please select a doctor',
      date: 'Please select a date',
      time: 'Please select a time',
      reason: 'Please specify a reason',
      status: 'pending'
    };

    setVoiceAppointmentDetails(details);
  };

  // Confirm and submit the voice appointment request
  const confirmVoiceAppointment = async () => {
    if (voiceAppointmentDetails) {
      // Find the doctor object that matches the appointment
      const doctor = doctors.find(doc => doc.name === voiceAppointmentDetails.doctor);

      if (!doctor) {
        Alert.alert('Error', `Could not find doctor: ${voiceAppointmentDetails.doctor}`);
        return;
      }

      try {
        // Create a new appointment from voice request
        const newAppointment = {
          id: Date.now().toString(),
          doctor: voiceAppointmentDetails.doctor,
          specialty: doctor.specialty,
          date: voiceAppointmentDetails.date,
          time: voiceAppointmentDetails.time,
          reason: voiceAppointmentDetails.reason,
          status: 'Pending',
          patientId: user?.uid || 'local-user',
          createdAt: new Date().toISOString()
        };

        // Save to Firebase
        await firebaseAppointmentsService.saveAppointment(newAppointment);

        // Add the new appointment to the state
        setAppointments(prevAppointments => [...prevAppointments, newAppointment]);

        // Generate notifications for upcoming appointments
        await generateAppointmentNotifications();

        // Close the modal and reset state
        setModalVisible(false);
        setRecognizedText('');
        setVoiceAppointmentDetails(null);

        // Show confirmation to user
        Alert.alert(
          'Appointment Requested',
          `Your appointment with ${voiceAppointmentDetails.doctor} on ${voiceAppointmentDetails.date} at ${voiceAppointmentDetails.time} has been requested.`,
          [{ text: 'OK' }]
        );
      } catch (error) {
        console.error('Error saving voice appointment:', error);
        Alert.alert('Error', 'Failed to save appointment');
      }
    }
  };

  const handleCloseModal = () => {
    // Enregistrer si nous étions en train de replanifier
    const wasRescheduling = isRescheduling;

    // Réinitialiser tous les états
    setModalVisible(false);
    setSelectedDoctor(null);
    setSelectedDate(new Date());
    setSelectedTime(null);
    setAvailabilityStatus(null);
    setIsRescheduling(false);
    setAppointmentToReschedule(null);
    setRequestMode('text');
    setRecognizedText('');
    setVoiceAppointmentDetails(null);
    setReasonForVisit('');
    setSuggestedTimeSlots([]);
    if (recording) {
      stopRecording();
    }

    // Force a complete refresh of appointments when closing the modal
    // Log supprimé

    // Afficher un indicateur de chargement
    setLoading(true);

    // First, clear the current appointments to force a complete refresh
    setAppointments([]);
    setFilteredAppointments([]);

    // Attendre un peu plus longtemps si nous étions en train de replanifier
    const delay = wasRescheduling ? 1000 : 500;

    // Then fetch new appointments from Firebase
    setTimeout(async () => {
      try {
        // Forcer une nouvelle récupération des rendez-vous depuis Firebase
        await fetchAppointments();
        // Log supprimé

        // Force another refresh after a short delay to ensure all updates are applied
        setTimeout(async () => {
          try {
            // Vider à nouveau les rendez-vous pour forcer une récupération complète
            setAppointments([]);
            setFilteredAppointments([]);

            // Récupérer à nouveau les rendez-vous
            await fetchAppointments();
            // Log supprimé

            // Si nous étions en train de replanifier, faire une troisième tentative
            if (wasRescheduling) {
              setTimeout(async () => {
                try {
                  await fetchAppointments();
                  // Log supprimé
                } catch (error) {
                  console.error('Error in third refresh:', error);
                } finally {
                  setLoading(false);
                }
              }, 1500);
            } else {
              setLoading(false);
            }
          } catch (error) {
            console.error('Error in second refresh:', error);
            setLoading(false);
          }
        }, 1500);
      } catch (error) {
        console.error('Error refreshing appointments:', error);
        setLoading(false);
      }
    }, delay);
  };

  // Pour ouvrir le modal de sélection de date
  const openDatePicker = () => {
    setTempSelectedDate(selectedDate);
    setDatePickerVisible(true);
  };

  // Pour ouvrir le modal de sélection de date pour un rendez-vous à reprogrammer
  const openRescheduleDatePicker = (appointment) => {
    // Convert the appointment date string to a Date object
    const [year, month, day] = appointment.date.split('-').map(num => parseInt(num));
    const appointmentDate = new Date(year, month - 1, day);

    setTempSelectedDate(appointmentDate);
    setSelectedTime(null); // Reset time selection
    setDatePickerVisible(true);
  };

  // Pour confirmer la date sélectionnée
  const confirmDate = () => {
    setSelectedDate(tempSelectedDate);
    setDatePickerVisible(false);
    setAvailabilityStatus(null); // Reset availability when date changes
    setSuggestedTimeSlots([]); // Reset suggested time slots

    if (isRescheduling && appointmentToReschedule && selectedTime) {
      // If we're rescheduling and have all the necessary data, proceed to check availability
      checkAvailability();
    }
  };

  // Handle selecting a suggested time slot
  const handleSelectSuggestedTime = (time) => {
    // Update the selected time
    setSelectedTime(time);
    // Update availability status
    setAvailabilityStatus('available');
    // Clear suggested time slots
    setSuggestedTimeSlots([]);

    // Show confirmation message
    Alert.alert(
      'Time Selected',
      `You've selected ${time}. This time is available.`,
      [{ text: 'OK' }]
    );
  };

  // Pour annuler la sélection de date
  const cancelDateSelection = () => {
    setDatePickerVisible(false);
  };

  // Pour générer les dates disponibles (les 30 prochains jours)
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  // Generate appointment notifications
  const generateAppointmentNotifications = async () => {
    try {
      // Get all appointments from Firebase
      const appointments = await firebaseAppointmentsService.getAllAppointments();

      // Filter for upcoming appointments
      const now = new Date();
      const upcomingAppointments = appointments.filter(appointment => {
        const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
        return appointmentDate > now && appointment.status.toLowerCase() !== 'cancelled';
      });

      // Sort by date and time
      upcomingAppointments.sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateA - dateB;
      });

      // Generate notifications
      const notifications = upcomingAppointments.map(appointment => ({
        id: `appointment-${appointment.id}`,
        title: `Appointment with ${appointment.doctor}`,
        body: `You have an appointment on ${appointment.date} at ${appointment.time}`,
        type: 'appointment',
        appointmentId: appointment.id,
        date: appointment.date,
        time: appointment.time,
        createdAt: new Date().toISOString(),
        read: false
      }));

      // Save to AsyncStorage
      const APPOINTMENT_NOTIFICATIONS_KEY = '@neurocare:appointment_notifications';
      await AsyncStorage.setItem(APPOINTMENT_NOTIFICATIONS_KEY, JSON.stringify(notifications));

      return notifications;
    } catch (error) {
      console.error('Error generating appointment notifications:', error);
      return [];
    }
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateForList = (date) => {
    // Format pour l'affichage dans la liste: YYYY-MM-DD
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Afficher les détails du rendez-vous
  const handleViewAppointmentDetails = (appointment) => {
    setSelectedAppointment(appointment);
    setDetailsModalVisible(true);
  };

  const handleCloseDetailsModal = () => {
    setDetailsModalVisible(false);
    setSelectedAppointment(null);
  };

  const handleRescheduleAppointment = (appointment) => {
    // Check if the appointment is already cancelled
    if (appointment.status && appointment.status.toLowerCase() === 'cancelled') {
      Alert.alert(
        'Cannot Reschedule',
        'This appointment has been cancelled and cannot be rescheduled.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check if the appointment is already completed
    if (appointment.status && appointment.status.toLowerCase() === 'completed') {
      Alert.alert(
        'Cannot Reschedule',
        'This appointment has been completed and cannot be rescheduled.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check if the appointment date has passed
    const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
    const now = new Date();

    if (appointmentDate < now) {
      Alert.alert(
        'Cannot Reschedule',
        'You cannot reschedule an appointment that has already passed.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Reschedule Appointment',
      `Would you like to reschedule your appointment with ${appointment.doctor} on ${appointment.date} at ${appointment.time}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reschedule',
          onPress: () => {
            // Set the appointment to reschedule and open the modal
            setAppointmentToReschedule(appointment);
            setIsRescheduling(true);
            setReasonForVisit(appointment.reason || ''); // Preserve the original reason

            // Find the doctor object that matches the appointment
            const doctor = doctors.find(doc => doc.name === appointment.doctor);
            if (doctor) {
              setSelectedDoctor(doctor);
            }

            // Open the modal first
            setModalVisible(true);

            // Then open the date picker
            setTimeout(() => {
              openRescheduleDatePicker(appointment);
            }, 300);
          }
        }
      ]
    );
  };

  // Function to open the cancellation reason modal
  const openCancelReasonModal = (appointment) => {
    setAppointmentToCancel(appointment);
    setCancellationReason('');
    setCancelReasonModalVisible(true);
  };

  // Function to close the cancellation reason modal
  const closeCancelReasonModal = () => {
    setCancelReasonModalVisible(false);
    setAppointmentToCancel(null);
  };

  // Function to process the appointment cancellation
  const processCancelAppointment = async (appointmentId, reason) => {
    try {
      setLoading(true);

      // Cancel appointment using the context
      await cancelAppointment(appointmentId, reason || 'Cancelled by patient');

      // Update notifications after cancellation
      try {
        // Generate new notifications after cancellation
        await generateAppointmentNotifications();
      } catch (error) {
        console.error('Error updating notifications after cancellation:', error);
        // Continue even if notification update fails
      }

      // Refresh appointments from server
      await fetchAppointments();

      // If we're not showing cancelled appointments, filter them out again
      if (!showCancelled && appointments.length > 0) {
        filterAppointments(appointments, showCancelled);
      }

      Alert.alert('Success', 'Your appointment has been cancelled.');
      return true;
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      Alert.alert('Error', 'Failed to cancel appointment. Please try again later.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Function to handle the cancellation confirmation
  const confirmCancellation = async () => {
    if (!appointmentToCancel) return;

    // Show loading indicator
    setLoading(true);

    try {
      const success = await processCancelAppointment(appointmentToCancel.id, cancellationReason);
      if (success) {
        closeCancelReasonModal();
      }
    } catch (error) {
      console.error('Error in confirmCancellation:', error);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle the initial cancel appointment request
  const handleCancelAppointment = (appointment) => {
    // Check if the appointment is already cancelled
    if (appointment.status && appointment.status.toLowerCase() === 'cancelled') {
      Alert.alert(
        'Already Cancelled',
        'This appointment has already been cancelled.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check if the appointment date has passed
    const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
    const now = new Date();

    if (appointmentDate < now) {
      Alert.alert(
        'Cannot Cancel',
        'You cannot cancel an appointment that has already passed.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Cancel Appointment',
      `Are you sure you want to cancel your appointment with ${appointment.doctor} on ${appointment.date} at ${appointment.time}?`,
      [
        {
          text: 'No',
          style: 'cancel'
        },
        {
          text: 'Provide Reason',
          onPress: () => openCancelReasonModal(appointment)
        },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              await processCancelAppointment(appointment.id, 'Cancelled by patient');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const checkAvailability = async () => {
    if (!selectedDoctor && !isRescheduling) {
      Alert.alert('Error', 'Please select a doctor first');
      return;
    }

    if (!selectedDate || !selectedTime) {
      Alert.alert('Error', 'Please select a date and time');
      return;
    }

    setAvailabilityChecking(true);
    setAvailabilityStatus(null);
    setSuggestedTimeSlots([]);

    try {
      // Get the doctor ID
      const doctorId = selectedDoctor ? selectedDoctor.id :
                      (appointmentToReschedule ? appointmentToReschedule.doctorId : null);

      if (!doctorId) {
        throw new Error('Doctor ID not found');
      }

      // Format the date for the API call
      const formattedDate = formatDateForList(selectedDate);

      // Get available time slots from the API
      const availableSlots = await getAvailableTimeSlots(doctorId, formattedDate);

      // Check if the selected time is in the available slots
      const isTimeAvailable = availableSlots.includes(selectedTime);

      if (isTimeAvailable) {
        // Time slot is available
        setAvailabilityStatus('available');
        Alert.alert(
          'Availability',
          'The selected time slot is available. You can proceed with your appointment request.',
          [{ text: 'OK' }]
        );
      } else {
        // Time slot is not available
        setAvailabilityStatus('unavailable');

        // Set suggested time slots
        setSuggestedTimeSlots(availableSlots);

        Alert.alert(
          'Time Slot Unavailable',
          'The selected time slot is not available. Please choose from the suggested available times.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error in checkAvailability:', error);

      // In case of network error, try to generate some default time slots
      const defaultSlots = [];
      const startHour = 9; // 9 AM
      const endHour = 17; // 5 PM

      for (let hour = startHour; hour < endHour; hour++) {
        defaultSlots.push(`${hour.toString().padStart(2, '0')}:00`);
        defaultSlots.push(`${hour.toString().padStart(2, '0')}:30`);
      }

      // Filter out the selected time from default slots
      const filteredDefaultSlots = defaultSlots.filter(slot => slot !== selectedTime);

      setSuggestedTimeSlots(filteredDefaultSlots);
      setAvailabilityStatus('unavailable');

      Alert.alert(
        'Connection Error',
        'Could not check availability due to a connection error. Please choose from the suggested time slots or try again later.',
        [{ text: 'OK' }]
      );
    } finally {
      setAvailabilityChecking(false);
    }
  };

  const submitAppointmentRequest = async () => {
    if (!selectedDoctor) {
      Alert.alert('Error', 'Please select a doctor');
      return;
    }

    if (!selectedTime) {
      Alert.alert('Error', 'Please select an appointment time');
      return;
    }

    // Check availability before submitting
    if (availabilityStatus !== 'available') {
      // If we haven't checked availability yet, do it now
      await checkAvailability();

      // After checking, if still not available, don't proceed
      if (availabilityStatus !== 'available') {
        Alert.alert(
          'Availability Check Required',
          'Please check the availability of your selected time slot before proceeding.',
          [{ text: 'OK' }]
        );
        return;
      }
    }

    // Use the selected time
    const timeString = selectedTime;

    try {
      if (isRescheduling && appointmentToReschedule) {
        setLoading(true);

        try {
          // Les logs ont été supprimés

          // Use the dedicated reschedule function
          try {
            await rescheduleAppointment(
              appointmentToReschedule.id,
              formatDateForList(selectedDate),
              timeString,
              `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time}`
            );

            // No need for additional update as the context already updates Firebase

            // Refresh appointments from server
            await fetchAppointments();

            // Create an updated appointment object to ensure UI is updated immediately
            const updatedAppointment = {
              ...appointmentToReschedule,
              date: formatDateForList(selectedDate),
              time: timeString,
              status: 'pending',
              previousDate: appointmentToReschedule.date,
              previousTime: appointmentToReschedule.time,
              notes: `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time} to ${formatDateForList(selectedDate)} at ${timeString}`,
              rescheduledAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            // Log supprimé

            // Update the appointments in state directly to ensure immediate UI update
            setAppointments(prev =>
              prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
            );

            // Update filtered appointments directly
            setFilteredAppointments(prev =>
              prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
            );

            // If we're not showing cancelled appointments, filter them out again
            if (!showCancelled && appointments.length > 0) {
              filterAppointments(appointments, showCancelled);
            }
          } catch (apiError) {
            console.error('Error rescheduling appointment:', apiError);

            // If the reschedule function fails, try a direct update to Firebase
            await firebaseAppointmentsService.updateAppointment(appointmentToReschedule.id, {
              ...appointmentToReschedule,
              date: formatDateForList(selectedDate),
              time: timeString,
              status: 'pending',
              previousDate: appointmentToReschedule.date,
              previousTime: appointmentToReschedule.time,
              rescheduledAt: new Date().toISOString(),
              rescheduledBy: user?.uid || 'local-user',
              updatedAt: new Date().toISOString()
            });

            // Update the appointments in state directly
            const updatedAppointment = {
              ...appointmentToReschedule,
              date: formatDateForList(selectedDate),
              time: timeString,
              status: 'pending',
              previousDate: appointmentToReschedule.date,
              previousTime: appointmentToReschedule.time,
              notes: `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time} to ${formatDateForList(selectedDate)} at ${timeString}`,
              rescheduledAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            // Log supprimé

            setAppointments(prev =>
              prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
            );

            // Update filtered appointments if needed
            if (filteredAppointments.length > 0) {
              setFilteredAppointments(prev =>
                prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
              );
            }
          }

          // Generate new notifications after rescheduling
          await generateAppointmentNotifications();

          // Force a complete refresh of appointments from Firebase
          await fetchAppointments();

          // Log supprimé

          // Force a direct update of the UI before showing the success message
          const newDate = formatDateForList(selectedDate);
          const newTime = timeString;
          const noteText = `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time} to ${newDate} at ${newTime}`;

          const updatedAppointment = {
            ...appointmentToReschedule,
            date: newDate,
            time: newTime,
            status: 'pending',
            previousDate: appointmentToReschedule.date,
            previousTime: appointmentToReschedule.time,
            notes: noteText,
            rescheduledAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          // Les logs de vérification ont été supprimés

          // Update both appointments arrays directly for immediate UI feedback
          setAppointments(prev =>
            prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
          );
          setFilteredAppointments(prev =>
            prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
          );

          // Afficher un message de confirmation avec un délai pour permettre à Firebase de se mettre à jour
          setTimeout(() => {
            Alert.alert(
              'Appointment Successfully Rescheduled',
              `Your appointment with ${selectedDoctor.name} has been rescheduled:\n\n` +
              `FROM: ${appointmentToReschedule.date} at ${appointmentToReschedule.time}\n` +
              `TO: ${formatDateForList(selectedDate)} at ${timeString}\n\n` +
              `Your new appointment is now pending confirmation.\n\n` +
              `Please use the Refresh button if the changes are not immediately visible.`,
              [{
                text: 'OK',
                onPress: () => {
                  // Afficher un indicateur de chargement
                  setLoading(true);

                  // Vider complètement les rendez-vous pour forcer une récupération complète
                  setAppointments([]);
                  setFilteredAppointments([]);

                  // Fermer le modal (ce qui déclenchera également un rafraîchissement)
                  handleCloseModal();

                  // Forcer un autre rafraîchissement après un délai plus long pour s'assurer que les données sont synchronisées
                  setTimeout(async () => {
                    try {
                      // Vider à nouveau les rendez-vous pour forcer une récupération complète
                      setAppointments([]);
                      setFilteredAppointments([]);

                      // Récupérer les données fraîches depuis Firebase
                      await fetchAppointments();
                      // Log supprimé

                      // Forcer une dernière mise à jour de l'interface avec les dernières données
                      if (appointments.length > 0) {
                        filterAppointments(appointments, showCancelled);
                      }
                    } catch (error) {
                      console.error('Error in final refresh:', error);
                    } finally {
                      setLoading(false);
                    }
                  }, 3000);
                }
              }]
            );
          }, 1000);
        } catch (error) {
          console.error('Error in rescheduling process:', error);
          Alert.alert(
            'Rescheduling Failed',
            'There was a problem rescheduling your appointment. Please try again later.',
            [{ text: 'OK' }]
          );
        } finally {
          setLoading(false);
        }
      } else {
        // Create a new appointment
        const appointmentData = {
          doctorId: selectedDoctor.id,
          doctor: selectedDoctor.name, // Add doctor name for Firebase
          specialty: selectedDoctor.specialty, // Add specialty for Firebase
          date: formatDateForList(selectedDate),
          time: timeString,
          reason: reasonForVisit, // Include reason for visit
          type: 'in-person', // Default type
          duration: 30 // Default duration in minutes
        };

        // Create appointment using the context
        try {
          const savedAppointment = await createAppointment(appointmentData);

          // No need for additional save as the context already saves to Firebase

          // Log supprimé
        } catch (apiError) {
          console.error('Error creating appointment:', apiError);

          // If API fails, create a local appointment
          const localAppointment = {
            id: Date.now().toString(),
            doctor: selectedDoctor.name,
            specialty: selectedDoctor.specialty,
            date: formatDateForList(selectedDate),
            time: timeString,
            reason: reasonForVisit,
            status: 'Pending',
            patientId: user?.uid || 'local-user',
            createdAt: new Date().toISOString()
          };

          await firebaseAppointmentsService.saveAppointment(localAppointment);
          setAppointments(prev => [...prev, localAppointment]);
        }

        // Generate new notifications after adding appointment
        await generateAppointmentNotifications();

        // Show success message for new appointment
        Alert.alert(
          'Success',
          `Your appointment request with ${selectedDoctor.name} on ${formatDate(selectedDate)} has been submitted.`,
          [{ text: 'OK', onPress: handleCloseModal }]
        );
      }

      // Refresh appointments from server
      fetchAppointments();
    } catch (error) {
      console.error('Error saving appointment:', error);
      Alert.alert('Error', 'Failed to save appointment: ' + error.message);
    }
  };

  const renderAppointmentItem = ({ item }) => {
    // Determine status color based on appointment status
    let statusColor;
    const status = item.status ? item.status.toLowerCase() : 'pending';

    switch (status) {
      case 'confirmed':
        statusColor = '#4CAF50'; // Green
        break;
      case 'cancelled':
        statusColor = '#F44336'; // Red
        break;
      case 'completed':
        statusColor = '#2196F3'; // Blue
        break;
      case 'pending':
      default:
        statusColor = '#FFC107'; // Yellow/Amber
        break;
    }

    // Format the status text for display
    const statusText = item.status ?
      item.status.charAt(0).toUpperCase() + item.status.slice(1).toLowerCase() :
      'Pending';

    // Check if this appointment has been rescheduled
    const isRescheduled = item.notes && item.notes.includes('Rescheduled from');

    // Les logs des rendez-vous ont été supprimés

    // Extraire la nouvelle date et heure à partir des notes si c'est un rendez-vous replanifié
    let displayDate = item.date;
    let displayTime = item.time;

    // Si le rendez-vous a été replanifié, extraire la nouvelle date et heure des notes
    if (isRescheduled && item.notes) {
      try {
        // Extraire la nouvelle date et heure à partir des notes
        const noteText = item.notes;
        const match = noteText.match(/to ([\d-]+) at ([\d:]+\s*[APM]+)/i);

        if (match && match.length >= 3) {
          const newDate = match[1];
          const newTime = match[2];

          // Log supprimé

          // Utiliser ces valeurs si elles sont valides
          if (newDate && newTime) {
            displayDate = newDate;
            displayTime = newTime;
          }
        }
      } catch (error) {
        console.error('Error extracting date from notes:', error);
      }
    }

    // Les logs des rendez-vous ont été supprimés

    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() => handleViewAppointmentDetails(item)}
      >
        <LinearGradient
          colors={['#f5f7fa', '#ffffff']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.cardGradient}
        >
          <View style={styles.cardContent}>
            <View style={styles.cardHeader}>
              <View style={styles.dateContainer}>
                <Text style={styles.dateTitle}>{displayDate}</Text>
                <View style={styles.statusContainer}>
                  <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
                  <Text style={[styles.statusText, { color: statusColor }]}>
                    {statusText.toUpperCase()}
                  </Text>
                </View>
              </View>
              <Avatar.Icon
                size={40}
                icon="calendar"
                style={[styles.avatarIcon, { backgroundColor: statusColor + '20' }]}
                color={statusColor}
              />
            </View>

            <View style={styles.cardDivider} />

            <View style={styles.cardDetails}>
              <View style={styles.detailRow}>
                <Ionicons name="person" size={16} color="#555" style={styles.detailIcon} />
                <Text style={styles.detailText}>{item.doctor}</Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons name="medical" size={16} color="#555" style={styles.detailIcon} />
                <Text style={styles.detailText}>{item.specialty}</Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons name="time" size={16} color="#555" style={styles.detailIcon} />
                <Text style={styles.detailText}>{displayTime}</Text>
              </View>
              {item.reason && (
                <View style={styles.detailRow}>
                  <Ionicons name="document-text" size={16} color="#555" style={styles.detailIcon} />
                  <Text style={styles.detailText}>{item.reason}</Text>
                </View>
              )}
            </View>

            {isRescheduled && (
              <View style={styles.rescheduledBanner}>
                <Ionicons name="refresh-circle" size={16} color="#ff9800" />
                <Text style={styles.rescheduledText}>Rescheduled</Text>
              </View>
            )}

            <View style={styles.cardFooter}>
              <View style={styles.actionButtonsContainer}>
                {/* Only show reschedule button if appointment is not cancelled or completed */}
                {status !== 'cancelled' && status !== 'completed' && (
                  <TouchableOpacity
                    style={[styles.actionButton, styles.rescheduleButton]}
                    onPress={() => handleRescheduleAppointment(item)}
                  >
                    <Ionicons name="calendar-outline" size={14} color="#ff9800" style={styles.buttonIcon} />
                    <Text style={[styles.actionButtonText, styles.rescheduleButtonText]}>Reschedule</Text>
                  </TouchableOpacity>
                )}

                {/* Only show cancel button if appointment is not cancelled or completed */}
                {status !== 'cancelled' && status !== 'completed' && (
                  <TouchableOpacity
                    style={[styles.actionButton, styles.cancelButton]}
                    onPress={() => handleCancelAppointment(item)}
                  >
                    <Ionicons name="close-circle-outline" size={14} color="#f44336" style={styles.buttonIcon} />
                    <Text style={[styles.actionButtonText, styles.cancelButtonText]}>Cancel</Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={styles.viewDetailsButton}
                  onPress={() => handleViewAppointmentDetails(item)}
                >
                  <Text style={styles.viewDetailsText}>View Details</Text>
                  <Ionicons name="chevron-forward" size={14} color="#4CAF50" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  // Rendu d'une date dans le sélecteur personnalisé
  const renderDateOption = (date) => {
    const isSelected =
      tempSelectedDate.getDate() === date.getDate() &&
      tempSelectedDate.getMonth() === date.getMonth() &&
      tempSelectedDate.getFullYear() === date.getFullYear();

    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    return (
      <TouchableOpacity
        key={date.toISOString()}
        style={[
          styles.dateOption,
          isSelected && styles.selectedDateOption
        ]}
        onPress={() => setTempSelectedDate(date)}
      >
        <Text style={[styles.dateOptionDay, isSelected && styles.selectedDateText]}>
          {dayNames[date.getDay()]}
        </Text>
        <Text style={[styles.dateOptionDate, isSelected && styles.selectedDateText]}>
          {date.getDate()}
        </Text>
        <Text style={[styles.dateOptionMonth, isSelected && styles.selectedDateText]}>
          {monthNames[date.getMonth()]}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Appointments</Text>
      </View>

      <View style={styles.actionButtonsRow}>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={toggleShowCancelled}
        >
          <Ionicons
            name={showCancelled ? "eye-off-outline" : "eye-outline"}
            size={18}
            color="#3f51b5"
          />
          <Text style={styles.filterButtonText}>
            {showCancelled ? "Hide Cancelled" : "Show All"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.requestButton}
          onPress={handleRequestAppointment}
        >
          <Ionicons name="add-circle" size={18} color="#fff" />
          <Text style={styles.requestButtonText}>Request Appointment</Text>
        </TouchableOpacity>
      </View>

      {loading || appointmentsLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3f51b5" />
          <Text style={styles.loadingText}>Loading appointments...</Text>
        </View>
      ) : appointments.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No appointments scheduled</Text>
          <Text style={styles.emptySubText}>
            Request an appointment with one of our specialists
          </Text>
        </View>
      ) : filteredAppointments.length === 0 && !showCancelled ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No active appointments</Text>
          <Text style={styles.emptySubText}>
            All your appointments have been cancelled or completed.
            Tap "Show All" to view your appointment history.
          </Text>
        </View>
      ) : filteredAppointments.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No appointments found</Text>
          <Text style={styles.emptySubText}>
            Request an appointment with one of our specialists
          </Text>
        </View>
      ) : (
        <>
          <View style={styles.appointmentGuide}>
            <Text style={styles.guideTitle}>Managing Your Appointments</Text>
            <View style={styles.guideItem}>
              <Ionicons name="calendar-outline" size={18} color="#3f51b5" />
              <Text style={styles.guideText}>
                To <Text style={styles.guideHighlight}>reschedule</Text> an appointment, tap the "Reschedule" button on any active appointment
              </Text>
            </View>
            <View style={styles.guideItem}>
              <Ionicons name="close-circle-outline" size={18} color="#f44336" />
              <Text style={styles.guideText}>
                To <Text style={styles.guideHighlight}>cancel</Text> an appointment, tap the "Cancel" button and provide a reason
              </Text>
            </View>
          </View>

          {/* Le bouton de rafraîchissement a été supprimé */}

          <FlatList
            data={filteredAppointments}
            keyExtractor={(item) => item.id + (item.updatedAt || '')}
            renderItem={renderAppointmentItem}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            extraData={[appointments, filteredAppointments]}
          />
        </>
      )}

      {/* Appointment Request Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {isRescheduling ? 'Reschedule Appointment' : 'Request Appointment'}
              </Text>
              <TouchableOpacity onPress={handleCloseModal}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Mode Selector */}
            {!isRescheduling && (
              <View style={styles.modeSelector}>
                <TouchableOpacity
                  style={[styles.modeOption, requestMode === 'text' && styles.activeModeOption]}
                  onPress={switchToTextMode}
                >
                  <Ionicons
                    name="create-outline"
                    size={20}
                    color={requestMode === 'text' ? '#fff' : '#3f51b5'}
                  />
                  <Text style={[styles.modeOptionText, requestMode === 'text' && styles.activeModeOptionText]}>
                    Text
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.modeOption, requestMode === 'voice' && styles.activeModeOption]}
                  onPress={switchToVoiceMode}
                >
                  <Ionicons
                    name="mic-outline"
                    size={20}
                    color={requestMode === 'voice' ? '#fff' : '#3f51b5'}
                  />
                  <Text style={[styles.modeOptionText, requestMode === 'voice' && styles.activeModeOptionText]}>
                    Voice
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            <ScrollView style={styles.modalBody}>
              {/* Voice Request Interface */}
              {!isRescheduling && requestMode === 'voice' && (
                <View style={styles.voiceContainer}>
                  {!recognizedText ? (
                    <>
                      <Text style={styles.voiceInstructions}>
                        {isRecording
                          ? "I'm listening... Speak clearly and include doctor name, date, time, and reason."
                          : "Tap the microphone and say something like:"}
                      </Text>

                      {!isRecording && (
                        <Text style={styles.voiceExample}>
                          "I need an appointment with Dr. Johnson next Tuesday at 2 PM for a follow-up consultation"
                        </Text>
                      )}

                      <TouchableOpacity
                        style={[
                          styles.recordButton,
                          isRecording && styles.recordingButton
                        ]}
                        onPress={isRecording ? stopRecording : startRecording}
                      >
                        <Ionicons
                          name={isRecording ? "stop" : "mic"}
                          size={32}
                          color={isRecording ? "#fff" : "#4285F4"}
                        />
                      </TouchableOpacity>

                      {isRecording && (
                        <Text style={styles.recordingText}>Recording... Tap to stop</Text>
                      )}
                    </>
                  ) : processingVoice ? (
                    <View style={styles.processingContainer}>
                      <ActivityIndicator size="large" color="#4285F4" />
                      <Text style={styles.processingText}>Processing your request...</Text>
                    </View>
                  ) : (
                    <View style={styles.resultContainer}>
                      <Text style={styles.recognizedTextTitle}>I heard:</Text>
                      <Text style={styles.recognizedText}>{recognizedText}</Text>

                      {voiceAppointmentDetails && (
                        <View style={styles.appointmentDetails}>
                          <Text style={styles.appointmentDetailsTitle}>Appointment Details:</Text>
                          <View style={styles.appointmentDetail}>
                            <Ionicons name="person" size={18} color="#4285F4" />
                            <Text style={styles.appointmentDetailText}>Doctor: {voiceAppointmentDetails.doctor}</Text>
                          </View>
                          <View style={styles.appointmentDetail}>
                            <Ionicons name="calendar" size={18} color="#4285F4" />
                            <Text style={styles.appointmentDetailText}>Date: {voiceAppointmentDetails.date}</Text>
                          </View>
                          <View style={styles.appointmentDetail}>
                            <Ionicons name="time" size={18} color="#4285F4" />
                            <Text style={styles.appointmentDetailText}>Time: {voiceAppointmentDetails.time}</Text>
                          </View>
                          <View style={styles.appointmentDetail}>
                            <Ionicons name="medical" size={18} color="#4285F4" />
                            <Text style={styles.appointmentDetailText}>Reason: {voiceAppointmentDetails.reason}</Text>
                          </View>
                        </View>
                      )}

                      <View style={styles.actionButtons}>
                        <TouchableOpacity
                          style={[styles.actionButton, styles.secondaryActionButton]}
                          onPress={() => {
                            setRecognizedText('');
                            setVoiceAppointmentDetails(null);
                          }}
                        >
                          <Text style={styles.secondaryActionButtonText}>Try Again</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.actionButton, styles.primaryActionButton]}
                          onPress={confirmVoiceAppointment}
                        >
                          <Text style={styles.primaryActionButtonText}>Confirm</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                </View>
              )}

              {/* Text Request Interface */}
              {(requestMode === 'text' || isRescheduling) && (
                <>
                  {!isRescheduling && (
                    <>
                      <Text style={styles.inputLabel}>Doctor</Text>
                      <View style={styles.doctorSelection}>
                        {doctors.map((doctor) => (
                          <TouchableOpacity
                            key={doctor.id}
                            style={[
                              styles.doctorOption,
                              selectedDoctor?.id === doctor.id && styles.selectedDoctorOption
                            ]}
                            onPress={() => {
                              setSelectedDoctor(doctor);
                              setAvailabilityStatus(null);
                            }}
                          >
                            <Text
                              style={[
                                styles.doctorOptionText,
                                selectedDoctor?.id === doctor.id && styles.selectedDoctorText
                              ]}
                            >
                              {doctor.name}
                            </Text>
                            <Text
                              style={[
                                styles.doctorSpecialty,
                                selectedDoctor?.id === doctor.id && styles.selectedDoctorText
                              ]}
                            >
                              {doctor.specialty}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </>
                  )}

                  {isRescheduling && appointmentToReschedule && (
                <View style={styles.reschedulingInfo}>
                  <Text style={styles.inputLabel}>Current Appointment</Text>
                  <View style={styles.currentAppointmentDetails}>
                    <Text style={styles.currentAppointmentText}>
                      <Text style={styles.appointmentLabel}>Doctor: </Text>
                      {appointmentToReschedule.doctor}
                    </Text>
                    <View style={styles.dateChangeContainer}>
                      <View style={styles.oldDateContainer}>
                        <Text style={styles.currentAppointmentText}>
                          <Text style={styles.appointmentLabel}>Current Date: </Text>
                          {appointmentToReschedule.date}
                        </Text>
                        <Text style={styles.currentAppointmentText}>
                          <Text style={styles.appointmentLabel}>Current Time: </Text>
                          {appointmentToReschedule.time}
                        </Text>
                        <View style={styles.oldDateBadge}>
                          <Text style={styles.oldDateBadgeText}>Current</Text>
                        </View>
                      </View>
                      <Ionicons name="arrow-forward" size={24} color="#757575" style={styles.dateChangeArrow} />
                      {selectedTime ? (
                        <View style={styles.newDateContainer}>
                          <Text style={styles.newAppointmentText}>
                            <Text style={styles.appointmentLabel}>New Date: </Text>
                            {formatDateForList(selectedDate)}
                          </Text>
                          <Text style={styles.newAppointmentText}>
                            <Text style={styles.appointmentLabel}>New Time: </Text>
                            {selectedTime}
                          </Text>
                          <View style={styles.newDateBadge}>
                            <Text style={styles.newDateBadgeText}>New</Text>
                          </View>
                        </View>
                      ) : (
                        <View style={styles.selectNewDatePrompt}>
                          <Text style={styles.selectNewDateText}>Please select a new date and time</Text>
                        </View>
                      )}
                    </View>
                  </View>
                </View>
              )}

              <Text style={styles.inputLabel}>{isRescheduling ? 'New Date & Time' : 'Preferred Date & Time'}</Text>
              <TouchableOpacity
                style={[styles.dateInput, isRescheduling && styles.newDateInput]}
                onPress={openDatePicker}
              >
                <Ionicons name="calendar" size={20} color={isRescheduling ? "#4CAF50" : "#3f51b5"} />
                <Text style={[styles.dateText, isRescheduling && styles.newDateText]}>
                  {formatDate(selectedDate)}{selectedTime ? ` at ${selectedTime}` : ''}
                </Text>
                {isRescheduling && (
                  <View style={styles.newDateBadge}>
                    <Text style={styles.newDateBadgeText}>New</Text>
                  </View>
                )}
              </TouchableOpacity>

              {/* Reason for Visit Field */}
              <Text style={styles.inputLabel}>Reason for Visit</Text>
              <TextInput
                style={styles.reasonInput}
                placeholder="Please describe the reason for your appointment"
                multiline
                numberOfLines={3}
                value={reasonForVisit}
                onChangeText={setReasonForVisit}
              />

              <View style={styles.availabilitySection}>
                <TouchableOpacity
                  style={[
                    styles.checkAvailabilityButton,
                    availabilityChecking && styles.checkingButton
                  ]}
                  onPress={checkAvailability}
                  disabled={!selectedDoctor || availabilityChecking || availabilityStatus === 'available'}
                >
                  {availabilityChecking ? (
                    <View style={styles.checkingContainer}>
                      <ActivityIndicator size="small" color="#fff" />
                      <Text style={styles.checkAvailabilityText}>Checking...</Text>
                    </View>
                  ) : availabilityStatus === 'available' ? (
                    <View style={styles.checkingContainer}>
                      <Ionicons name="checkmark-circle" size={20} color="#fff" />
                      <Text style={styles.checkAvailabilityText}>
                        Availability Confirmed
                      </Text>
                    </View>
                  ) : (
                    <Text style={styles.checkAvailabilityText}>
                      Check Availability
                    </Text>
                  )}
                </TouchableOpacity>

                {availabilityStatus === 'available' && (
                  <View style={styles.availabilityResult}>
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color="#4CAF50"
                    />
                    <Text
                      style={[
                        styles.availabilityText,
                        { color: '#4CAF50' }
                      ]}
                    >
                      Time slot is available for booking
                    </Text>
                  </View>
                )}

                {availabilityStatus === 'unavailable' && (
                  <View style={styles.availabilityResult}>
                    <Ionicons
                      name="close-circle"
                      size={20}
                      color="#F44336"
                    />
                    <Text
                      style={[
                        styles.availabilityText,
                        { color: '#F44336' }
                      ]}
                    >
                      Selected time slot is not available
                    </Text>
                  </View>
                )}

                {/* Suggested Time Slots */}
                {availabilityStatus === 'unavailable' && (
                  <View style={styles.suggestedTimesContainer}>
                    <Text style={styles.suggestedTimesTitle}>Available time slots:</Text>
                    {suggestedTimeSlots.length > 0 ? (
                      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                        <View style={styles.suggestedTimesOptions}>
                          {suggestedTimeSlots.map((time) => (
                            <TouchableOpacity
                              key={time}
                              style={styles.suggestedTimeOption}
                              onPress={() => {
                                console.log('Selected suggested time:', time);
                                handleSelectSuggestedTime(time);
                              }}
                            >
                              <Ionicons name="time-outline" size={16} color="#3f51b5" />
                              <Text style={styles.suggestedTimeText}>{time}</Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      </ScrollView>
                    ) : (
                      <Text style={styles.noSuggestionsText}>No alternative time slots available. Please try another date.</Text>
                    )}
                  </View>
                )}
              </View>

              <TouchableOpacity
                style={[
                  styles.submitButton,
                  ((!selectedDoctor && !isRescheduling) || !selectedTime)
                    && styles.disabledButton
                ]}
                onPress={submitAppointmentRequest}
                disabled={(!selectedDoctor && !isRescheduling) || !selectedTime}
              >
                <Text style={styles.submitButtonText}>
                  {isRescheduling ? 'Confirm Rescheduling' : 'Submit Request'}
                </Text>
              </TouchableOpacity>
                </>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Date Picker Modal (Custom) */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={datePickerVisible}
        onRequestClose={cancelDateSelection}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.datePickerModal}>
            <View style={styles.datePickerHeader}>
              <Text style={styles.datePickerTitle}>Select a Date & Time</Text>
              <TouchableOpacity onPress={cancelDateSelection}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateScrollView}>
              <View style={styles.dateOptionsContainer}>
                {getAvailableDates().map(renderDateOption)}
              </View>
            </ScrollView>

            <View style={styles.timeSelectionContainer}>
              <Text style={styles.timeSelectionTitle}>Select Time</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.timeScrollView}>
                <View style={styles.timeOptionsContainer}>
                  {['9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM'].map(time => (
                    <TouchableOpacity
                      key={time}
                      style={[
                        styles.timeOption,
                        selectedTime === time && styles.selectedTimeOption
                      ]}
                      onPress={() => setSelectedTime(time)}
                    >
                      <Text style={[styles.timeOptionText, selectedTime === time && styles.selectedTimeText]}>
                        {time}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View style={styles.datePickerActions}>
              <TouchableOpacity
                style={styles.cancelDateButton}
                onPress={cancelDateSelection}
              >
                <Text style={styles.cancelDateText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmDateButton}
                onPress={confirmDate}
                disabled={!selectedTime}
              >
                <Text style={styles.confirmDateText}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Appointment Details Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={detailsModalVisible}
        onRequestClose={handleCloseDetailsModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Appointment Details</Text>
              <TouchableOpacity onPress={handleCloseDetailsModal}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {selectedAppointment && (
                <>
                  <View style={styles.detailsHeader}>
                    <View style={[styles.statusBadge, { backgroundColor: selectedAppointment.status === 'Confirmed' ? '#4CAF50' : '#FFC107' }]}>
                      <Text style={styles.statusText}>{selectedAppointment.status}</Text>
                    </View>
                    <Text style={styles.detailsTitle}>{selectedAppointment.doctor}</Text>
                    <Text style={styles.detailsSubtitle}>{selectedAppointment.specialty}</Text>
                  </View>

                  <View style={styles.detailsSection}>
                    <Text style={styles.detailsSectionTitle}>Appointment Information</Text>

                    <View style={styles.detailsRow}>
                      <View style={styles.detailsIconContainer}>
                        <Ionicons name="calendar" size={20} color="#3f51b5" />
                      </View>
                      <View style={styles.detailsContent}>
                        <Text style={styles.detailsLabel}>Date</Text>
                        <Text style={styles.detailsValue}>{selectedAppointment.date}</Text>
                      </View>
                    </View>

                    <View style={styles.detailsRow}>
                      <View style={styles.detailsIconContainer}>
                        <Ionicons name="time" size={20} color="#3f51b5" />
                      </View>
                      <View style={styles.detailsContent}>
                        <Text style={styles.detailsLabel}>Time</Text>
                        <Text style={styles.detailsValue}>{selectedAppointment.time}</Text>
                      </View>
                    </View>

                    <View style={styles.detailsRow}>
                      <View style={styles.detailsIconContainer}>
                        <Ionicons name="medical" size={20} color="#3f51b5" />
                      </View>
                      <View style={styles.detailsContent}>
                        <Text style={styles.detailsLabel}>Reason for Visit</Text>
                        <Text style={styles.detailsValue}>{selectedAppointment.reason || 'General consultation'}</Text>
                      </View>
                    </View>

                    {/* Afficher les informations de replanification si disponibles */}
                    {selectedAppointment.previousDate && selectedAppointment.previousTime && (
                      <View style={styles.detailsRow}>
                        <View style={styles.detailsIconContainer}>
                          <Ionicons name="refresh-circle" size={20} color="#FF9800" />
                        </View>
                        <View style={styles.detailsContent}>
                          <Text style={styles.detailsLabel}>Rescheduled</Text>
                          <Text style={styles.detailsValue}>
                            From {selectedAppointment.previousDate} at {selectedAppointment.previousTime}
                          </Text>
                          <Text style={styles.detailsSecondaryValue}>
                            To {selectedAppointment.date} at {selectedAppointment.time}
                          </Text>
                        </View>
                      </View>
                    )}

                    <View style={styles.detailsRow}>
                      <View style={styles.detailsIconContainer}>
                        <Ionicons name="location" size={20} color="#3f51b5" />
                      </View>
                      <View style={styles.detailsContent}>
                        <Text style={styles.detailsLabel}>Location</Text>
                        <Text style={styles.detailsValue}>Neuro Care Medical Center</Text>
                        <Text style={styles.detailsSecondaryValue}>123 Medical Plaza, Suite 456</Text>
                        <Text style={styles.detailsSecondaryValue}>New York, NY 10001</Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.detailsSection}>
                    <Text style={styles.detailsSectionTitle}>Doctor Information</Text>

                    <View style={styles.doctorInfoContainer}>
                      <View style={styles.doctorAvatarContainer}>
                        <Ionicons name="person-circle" size={60} color="#3f51b5" />
                      </View>
                      <View style={styles.doctorInfo}>
                        <Text style={styles.doctorName}>{selectedAppointment.doctor}</Text>
                        <Text style={styles.doctorSpecialty}>{selectedAppointment.specialty}</Text>
                        <Text style={styles.doctorCredentials}>Board Certified</Text>
                        <View style={styles.doctorRating}>
                          <Ionicons name="star" size={16} color="#FFC107" />
                          <Ionicons name="star" size={16} color="#FFC107" />
                          <Ionicons name="star" size={16} color="#FFC107" />
                          <Ionicons name="star" size={16} color="#FFC107" />
                          <Ionicons name="star-half" size={16} color="#FFC107" />
                          <Text style={styles.doctorRatingText}>4.5 (120 reviews)</Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  <View style={styles.detailsSection}>
                    <Text style={styles.detailsSectionTitle}>Special Instructions</Text>
                    <Text style={styles.instructionsText}>
                      Please arrive 15 minutes before your scheduled appointment time.
                      Bring your insurance card and a list of current medications.
                      If this is your first visit, please complete the new patient forms available on our website.
                    </Text>
                  </View>

                  <View style={styles.detailsActions}>
                    {/* Only show action buttons if appointment is not cancelled or completed */}
                    {selectedAppointment.status &&
                     selectedAppointment.status.toLowerCase() !== 'cancelled' &&
                     selectedAppointment.status.toLowerCase() !== 'completed' ? (
                      <>
                        <TouchableOpacity
                          style={styles.detailsActionButton}
                          onPress={() => {
                            handleCloseDetailsModal();
                            handleRescheduleAppointment(selectedAppointment);
                          }}
                        >
                          <Ionicons name="calendar" size={20} color="#3f51b5" />
                          <Text style={styles.detailsActionText}>Reschedule</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.detailsActionButton, styles.cancelActionButton]}
                          onPress={() => {
                            handleCloseDetailsModal();
                            handleCancelAppointment(selectedAppointment);
                          }}
                        >
                          <Ionicons name="close-circle" size={20} color="#F44336" />
                          <Text style={[styles.detailsActionText, { color: '#F44336' }]}>Cancel</Text>
                        </TouchableOpacity>
                      </>
                    ) : (
                      <Text style={styles.noActionsText}>
                        {selectedAppointment.status && selectedAppointment.status.toLowerCase() === 'cancelled'
                          ? 'This appointment has been cancelled.'
                          : 'This appointment has been completed.'}
                      </Text>
                    )}
                  </View>
                </>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Cancellation Reason Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={cancelReasonModalVisible}
        onRequestClose={closeCancelReasonModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Cancellation Reason</Text>
              <TouchableOpacity onPress={closeCancelReasonModal}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={styles.inputLabel}>Please provide a reason for cancelling this appointment:</Text>
              <TextInput
                style={styles.reasonInput}
                placeholder="Enter cancellation reason"
                multiline
                numberOfLines={3}
                value={cancellationReason}
                onChangeText={setCancellationReason}
              />

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={closeCancelReasonModal}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={confirmCancellation}
                >
                  <Text style={styles.confirmButtonText}>Confirm Cancellation</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: '#fff',
    elevation: 2,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: '#fff',
    elevation: 2,
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#e8eaf6',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#c5cae9',
    flex: 1,
    marginRight: 8,
    height: 44,
  },
  filterButtonText: {
    color: '#3f51b5',
    fontWeight: 'bold',
    marginLeft: 6,
    fontSize: 14,
  },
  requestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3f51b5',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1.5,
    height: 44,
  },
  modeSelector: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modeOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  activeModeOption: {
    backgroundColor: '#3f51b5',
  },
  modeOptionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3f51b5',
    marginLeft: 8,
  },
  activeModeOptionText: {
    color: '#fff',
  },
  requestButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 6,
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    marginTop: 8,
  },
  listContainer: {
    padding: 16,
  },
  card: {
    margin: 12,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: 'transparent', // Important for the gradient to be visible
    marginBottom: 16,
  },
  cardGradient: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  cardContent: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  dateContainer: {
    flex: 1,
  },
  dateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  avatarIcon: {
    marginLeft: 8,
  },
  cardDivider: {
    height: 1,
    marginVertical: 12,
    backgroundColor: '#e0e0e0',
  },
  cardDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  cardFooter: {
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 3,
    paddingHorizontal: 8,
    backgroundColor: '#f5f7fa',
    borderRadius: 8,
    marginLeft: 'auto',
  },
  viewDetailsText: {
    color: '#4CAF50',
    fontWeight: 'bold',
    marginRight: 4,
    fontSize: 14,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    width: '100%',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  actionButtonText: {
    fontWeight: 'bold',
    color: '#3f51b5',
    fontSize: 12,
  },
  buttonIcon: {
    marginRight: 4,
  },
  rescheduleButton: {
    backgroundColor: '#fff3e0',
    borderWidth: 1,
    borderColor: '#ffcc80',
  },
  rescheduleButtonText: {
    color: '#ff9800',
  },
  cancelButton: {
    backgroundColor: '#ffebee',
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  cancelButtonText: {
    color: '#f44336',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    marginTop: 12,
  },
  doctorSelection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  doctorOption: {
    width: '48%',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedDoctorOption: {
    backgroundColor: '#e8eaf6',
    borderColor: '#3f51b5',
  },
  doctorOptionText: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 4,
  },
  doctorSpecialty: {
    fontSize: 12,
    color: '#666',
  },
  selectedDoctorText: {
    color: '#3f51b5',
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  newDateInput: {
    backgroundColor: '#e8f5e9',
    borderWidth: 1,
    borderColor: '#a5d6a7',
  },
  dateText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  newDateText: {
    color: '#2e7d32',
    fontWeight: 'bold',
  },
  newDateBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  newDateBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  reasonInput: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    textAlignVertical: 'top',
    minHeight: 100,
  },
  availabilitySection: {
    marginVertical: 16,
  },
  checkAvailabilityButton: {
    backgroundColor: '#3f51b5',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  checkingButton: {
    backgroundColor: '#7986cb', // Lighter color when checking
  },
  checkingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkAvailabilityText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  availabilityResult: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
  },
  availabilityText: {
    marginLeft: 8,
    flex: 1,
  },
  suggestedTimesContainer: {
    marginTop: 16,
    backgroundColor: '#E8F5E9',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  suggestedTimesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 12,
  },
  suggestedTimesOptions: {
    flexDirection: 'row',
    paddingBottom: 8,
  },
  suggestedTimeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#3f51b5',
  },
  suggestedTimeText: {
    marginLeft: 4,
    color: '#3f51b5',
    fontWeight: 'bold',
  },
  noSuggestionsText: {
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
    textAlign: 'center',
  },

  submitButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  reschedulingInfo: {
    marginBottom: 16,
  },
  currentAppointmentDetails: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  currentAppointmentText: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  newAppointmentText: {
    fontSize: 14,
    marginBottom: 8,
    color: '#2e7d32',
    fontWeight: 'bold',
  },
  appointmentLabel: {
    fontWeight: 'bold',
  },
  dateChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  oldDateContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    position: 'relative',
  },
  newDateContainer: {
    flex: 1,
    backgroundColor: '#e8f5e9',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#a5d6a7',
    position: 'relative',
  },
  dateChangeArrow: {
    marginHorizontal: 8,
  },
  oldDateBadge: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: '#757575',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  oldDateBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  selectNewDatePrompt: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectNewDateText: {
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  // Custom Date Picker Modal
  datePickerModal: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dateScrollView: {
    maxHeight: 300,
  },
  dateOptionsContainer: {
    flexDirection: 'row',
    padding: 16,
  },
  dateOption: {
    width: 70,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 8,
  },
  selectedDateOption: {
    backgroundColor: '#3f51b5',
  },
  dateOptionDay: {
    fontSize: 13,
    color: '#666',
    marginBottom: 4,
  },
  dateOptionDate: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  dateOptionMonth: {
    fontSize: 13,
    color: '#666',
  },
  selectedDateText: {
    color: 'white',
  },
  datePickerActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  cancelDateButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  cancelDateText: {
    color: '#666',
    fontWeight: 'bold',
  },
  confirmDateButton: {
    backgroundColor: '#3f51b5',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  confirmDateText: {
    color: 'white',
    fontWeight: 'bold',
  },
  timeSelectionContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  timeSelectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  timeScrollView: {
    maxHeight: 80,
  },
  timeOptionsContainer: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  timeOption: {
    width: 80,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedTimeOption: {
    backgroundColor: '#3f51b5',
    borderColor: '#3f51b5',
  },
  timeOptionText: {
    fontSize: 14,
    color: '#333',
  },
  selectedTimeText: {
    color: 'white',
    fontWeight: 'bold',
  },
  // Voice request styles
  voiceContainer: {
    padding: 20,
    alignItems: 'center',
  },
  voiceInstructions: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 15,
  },
  voiceExample: {
    fontSize: 14,
    color: '#757575',
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    borderWidth: 2,
    borderColor: '#4285F4',
  },
  recordingButton: {
    backgroundColor: '#EA4335',
    borderColor: '#EA4335',
  },
  recordingText: {
    fontSize: 14,
    color: '#EA4335',
    marginTop: 10,
  },
  processingContainer: {
    alignItems: 'center',
    padding: 30,
  },
  processingText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 15,
  },
  resultContainer: {
    width: '100%',
    padding: 10,
  },
  recognizedTextTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  recognizedText: {
    fontSize: 16,
    color: '#333',
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  appointmentDetails: {
    backgroundColor: '#E8F5E9',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  appointmentDetailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  appointmentDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  appointmentDetailText: {
    fontSize: 15,
    color: '#333',
    marginLeft: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  primaryActionButton: {
    backgroundColor: '#4285F4',
  },
  primaryActionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  secondaryActionButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#BDBDBD',
  },
  secondaryActionButtonText: {
    color: '#757575',
    fontWeight: 'bold',
    fontSize: 16,
  },
  // Appointment Details Modal Styles
  detailsHeader: {
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  statusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  detailsTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    textAlign: 'center',
  },
  detailsSubtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  detailsSection: {
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  detailsSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  detailsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8EAF6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  detailsContent: {
    flex: 1,
  },
  detailsLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  detailsValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  detailsSecondaryValue: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  doctorInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  doctorAvatarContainer: {
    marginRight: 16,
  },
  doctorInfo: {
    flex: 1,
  },
  doctorName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  doctorCredentials: {
    fontSize: 14,
    color: '#4CAF50',
    marginBottom: 4,
  },
  doctorRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doctorRatingText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  instructionsText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  detailsActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    marginBottom: 16,
  },
  detailsActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  detailsActionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3f51b5',
    marginLeft: 8,
  },
  // Cancellation modal styles
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  confirmButton: {
    backgroundColor: '#f44336',
    borderRadius: 8,
    padding: 12,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelActionButton: {
    borderColor: '#ffcdd2',
    backgroundColor: '#ffebee',
  },
  noActionsText: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#666',
    textAlign: 'center',
    padding: 10,
  },
  // Guide styles
  appointmentGuide: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    elevation: 2,
    marginHorizontal: 16,
  },
  guideTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  guideItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingVertical: 4,
  },
  guideText: {
    fontSize: 14,
    color: '#555',
    marginLeft: 8,
    flex: 1,
  },
  guideHighlight: {
    fontWeight: 'bold',
    color: '#3f51b5',
  },
  // Styles pour l'indicateur de replanification
  rescheduledBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 4,
    marginTop: 8,
    marginBottom: 4,
    alignSelf: 'flex-start',
    borderLeftWidth: 3,
    borderLeftColor: '#FF9800',
  },
  rescheduledText: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  // Styles pour afficher la date précédente
  previousDateContainer: {
    backgroundColor: '#F5F5F5',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
    borderLeftWidth: 2,
    borderLeftColor: '#9E9E9E',
  },
  previousDateText: {
    fontSize: 11,
    color: '#757575',
    fontStyle: 'italic',
  },
  // Les styles du bouton de rafraîchissement ont été supprimés
  // Styles pour le conteneur de débogage
  debugContainer: {
    backgroundColor: '#FFECB3',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
    marginBottom: 8,
    borderLeftWidth: 2,
    borderLeftColor: '#FFA000',
  },
  debugText: {
    fontSize: 10,
    color: '#333',
    fontFamily: 'monospace',
  },
});

export default Appointments;