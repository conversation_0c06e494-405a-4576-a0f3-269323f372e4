import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView,
  Dimensions,
  Image
} from 'react-native';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ROLE_COLORS } from '../config/theme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db, auth } from '../config/firebase';
import { doc, updateDoc } from 'firebase/firestore';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Google Maps style - Standard style with slight modifications for better readability
const googleMapsStyle = [
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.business",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "labels.text",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "on"
      }
    ]
  },
  {
    "featureType": "transit",
    "stylers": [
      {
        "visibility": "on"
      }
    ]
  }
];

// Storage keys
const SAVED_LOCATIONS_KEY = '@neurocare:saved_locations';
const SAVED_ROUTES_KEY = '@neurocare:saved_routes';

const MapScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const patientColors = ROLE_COLORS.patient;

  // Get guidance data from route params if available
  const { guidanceData, mode } = route.params || {};
  const isNavigationMode = mode === 'navigation' && guidanceData;

  // State variables
  const [loading, setLoading] = useState(true);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [savedLocations, setSavedLocations] = useState([]);
  const [savedRoutes, setSavedRoutes] = useState([]);
  const [showSaveLocationModal, setShowSaveLocationModal] = useState(false);
  const [showSaveRouteModal, setShowSaveRouteModal] = useState(false);
  const [locationName, setLocationName] = useState('');
  const [locationNotes, setLocationNotes] = useState('');
  const [routeName, setRouteName] = useState('');
  const [routeNotes, setRouteNotes] = useState('');
  const [showSavedLocations, setShowSavedLocations] = useState(false);
  const [showSavedRoutes, setShowSavedRoutes] = useState(false);
  const [isTrackingRoute, setIsTrackingRoute] = useState(false);
  const [currentRouteCoordinates, setCurrentRouteCoordinates] = useState([]);
  const [locationToSave, setLocationToSave] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [navigationActive, setNavigationActive] = useState(isNavigationMode);
  const [navigationDestination, setNavigationDestination] = useState(
    isNavigationMode ? guidanceData.destination : null
  );
  const [navigationInstructions, setNavigationInstructions] = useState(
    isNavigationMode ? guidanceData.instructions : null
  );
  const [showNavigationModal, setShowNavigationModal] = useState(false);

  // Map specific states
  const [mapRegion, setMapRegion] = useState(null);
  const [destinationCoords, setDestinationCoords] = useState(
    isNavigationMode && guidanceData?.destinationCoords ? guidanceData.destinationCoords : null
  );
  const [routeCoordinates, setRouteCoordinates] = useState(
    isNavigationMode && guidanceData?.routeCoordinates ? guidanceData.routeCoordinates : []
  );
  const [routeDistance, setRouteDistance] = useState(
    isNavigationMode && guidanceData?.routeDistance ? guidanceData.routeDistance : null
  );
  const [routeDuration, setRouteDuration] = useState(
    isNavigationMode && guidanceData?.routeDuration ? guidanceData.routeDuration : null
  );

  // Refs
  const mapRef = useRef(null);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Request location permissions
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          setErrorMessage('Permission to access location was denied');
          setLoading(false);
          return;
        }

        // Get current location
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });

        if (location) {
          const currentCoords = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };

          setCurrentLocation(currentCoords);

          // Set initial map region
          const initialRegion = {
            ...currentCoords,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          };

          setMapRegion(initialRegion);

          // If in navigation mode and we have destination coordinates, adjust the map region
          if (isNavigationMode && destinationCoords) {
            // We'll fit the map to show both current location and destination later
            // when the map is ready
          }
        } else {
          // Default location if user location is not available
          const defaultCoords = {
            latitude: 48.8566, // Paris coordinates as fallback
            longitude: 2.3522,
          };

          setCurrentLocation(defaultCoords);

          // Set default map region
          setMapRegion({
            ...defaultCoords,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });

          console.log('Using default location as fallback');
        }

        // Load saved locations and routes
        const savedLocationsJson = await AsyncStorage.getItem(SAVED_LOCATIONS_KEY);
        const savedRoutesJson = await AsyncStorage.getItem(SAVED_ROUTES_KEY);

        setSavedLocations(savedLocationsJson ? JSON.parse(savedLocationsJson) : []);
        setSavedRoutes(savedRoutesJson ? JSON.parse(savedRoutesJson) : []);

        // If in navigation mode, show the navigation modal
        if (isNavigationMode) {
          setShowNavigationModal(true);

          // Update guidance status to active if it's from a supervisor
          if (guidanceData && guidanceData.id) {
            try {
              const guidanceRef = doc(db, 'patientGuidance', guidanceData.id);
              await updateDoc(guidanceRef, {
                status: 'active'
              });
            } catch (error) {
              console.error('Error updating guidance status:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
        setErrorMessage('Failed to load map data. Please try again.');
        // Set default location in case of error
        setCurrentLocation({
          latitude: 48.8566, // Paris coordinates as fallback
          longitude: 2.3522,
        });
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();

    // Clean up function
    return () => {
      if (isTrackingRoute) {
        stopRouteTracking();
      }
    };
  }, [isNavigationMode, guidanceData]);

  // Handle saving a location
  const handleSaveLocation = async () => {
    if (!locationName.trim()) {
      Alert.alert('Error', 'Please enter a name for this location');
      return;
    }

    try {
      // Get current location if locationToSave is not set
      const locationData = locationToSave || currentLocation;

      if (!locationData) {
        Alert.alert('Error', 'Unable to determine location. Please try again.');
        return;
      }

      const newLocation = {
        id: Date.now().toString(),
        name: locationName.trim(),
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        notes: locationNotes.trim(),
        createdAt: new Date().toISOString(),
      };

      // Get existing saved locations
      const savedLocationsJson = await AsyncStorage.getItem(SAVED_LOCATIONS_KEY);
      const existingLocations = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];

      // Add the new location
      const updatedLocations = [...existingLocations, newLocation];

      // Save to AsyncStorage
      await AsyncStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(updatedLocations));

      // Update state
      setSavedLocations(updatedLocations);
      setShowSaveLocationModal(false);
      setLocationName('');
      setLocationNotes('');
      setLocationToSave(null);

      Alert.alert('Success', 'Location saved successfully');
    } catch (error) {
      console.error('Error saving location:', error);
      Alert.alert('Error', 'Failed to save location. Please try again.');
    }
  };

  // Start tracking a route
  const startRouteTracking = async () => {
    try {
      // Reset current route
      setCurrentRouteCoordinates([]);

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.BestForNavigation,
      });

      if (location) {
        const initialCoordinate = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          timestamp: new Date().toISOString(),
        };

        setCurrentRouteCoordinates([initialCoordinate]);
        setIsTrackingRoute(true);

        // Start watching position
        const locationSubscription = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.BestForNavigation,
            timeInterval: 5000,
            distanceInterval: 10,
          },
          (newLocation) => {
            setCurrentRouteCoordinates(prevCoordinates => [
              ...prevCoordinates,
              {
                latitude: newLocation.coords.latitude,
                longitude: newLocation.coords.longitude,
                timestamp: new Date().toISOString(),
              }
            ]);
          }
        );

        // Store the subscription for cleanup
        return locationSubscription;
      } else {
        Alert.alert('Error', 'Could not get current location. Please try again.');
        return null;
      }
    } catch (error) {
      console.error('Error starting route tracking:', error);
      Alert.alert('Error', 'Failed to start route tracking. Please try again.');
      return null;
    }
  };

  // Stop tracking a route
  const stopRouteTracking = () => {
    setIsTrackingRoute(false);

    if (currentRouteCoordinates.length > 1) {
      setShowSaveRouteModal(true);
    } else {
      Alert.alert('Route Too Short', 'The route is too short to save.');
      setCurrentRouteCoordinates([]);
    }
  };

  // Handle saving a route
  const handleSaveRoute = async () => {
    if (!routeName.trim()) {
      Alert.alert('Error', 'Please enter a name for this route');
      return;
    }

    if (currentRouteCoordinates.length < 2) {
      Alert.alert('Error', 'Route is too short to save');
      return;
    }

    try {
      const newRoute = {
        id: Date.now().toString(),
        name: routeName.trim(),
        coordinates: currentRouteCoordinates,
        notes: routeNotes.trim(),
        createdAt: new Date().toISOString(),
      };

      // Get existing saved routes
      const savedRoutesJson = await AsyncStorage.getItem(SAVED_ROUTES_KEY);
      const existingRoutes = savedRoutesJson ? JSON.parse(savedRoutesJson) : [];

      // Add the new route
      const updatedRoutes = [...existingRoutes, newRoute];

      // Save to AsyncStorage
      await AsyncStorage.setItem(SAVED_ROUTES_KEY, JSON.stringify(updatedRoutes));

      // Update state
      setSavedRoutes(updatedRoutes);
      setShowSaveRouteModal(false);
      setRouteName('');
      setRouteNotes('');
      setCurrentRouteCoordinates([]);

      Alert.alert('Success', 'Route saved successfully');
    } catch (error) {
      console.error('Error saving route:', error);
      Alert.alert('Error', 'Failed to save route. Please try again.');
    }
  };

  // Handle deleting a location
  const handleDeleteLocation = async (locationId) => {
    try {
      // Get existing saved locations
      const savedLocationsJson = await AsyncStorage.getItem(SAVED_LOCATIONS_KEY);
      const existingLocations = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];

      // Filter out the location to delete
      const updatedLocations = existingLocations.filter(location => location.id !== locationId);

      // Save to AsyncStorage
      await AsyncStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(updatedLocations));

      // Update state
      setSavedLocations(updatedLocations);

      Alert.alert('Success', 'Location deleted successfully');
    } catch (error) {
      console.error('Error deleting location:', error);
      Alert.alert('Error', 'Failed to delete location. Please try again.');
    }
  };

  // Handle deleting a route
  const handleDeleteRoute = async (routeId) => {
    try {
      // Get existing saved routes
      const savedRoutesJson = await AsyncStorage.getItem(SAVED_ROUTES_KEY);
      const existingRoutes = savedRoutesJson ? JSON.parse(savedRoutesJson) : [];

      // Filter out the route to delete
      const updatedRoutes = existingRoutes.filter(route => route.id !== routeId);

      // Save to AsyncStorage
      await AsyncStorage.setItem(SAVED_ROUTES_KEY, JSON.stringify(updatedRoutes));

      // Update state
      setSavedRoutes(updatedRoutes);

      Alert.alert('Success', 'Route deleted successfully');
    } catch (error) {
      console.error('Error deleting route:', error);
      Alert.alert('Error', 'Failed to delete route. Please try again.');
    }
  };

  // Get static map URL for current location (legacy function, kept for backward compatibility)
  const getStaticMapUrl = (latitude, longitude, zoom = 14, width = 600, height = 300) => {
    return `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&zoom=${zoom}&size=${width}x${height}&markers=color:red%7C${latitude},${longitude}&key=YOUR_API_KEY`;
  };

  // Function to fit map to show both current location and destination
  const fitMapToCoordinates = (coordinates) => {
    if (!mapRef.current || !coordinates || coordinates.length === 0) {
      return;
    }

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
      animated: true,
    });
  };

  // Effect to fit map when coordinates change
  useEffect(() => {
    if (mapRef.current && currentLocation && destinationCoords && navigationActive) {
      const coordinatesToFit = [
        currentLocation,
        destinationCoords
      ];

      // If we have route coordinates, use those instead
      if (routeCoordinates && routeCoordinates.length > 0) {
        fitMapToCoordinates(routeCoordinates);
      } else {
        fitMapToCoordinates(coordinatesToFit);
      }
    }
  }, [currentLocation, destinationCoords, navigationActive, routeCoordinates]);

  // Render loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={patientColors.primary} />
        <Text style={styles.loadingText}>Loading map...</Text>
      </View>
    );
  }

  // Handle completing navigation
  const handleCompleteNavigation = async () => {
    if (guidanceData && guidanceData.id) {
      try {
        const guidanceRef = doc(db, 'patientGuidance', guidanceData.id);
        await updateDoc(guidanceRef, {
          status: 'completed'
        });

        Alert.alert(
          'Navigation Completed',
          'You have successfully reached your destination!',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } catch (error) {
        console.error('Error updating guidance status:', error);
        Alert.alert('Error', 'Failed to update navigation status. Please try again.');
      }
    } else {
      setNavigationActive(false);
      setShowNavigationModal(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Map View */}
      <View style={styles.mapContainer}>
        {mapRegion ? (
          <>
            <MapView
              ref={mapRef}
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              initialRegion={mapRegion}
              customMapStyle={googleMapsStyle}
              showsUserLocation={false}
              showsMyLocationButton={false}
              showsCompass={true}
              showsScale={true}
              showsTraffic={false}
              showsBuildings={true}
              showsIndoors={true}
              zoomControlEnabled={true}
              rotateEnabled={true}
              scrollEnabled={true}
              pitchEnabled={true}
            >
              {/* Current Location Marker */}
              {currentLocation && (
                <Marker
                  coordinate={currentLocation}
                  title="Your Location"
                  description="Your current location"
                >
                  <View style={styles.currentLocationMarker}>
                    <View style={styles.currentLocationDot} />
                    <View style={styles.currentLocationHalo} />
                  </View>
                </Marker>
              )}

              {/* Destination Marker (if in navigation mode) */}
              {navigationActive && destinationCoords && (
                <Marker
                  coordinate={destinationCoords}
                  title={navigationDestination || "Destination"}
                  description="Your destination"
                >
                  <View style={styles.destinationMarkerContainer}>
                    <View style={[styles.destinationMarker, { backgroundColor: patientColors.primary }]}>
                      <Ionicons name="location" size={18} color="#fff" />
                    </View>
                    <View style={[styles.destinationMarkerTail, { borderTopColor: patientColors.primary }]} />
                  </View>
                </Marker>
              )}

              {/* Route Polyline (if in navigation mode) */}
              {navigationActive && routeCoordinates && routeCoordinates.length > 0 && (
                <>
                  {/* Background line (shadow effect) */}
                  <Polyline
                    coordinates={routeCoordinates}
                    strokeWidth={8}
                    strokeColor="rgba(0, 0, 0, 0.2)"
                    lineCap="round"
                    lineJoin="round"
                  />
                  {/* Main route line */}
                  <Polyline
                    coordinates={routeCoordinates}
                    strokeWidth={4}
                    strokeColor="#4285F4"
                    lineCap="round"
                    lineJoin="round"
                  />
                </>
              )}

              {/* Saved Locations Markers */}
              {!navigationActive && savedLocations.map((location) => (
                <Marker
                  key={location.id}
                  coordinate={{
                    latitude: location.latitude,
                    longitude: location.longitude,
                  }}
                  title={location.name}
                  description={location.notes}
                  pinColor="#4CAF50"
                />
              ))}

              {/* Current Route Tracking Polyline */}
              {!navigationActive && isTrackingRoute && currentRouteCoordinates.length > 0 && (
                <Polyline
                  coordinates={currentRouteCoordinates}
                  strokeWidth={3}
                  strokeColor="#FF5722"
                />
              )}
            </MapView>

            {/* Custom Map Controls */}
            <View style={styles.mapControls}>
              <TouchableOpacity
                style={styles.mapControlButton}
                onPress={() => {
                  if (mapRef.current) {
                    mapRef.current.animateToRegion({
                      ...mapRegion,
                      latitudeDelta: mapRegion.latitudeDelta / 2,
                      longitudeDelta: mapRegion.longitudeDelta / 2,
                    }, 300);
                  }
                }}
              >
                <Ionicons name="add" size={24} color="#333" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.mapControlButton}
                onPress={() => {
                  if (mapRef.current) {
                    mapRef.current.animateToRegion({
                      ...mapRegion,
                      latitudeDelta: mapRegion.latitudeDelta * 2,
                      longitudeDelta: mapRegion.longitudeDelta * 2,
                    }, 300);
                  }
                }}
              >
                <Ionicons name="remove" size={24} color="#333" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.mapControlButton}
                onPress={async () => {
                  try {
                    const { status } = await Location.requestForegroundPermissionsAsync();
                    if (status !== 'granted') {
                      Alert.alert('Permission Denied', 'Location permission is required for this feature.');
                      return;
                    }

                    const location = await Location.getCurrentPositionAsync({
                      accuracy: Location.Accuracy.Balanced,
                    });

                    if (location) {
                      const currentCoords = {
                        latitude: location.coords.latitude,
                        longitude: location.coords.longitude,
                      };

                      setCurrentLocation(currentCoords);

                      if (mapRef.current) {
                        mapRef.current.animateToRegion({
                          ...currentCoords,
                          latitudeDelta: 0.01,
                          longitudeDelta: 0.01,
                        }, 300);
                      }
                    }
                  } catch (error) {
                    console.error('Error getting current location:', error);
                    Alert.alert('Error', 'Failed to get current location. Please try again.');
                  }
                }}
              >
                <Ionicons name="locate" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Navigation Info Overlay (if in navigation mode) */}
            {navigationActive && (
              <View style={styles.navigationOverlay}>
                <Text style={styles.navigationTitle}>
                  Navigating to: {navigationDestination}
                </Text>

                {/* Route Information */}
                {routeDistance && routeDuration && (
                  <View style={styles.routeInfoContainer}>
                    <View style={styles.routeInfoItem}>
                      <Ionicons name="speedometer-outline" size={20} color={patientColors.primary} />
                      <Text style={styles.routeInfoText}>
                        {routeDistance.toFixed(2)} km
                      </Text>
                    </View>
                    <View style={styles.routeInfoItem}>
                      <Ionicons name="time-outline" size={20} color={patientColors.primary} />
                      <Text style={styles.routeInfoText}>
                        {routeDuration > 60
                          ? `${Math.floor(routeDuration / 60)}h ${routeDuration % 60}min`
                          : `${routeDuration} min`}
                      </Text>
                    </View>
                  </View>
                )}

                {/* Instructions */}
                {navigationInstructions && (
                  <View style={styles.instructionsContainer}>
                    <Text style={styles.instructionsTitle}>Instructions:</Text>
                    <Text style={styles.instructionsText}>{navigationInstructions}</Text>
                  </View>
                )}
              </View>
            )}
          </>
        ) : (
          <View style={styles.mapErrorContainer}>
            <Text style={styles.mapErrorText}>Unable to load map</Text>
            <TouchableOpacity
              style={styles.mapErrorButton}
              onPress={async () => {
                setLoading(true);
                try {
                  const { status } = await Location.requestForegroundPermissionsAsync();
                  if (status !== 'granted') {
                    setErrorMessage('Permission to access location was denied');
                    return;
                  }

                  const location = await Location.getCurrentPositionAsync({});
                  if (location) {
                    const currentCoords = {
                      latitude: location.coords.latitude,
                      longitude: location.coords.longitude,
                    };

                    setCurrentLocation(currentCoords);
                    setMapRegion({
                      ...currentCoords,
                      latitudeDelta: 0.01,
                      longitudeDelta: 0.01,
                    });
                  } else {
                    // Default location
                    const defaultCoords = {
                      latitude: 48.8566,
                      longitude: 2.3522,
                    };

                    setCurrentLocation(defaultCoords);
                    setMapRegion({
                      ...defaultCoords,
                      latitudeDelta: 0.01,
                      longitudeDelta: 0.01,
                    });
                  }
                } catch (error) {
                  console.error('Error getting location:', error);
                  Alert.alert('Error', 'Failed to get location. Please check your permissions.');
                } finally {
                  setLoading(false);
                }
              }}
            >
              <Text style={styles.mapErrorButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtonsContainer}>
        {navigationActive ? (
          <>
            {/* Current Location Button (Navigation Mode) */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: patientColors.primary }]}
              onPress={async () => {
                try {
                  const location = await Location.getCurrentPositionAsync({
                    accuracy: Location.Accuracy.Balanced,
                  });

                  if (location) {
                    setCurrentLocation({
                      latitude: location.coords.latitude,
                      longitude: location.coords.longitude,
                    });
                  }
                } catch (error) {
                  console.error('Error getting current location:', error);
                  Alert.alert('Error', 'Failed to get current location. Please try again.');
                }
              }}
            >
              <Ionicons name="locate" size={24} color="#fff" />
            </TouchableOpacity>

            {/* Show Navigation Instructions */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#9C27B0' }]}
              onPress={() => setShowNavigationModal(true)}
            >
              <Ionicons name="information-circle" size={24} color="#fff" />
            </TouchableOpacity>

            {/* Complete Navigation */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
              onPress={handleCompleteNavigation}
            >
              <Ionicons name="checkmark-circle" size={24} color="#fff" />
            </TouchableOpacity>

            {/* Cancel Navigation */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#F44336' }]}
              onPress={() => {
                Alert.alert(
                  'Cancel Navigation',
                  'Are you sure you want to cancel navigation?',
                  [
                    { text: 'No', style: 'cancel' },
                    {
                      text: 'Yes',
                      onPress: () => {
                        setNavigationActive(false);
                        navigation.goBack();
                      }
                    }
                  ]
                );
              }}
            >
              <Ionicons name="close-circle" size={24} color="#fff" />
            </TouchableOpacity>
          </>
        ) : (
          <>
            {/* Current Location Button */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: patientColors.primary }]}
              onPress={async () => {
                try {
                  const location = await Location.getCurrentPositionAsync({
                    accuracy: Location.Accuracy.Balanced,
                  });

                  if (location) {
                    setCurrentLocation({
                      latitude: location.coords.latitude,
                      longitude: location.coords.longitude,
                    });
                  }
                } catch (error) {
                  console.error('Error getting current location:', error);
                  Alert.alert('Error', 'Failed to get current location. Please try again.');
                }
              }}
            >
              <Ionicons name="locate" size={24} color="#fff" />
            </TouchableOpacity>

            {/* Save Current Location Button */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: patientColors.secondary }]}
              onPress={() => {
                if (currentLocation) {
                  setLocationToSave(currentLocation);
                  setShowSaveLocationModal(true);
                } else {
                  Alert.alert('Error', 'Unable to determine current location. Please try again.');
                }
              }}
            >
              <Ionicons name="bookmark" size={24} color="#fff" />
            </TouchableOpacity>

            {/* Saved Locations Button */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: patientColors.tertiary }]}
              onPress={() => setShowSavedLocations(!showSavedLocations)}
            >
              <Ionicons name="list" size={24} color="#fff" />
            </TouchableOpacity>

            {/* Route Tracking Button */}
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: isTrackingRoute ? '#e74c3c' : '#2ecc71' }
              ]}
              onPress={async () => {
                if (isTrackingRoute) {
                  stopRouteTracking();
                } else {
                  await startRouteTracking();
                }
              }}
            >
              <Ionicons
                name={isTrackingRoute ? 'stop-circle' : 'play-circle'}
                size={24}
                color="#fff"
              />
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* Navigation Instructions Modal */}
      <Modal
        visible={showNavigationModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowNavigationModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Navigation Guidance</Text>
              <TouchableOpacity
                onPress={() => setShowNavigationModal(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <View style={styles.navigationDetails}>
                <View style={styles.navigationDetailRow}>
                  <Text style={styles.navigationDetailLabel}>Destination:</Text>
                  <Text style={styles.navigationDetailValue}>{navigationDestination}</Text>
                </View>

                {guidanceData && guidanceData.supervisorName && (
                  <View style={styles.navigationDetailRow}>
                    <Text style={styles.navigationDetailLabel}>From:</Text>
                    <Text style={styles.navigationDetailValue}>{guidanceData.supervisorName}</Text>
                  </View>
                )}

                {guidanceData && guidanceData.routeType && (
                  <View style={styles.navigationDetailRow}>
                    <Text style={styles.navigationDetailLabel}>Mode:</Text>
                    <View style={styles.routeTypeContainer}>
                      <Ionicons
                        name={
                          guidanceData.routeType === 'walking' ? 'walk' :
                          guidanceData.routeType === 'driving' ? 'car' : 'bus'
                        }
                        size={20}
                        color={patientColors.primary}
                      />
                      <Text style={styles.routeTypeText}>
                        {guidanceData.routeType.charAt(0).toUpperCase() + guidanceData.routeType.slice(1)}
                      </Text>
                    </View>
                  </View>
                )}

                {navigationInstructions && (
                  <View style={styles.instructionsContainer}>
                    <Text style={styles.instructionsLabel}>Instructions:</Text>
                    <Text style={styles.instructionsText}>{navigationInstructions}</Text>
                  </View>
                )}
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowNavigationModal(false);
                }}
              >
                <Text style={styles.modalButtonText}>Close</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.completeButton]}
                onPress={handleCompleteNavigation}
              >
                <Text style={styles.modalButtonText}>Complete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Saved Locations Panel */}
      {showSavedLocations && (
        <View style={styles.savedItemsPanel}>
          <View style={styles.savedItemsHeader}>
            <Text style={styles.savedItemsTitle}>Saved Locations</Text>
            <TouchableOpacity onPress={() => setShowSavedLocations(false)}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.savedItemsList}>
            {savedLocations.length === 0 ? (
              <Text style={styles.emptyListText}>No saved locations yet</Text>
            ) : (
              savedLocations.map((location) => (
                <TouchableOpacity
                  key={location.id}
                  style={styles.savedItemCard}
                  onPress={() => {
                    setCurrentLocation({
                      latitude: location.latitude,
                      longitude: location.longitude,
                    });
                    setShowSavedLocations(false);
                  }}
                >
                  <View style={styles.savedItemInfo}>
                    <Text style={styles.savedItemName}>{location.name}</Text>
                    <Text style={styles.savedItemAddress}>
                      {`Lat: ${location.latitude.toFixed(6)}, Lng: ${location.longitude.toFixed(6)}`}
                    </Text>
                    {location.notes && (
                      <Text style={styles.savedItemNotes}>{location.notes}</Text>
                    )}
                  </View>
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => {
                      Alert.alert(
                        'Delete Location',
                        `Are you sure you want to delete "${location.name}"?`,
                        [
                          { text: 'Cancel', style: 'cancel' },
                          { text: 'Delete', onPress: () => handleDeleteLocation(location.id), style: 'destructive' }
                        ]
                      );
                    }}
                  >
                    <Ionicons name="trash" size={20} color="#e74c3c" />
                  </TouchableOpacity>
                </TouchableOpacity>
              ))
            )}
          </ScrollView>
        </View>
      )}

      {/* Save Location Modal */}
      <Modal
        visible={showSaveLocationModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSaveLocationModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Save Location</Text>

            <TextInput
              style={styles.input}
              placeholder="Location Name"
              value={locationName}
              onChangeText={setLocationName}
              autoFocus
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Notes (optional)"
              value={locationNotes}
              onChangeText={setLocationNotes}
              multiline
              numberOfLines={3}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowSaveLocationModal(false);
                  setLocationName('');
                  setLocationNotes('');
                  setLocationToSave(null);
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleSaveLocation}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Save Route Modal */}
      <Modal
        visible={showSaveRouteModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSaveRouteModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Save Route</Text>

            <TextInput
              style={styles.input}
              placeholder="Route Name"
              value={routeName}
              onChangeText={setRouteName}
              autoFocus
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Notes (optional)"
              value={routeNotes}
              onChangeText={setRouteNotes}
              multiline
              numberOfLines={3}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowSaveRouteModal(false);
                  setRouteName('');
                  setRouteNotes('');
                  setCurrentRouteCoordinates([]);
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleSaveRoute}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  mapContainer: {
    height: '60%',
    width: '100%',
    backgroundColor: '#e0e0e0',
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapControls: {
    position: 'absolute',
    right: 16,
    top: 16,
    backgroundColor: 'transparent',
    zIndex: 5,
  },
  mapControlButton: {
    backgroundColor: '#fff',
    borderRadius: 30,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  staticMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  navigationContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'flex-start',
    alignItems: 'center',
    padding: 16,
  },
  navigationOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 16,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '40%',
  },
  navigationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  instructionsContainer: {
    width: '100%',
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  mapPlaceholder: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  mapOverlayText: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    color: '#fff',
    padding: 8,
    borderRadius: 4,
    fontSize: 14,
  },
  mapErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  mapErrorText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  mapErrorButton: {
    backgroundColor: '#4285F4',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 4,
  },
  mapErrorButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  savedItemsPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '40%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 10,
  },
  savedItemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  savedItemsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  savedItemsList: {
    padding: 16,
  },
  emptyListText: {
    textAlign: 'center',
    color: '#999',
    fontSize: 16,
    padding: 20,
  },
  savedItemCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#eee',
  },
  savedItemInfo: {
    flex: 1,
  },
  savedItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  savedItemAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  savedItemNotes: {
    fontSize: 14,
    color: '#888',
    marginTop: 4,
    fontStyle: 'italic',
  },
  deleteButton: {
    padding: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  navigationDetails: {
    marginBottom: 16,
  },
  navigationDetailRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  navigationDetailLabel: {
    width: 100,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  navigationDetailValue: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  routeTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeTypeText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#333',
  },
  routeInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  routeInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeInfoText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  instructionsLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  completeButton: {
    backgroundColor: '#4CAF50',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f1f2f6',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  saveButton: {
    backgroundColor: '#3498db',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  // Google Maps style markers
  currentLocationMarker: {
    width: 22,
    height: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentLocationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4285F4',
    borderWidth: 1,
    borderColor: '#fff',
  },
  currentLocationHalo: {
    position: 'absolute',
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: 'rgba(66, 133, 244, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(66, 133, 244, 0.3)',
  },
  destinationMarkerContainer: {
    alignItems: 'center',
  },
  destinationMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#DB4437',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 5,
  },
  destinationMarkerTail: {
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#DB4437',
    marginTop: -2,
  },
});

export default MapScreen;
